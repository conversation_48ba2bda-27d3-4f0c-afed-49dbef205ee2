# 表盘复杂功能设置指南

## 概述

薪资奖励官应用支持 Apple Watch 表盘复杂功能，可以在表盘上直接显示当前收入、工作进度等信息。

## 功能特性

### 1. 支持的复杂功能类型
- **小型模块化** (Modular Small): 显示当前收入金额
- **大型模块化** (Modular Large): 显示收入、进度和状态
- **实用小型** (Utilitarian Small): 简洁的收入显示
- **圆形小型** (Circular Small): 进度百分比显示
- **图形圆形** (Graphic Circular): 带进度环的收入显示
- **图形矩形** (Graphic Rectangular): 完整信息展示
- **图形角落** (Graphic Corner): 收入和进度环
- **超大** (Extra Large): 大字体收入显示

### 2. 显示样式
- **收入显示**: 主要显示当前收入金额
- **进度显示**: 主要显示工作进度百分比
- **组合显示**: 同时显示收入和进度信息
- **简洁显示**: 显示最基本的状态信息

### 3. 颜色主题
- **蓝色主题**: 默认蓝色配色方案
- **绿色主题**: 绿色配色方案
- **橙色主题**: 橙色配色方案
- **紫色主题**: 紫色配色方案

### 4. 更新策略
- **自动更新**: 定期自动更新复杂功能数据
- **手动更新**: 仅在手动触发时更新
- **智能更新**: 根据时间和使用情况智能调整更新频率

## 设置步骤

### 1. 在应用中启用复杂功能
1. 打开薪资奖励官应用
2. 进入"设置" → "表盘功能" → "复杂功能设置"
3. 开启"启用复杂功能"开关
4. 选择喜欢的显示样式和颜色主题
5. 配置更新策略

### 2. 在表盘上添加复杂功能
1. 在 Apple Watch 上长按表盘进入编辑模式
2. 滑动到复杂功能编辑界面
3. 点击要添加复杂功能的位置
4. 滚动找到"薪资奖励官"
5. 选择合适的复杂功能样式
6. 点击"完成"保存设置

### 3. 点击复杂功能
- 在表盘上点击薪资奖励官复杂功能可直接打开应用
- 支持深度链接，可以导航到特定功能页面

## 数据更新机制

### 自动更新
- 工作时间内：每1分钟更新一次
- 非工作时间：每5分钟更新一次
- 深夜时间：每15分钟更新一次

### 电池优化
- 正常模式 (>50%): 正常更新频率
- 节能模式 (20-50%): 降低更新频率
- 极简模式 (<20%): 最低更新频率

### 手动更新
- 在应用中可以手动触发复杂功能更新
- 支持强制更新功能
- 提供测试工具验证功能

## 故障排除

### 复杂功能不显示数据
1. 检查应用中是否启用了复杂功能
2. 确认表盘上已正确添加复杂功能
3. 尝试手动更新数据
4. 重启 Apple Watch

### 数据更新不及时
1. 检查更新策略设置
2. 确认网络连接正常
3. 查看电池优化设置
4. 尝试强制更新

### 复杂功能样式异常
1. 检查选择的样式是否支持当前表盘
2. 尝试切换不同的颜色主题
3. 重新配置复杂功能设置
4. 重新添加表盘复杂功能

## 技术实现

### 核心组件
- `ComplicationController`: ClockKit 复杂功能控制器
- `WatchComplicationProvider`: 数据提供者
- `ComplicationStyleManager`: 样式管理器
- `ComplicationUpdateManager`: 更新管理器

### 数据流程
1. AppState 更新工资数据
2. WatchComplicationProvider 处理数据格式化
3. ComplicationController 创建复杂功能模板
4. CLKComplicationServer 更新表盘显示

### 深度链接
- URL Scheme: `salary-reward-app://`
- 复杂功能点击: `salary-reward-app://complication`
- 目标导航: `salary-reward-app://goal/{goalId}`

## 最佳实践

### 性能优化
- 合理设置更新频率
- 启用电池优化
- 避免频繁的强制更新

### 用户体验
- 选择合适的复杂功能样式
- 配置个性化的颜色主题
- 定期检查数据准确性

### 调试和测试
- 使用内置的测试工具
- 监控更新日志
- 验证不同场景下的表现