//
//  ContentView.swift
//  xzjlg-iwatch Watch App
//
//  Created by 宋铭 on 2025/6/6.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var appState = AppState()
    @ObservedObject private var onboardingManager = OnboardingManager.shared
    @State private var selectedTab: Int = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            MainWorkView()
                .tabItem {
                    Label("工作", systemImage: "briefcase.fill")
                }
                .tag(0)
            
            GoalTrackingView()
                .tabItem {
                    Label("目标", systemImage: "target")
                }
                .tag(1)
                
            SettingsView()
                .tabItem {
                    Label("设置", systemImage: "gear")
                }
                .tag(2)
        }
        .environmentObject(appState)
        .sheet(isPresented: $onboardingManager.showOnboarding) {
            OnboardingContainerView()
                .environmentObject(appState)
        }
        .onAppear {
            appState.startCalculation()
            appState.recordUserInteraction() // 记录应用启动时的交互
            
            // 设置Tab切换回调
            appState.switchToSettingsTab = {
                selectedTab = 2
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .onboardingCompleted)) { _ in
            // 引导完成后跳转到设置页面，让用户进行详细配置
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                selectedTab = 2 // 跳转到设置页面
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .NSExtensionHostWillEnterForeground)) { _ in
            appState.recordUserInteraction() // 记录应用进入前台时的交互
        }
        .sheet(item: $appState.celebratingGoal) { goal in
            GoalCompletionCelebrationView(completedGoal: goal)
        }
        .sheet(item: $appState.currentMilestone) { milestone in
            MilestoneNotificationView(milestone: milestone)
        }
        // 添加全局错误处理UI
        .background(
            ErrorAlertView()
        )
    }
}

/// 主工作视图
struct MainWorkView: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 小福狸角色
                    FoxCharacterView()
                        .environmentObject(appState)
                        .onTapGesture {
                            appState.recordUserInteraction()
                        }
                    
                    // 工资信息卡片
                    SalaryInfoCard()
                        .environmentObject(appState)
                        .onTapGesture {
                            appState.recordUserInteraction()
                        }
                    
                    // 快速操作按钮
                    QuickActionButtons()
                        .environmentObject(appState)
                }
                .padding()
            }
            .navigationTitle("薪资奖励官")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview {
    ContentView()
}
