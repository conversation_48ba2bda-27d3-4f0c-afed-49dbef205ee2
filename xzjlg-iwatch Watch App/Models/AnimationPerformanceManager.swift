//
//  AnimationPerformanceManager.swift
//  xzjlg-iwatch Watch App
//
//  Created by AI Assistant on 2025/7/26.
//

import Foundation
import SwiftUI
import WatchKit
import Combine

/// 动画性能管理器 - 负责优化动画性能和电量消耗
class AnimationPerformanceManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = AnimationPerformanceManager()
    
    // MARK: - Published Properties
    
    /// 当前动画质量级别
    @Published var animationQuality: AnimationQuality = .high
    
    /// 是否启用动画
    @Published var animationsEnabled: Bool = true
    
    /// 当前帧率
    @Published var currentFrameRate: Double = 30.0
    
    /// 动画复杂度级别
    @Published var complexityLevel: ComplexityLevel = .full
    
    // MARK: - Private Properties
    
    private var batteryLevel: Float = 1.0
    private var thermalState: ProcessInfo.ThermalState = .nominal
    private var isLowPowerModeEnabled: Bool = false
    private var lastInteractionTime: Date = Date()
    private var cancellables = Set<AnyCancellable>()
    
    // 性能监控
    private var frameTimeHistory: [TimeInterval] = []
    private var averageFrameTime: TimeInterval = 0
    private var droppedFrameCount: Int = 0
    
    // MARK: - Initialization
    
    private init() {
        setupPerformanceMonitoring()
        updateAnimationSettings()
    }
    
    // MARK: - Public Methods
    
    /// 更新电池状态
    func updateBatteryStatus(_ level: Float) {
        batteryLevel = level
        updateAnimationSettings()
    }
    
    /// 更新热状态
    func updateThermalState(_ state: ProcessInfo.ThermalState) {
        thermalState = state
        updateAnimationSettings()
    }
    
    /// 更新低电量模式状态
    func updateLowPowerMode(_ enabled: Bool) {
        isLowPowerModeEnabled = enabled
        updateAnimationSettings()
    }
    
    /// 记录用户交互
    func recordUserInteraction() {
        lastInteractionTime = Date()
        
        // 用户交互时临时提升动画质量
        if animationQuality == .minimal {
            temporarilyBoostAnimations()
        }
    }
    
    /// 获取推荐的动画持续时间
    func getRecommendedAnimationDuration(base: TimeInterval) -> TimeInterval {
        switch animationQuality {
        case .high:
            return base
        case .medium:
            return base * 0.8
        case .low:
            return base * 0.6
        case .minimal:
            return base * 0.4
        }
    }
    
    /// 获取推荐的动画延迟
    func getRecommendedAnimationDelay(base: TimeInterval) -> TimeInterval {
        switch animationQuality {
        case .high:
            return base
        case .medium:
            return base * 1.2
        case .low:
            return base * 1.5
        case .minimal:
            return base * 2.0
        }
    }
    
    /// 获取推荐的粒子数量
    func getRecommendedParticleCount(base: Int) -> Int {
        switch complexityLevel {
        case .full:
            return base
        case .reduced:
            return Int(Double(base) * 0.7)
        case .minimal:
            return Int(Double(base) * 0.4)
        case .disabled:
            return 0
        }
    }
    
    /// 获取推荐的更新间隔
    func getRecommendedUpdateInterval() -> TimeInterval {
        return 1.0 / currentFrameRate
    }
    
    /// 是否应该显示特效
    func shouldShowEffects() -> Bool {
        return complexityLevel != .disabled && animationsEnabled
    }
    
    /// 是否应该使用简化动画
    func shouldUseSimplifiedAnimations() -> Bool {
        return animationQuality == .minimal || complexityLevel == .minimal
    }
    
    /// 获取动画性能摘要
    func getPerformanceSummary() -> AnimationPerformanceSummary {
        return AnimationPerformanceSummary(
            animationQuality: animationQuality,
            complexityLevel: complexityLevel,
            currentFrameRate: currentFrameRate,
            averageFrameTime: averageFrameTime,
            droppedFrameCount: droppedFrameCount,
            batteryLevel: batteryLevel,
            thermalState: thermalState,
            isLowPowerModeEnabled: isLowPowerModeEnabled,
            recommendations: generateRecommendations()
        )
    }
    
    /// 手动设置动画质量
    func setAnimationQuality(_ quality: AnimationQuality) {
        animationQuality = quality
        updateComplexityLevel()
        notifyQualityChange()
    }
    
    /// 重置性能统计
    func resetPerformanceStats() {
        frameTimeHistory.removeAll()
        averageFrameTime = 0
        droppedFrameCount = 0
    }
}

// MARK: - Private Methods
private extension AnimationPerformanceManager {
    
    /// 设置性能监控
    func setupPerformanceMonitoring() {
        // 监听电池状态变化
        NotificationCenter.default.publisher(for: .NSProcessInfoPowerStateDidChange)
            .sink { [weak self] _ in
                self?.handlePowerStateChange()
            }
            .store(in: &cancellables)
        
        // 监听热状态变化
        NotificationCenter.default.publisher(for: ProcessInfo.thermalStateDidChangeNotification)
            .sink { [weak self] _ in
                self?.handleThermalStateChange()
            }
            .store(in: &cancellables)
        
        // 定期检查性能
        Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            self?.performPerformanceCheck()
        }
    }
    
    /// 更新动画设置
    func updateAnimationSettings() {
        // 根据电池电量调整
        let batteryBasedQuality = getBatteryBasedQuality()
        
        // 根据热状态调整
        let thermalBasedQuality = getThermalBasedQuality()
        
        // 根据低电量模式调整
        let powerModeBasedQuality = getPowerModeBasedQuality()
        
        // 选择最保守的设置
        let newQuality = min(batteryBasedQuality, thermalBasedQuality, powerModeBasedQuality)
        
        if newQuality != animationQuality {
            animationQuality = newQuality
            updateComplexityLevel()
            updateFrameRate()
            notifyQualityChange()
        }
    }
    
    /// 根据电池电量获取质量级别
    func getBatteryBasedQuality() -> AnimationQuality {
        switch batteryLevel {
        case 0.5...1.0:
            return .high
        case 0.2..<0.5:
            return .medium
        case 0.1..<0.2:
            return .low
        default:
            return .minimal
        }
    }
    
    /// 根据热状态获取质量级别
    func getThermalBasedQuality() -> AnimationQuality {
        switch thermalState {
        case .nominal:
            return .high
        case .fair:
            return .medium
        case .serious:
            return .low
        case .critical:
            return .minimal
        @unknown default:
            return .medium
        }
    }
    
    /// 根据低电量模式获取质量级别
    func getPowerModeBasedQuality() -> AnimationQuality {
        return isLowPowerModeEnabled ? .minimal : .high
    }
    
    /// 更新复杂度级别
    func updateComplexityLevel() {
        switch animationQuality {
        case .high:
            complexityLevel = .full
        case .medium:
            complexityLevel = .reduced
        case .low:
            complexityLevel = .minimal
        case .minimal:
            complexityLevel = .disabled
        }
    }
    
    /// 更新帧率
    func updateFrameRate() {
        switch animationQuality {
        case .high:
            currentFrameRate = 30.0
        case .medium:
            currentFrameRate = 24.0
        case .low:
            currentFrameRate = 20.0
        case .minimal:
            currentFrameRate = 15.0
        }
    }
    
    /// 临时提升动画质量
    func temporarilyBoostAnimations() {
        let originalQuality = animationQuality
        animationQuality = .medium
        updateComplexityLevel()
        updateFrameRate()
        
        // 3秒后恢复原始质量
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) { [weak self] in
            self?.animationQuality = originalQuality
            self?.updateComplexityLevel()
            self?.updateFrameRate()
        }
    }
    
    /// 处理电源状态变化
    func handlePowerStateChange() {
        let device = WKInterfaceDevice.current()
        updateBatteryStatus(device.batteryLevel)
        updateLowPowerMode(ProcessInfo.processInfo.isLowPowerModeEnabled)
    }
    
    /// 处理热状态变化
    func handleThermalStateChange() {
        updateThermalState(ProcessInfo.processInfo.thermalState)
    }
    
    /// 执行性能检查
    func performPerformanceCheck() {
        // 计算平均帧时间
        if !frameTimeHistory.isEmpty {
            averageFrameTime = frameTimeHistory.reduce(0, +) / Double(frameTimeHistory.count)
            
            // 如果平均帧时间过长，降低质量
            if averageFrameTime > 1.0 / 20.0 && animationQuality != .minimal {
                let lowerQuality = AnimationQuality(rawValue: animationQuality.rawValue - 1) ?? .minimal
                setAnimationQuality(lowerQuality)
            }
        }
        
        // 检查是否长时间无交互
        let timeSinceLastInteraction = Date().timeIntervalSince(lastInteractionTime)
        if timeSinceLastInteraction > 30.0 && animationQuality != .minimal {
            // 长时间无交互时降低动画质量
            setAnimationQuality(.low)
        }
    }
    
    /// 生成性能建议
    func generateRecommendations() -> [String] {
        var recommendations: [String] = []
        
        if batteryLevel < 0.2 {
            recommendations.append("电量较低，建议降低动画质量")
        }
        
        if thermalState == .serious || thermalState == .critical {
            recommendations.append("设备温度较高，建议减少动画效果")
        }
        
        if averageFrameTime > 1.0 / 20.0 {
            recommendations.append("动画性能不佳，建议简化特效")
        }
        
        if droppedFrameCount > 10 {
            recommendations.append("检测到掉帧，建议优化动画设置")
        }
        
        if isLowPowerModeEnabled {
            recommendations.append("低电量模式已启用，动画已自动优化")
        }
        
        return recommendations
    }
    
    /// 通知质量变化
    func notifyQualityChange() {
        NotificationCenter.default.post(
            name: .animationQualityChanged,
            object: getPerformanceSummary()
        )
    }
}

// MARK: - Supporting Types

/// 动画质量级别
enum AnimationQuality: Int, CaseIterable, Comparable {
    case high = 3      // 高质量：全特效
    case medium = 2    // 中等质量：减少特效
    case low = 1       // 低质量：简化动画
    case minimal = 0   // 最低质量：基础动画
    
    static func < (lhs: AnimationQuality, rhs: AnimationQuality) -> Bool {
        return lhs.rawValue < rhs.rawValue
    }
    
    var displayName: String {
        switch self {
        case .high:
            return "高质量"
        case .medium:
            return "中等质量"
        case .low:
            return "低质量"
        case .minimal:
            return "最低质量"
        }
    }
    
    var description: String {
        switch self {
        case .high:
            return "全特效，30fps，最佳视觉体验"
        case .medium:
            return "减少特效，24fps，平衡性能和视觉"
        case .low:
            return "简化动画，20fps，节省电量"
        case .minimal:
            return "基础动画，15fps，最大化电池续航"
        }
    }
}

/// 复杂度级别
enum ComplexityLevel: CaseIterable {
    case full      // 完整复杂度
    case reduced   // 减少复杂度
    case minimal   // 最小复杂度
    case disabled  // 禁用特效
    
    var displayName: String {
        switch self {
        case .full:
            return "完整"
        case .reduced:
            return "减少"
        case .minimal:
            return "最小"
        case .disabled:
            return "禁用"
        }
    }
}

/// 动画性能摘要
struct AnimationPerformanceSummary {
    let animationQuality: AnimationQuality
    let complexityLevel: ComplexityLevel
    let currentFrameRate: Double
    let averageFrameTime: TimeInterval
    let droppedFrameCount: Int
    let batteryLevel: Float
    let thermalState: ProcessInfo.ThermalState
    let isLowPowerModeEnabled: Bool
    let recommendations: [String]
    
    var statusDescription: String {
        return "质量: \(animationQuality.displayName), 帧率: \(Int(currentFrameRate))fps, 电量: \(Int(batteryLevel * 100))%"
    }
}

// MARK: - Notification Extensions
extension Notification.Name {
    static let animationQualityChanged = Notification.Name("animationQualityChanged")
}