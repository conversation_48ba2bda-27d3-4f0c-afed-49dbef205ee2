//
//  AppState.swift
//  xzjlg-iwatch Watch App
//
//  Created by Kiro on 2025/7/25.
//

import Foundation
import Combine
import WatchKit
import UserNotifications  

/// 全局状态管理器，负责协调应用的所有状态和业务逻辑
class AppState: ObservableObject {
    // MARK: - Published Properties
    
    /// 当日收入金额
    @Published var dailyEarnings: Double = 0.0
    
    /// 当前角色状态
    @Published var currentCharacterState: CharacterState = .normal
    
    /// 当前显示的消息
    @Published var currentMessage: String = ""
    
    /// 当前工作场景
    @Published var currentWorkScenario: WorkScenario = .resting
    
    /// 计算状态
    @Published var calculationState: CalculationState = .stopped
    
    /// 最后发生的错误
    @Published var lastError: SalaryCalculatorError?
    
    /// 用户设置
    @Published var userSettings: UserSettings = .default
    
    /// 目标追踪器
    @Published var goalTracker: GoalTracker = GoalTracker()
    
    /// 目标列表（便于访问）
    var goals: [Goal] {
        return goalTracker.goals
    }
    
    /// 手动操作标志 - 防止自动分配干扰手动操作
    @Published var isManualOperating: Bool = false
    
    /// 当前进度百分比
    @Published var currentProgress: Double = 0.0
    
    /// 是否显示角色动画
    @Published var showCharacterAnimation: Bool = true
    
    /// 电池优化级别
    @Published var batteryOptimizationLevel: BatteryOptimizationLevel = .normal
    
    /// 电池优化管理器
    @Published var batteryOptimizationManager: BatteryOptimizationManager = BatteryOptimizationManager()
    
    /// 进度里程碑检测器
    @Published var milestoneDetector: ProgressMilestoneDetector = ProgressMilestoneDetector()
    
    /// 智能场景检测器
    @Published var scenarioDetector: SmartScenarioDetector = SmartScenarioDetector()
    
    /// 智能提醒管理器
    @Published var reminderManager: SmartReminderManager = SmartReminderManager()
    
    /// 当前显示的里程碑
    @Published var currentMilestone: ProgressMilestone?
    
    /// 当前庆祝的目标
    @Published var celebratingGoal: Goal?
    
    /// 是否正在计算工资（兼容性属性）
    var isCalculating: Bool {
        return calculationState == .calculating
    }
    
    // MARK: - Private Properties
    
    private var salaryCalculator: SalaryCalculator
    private var calculationTimer: Timer?
    private var messageUpdateTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    
    // 通知相关
    @Published var notificationManager: NotificationManager = NotificationManager()
    
    // 智能提醒系统
    private var workDurationTimer: Timer?
    private var lastWorkReminder: Date = Date()
    private var lastPostureReminder: Date = Date()
    private var reachedThresholds: Set<Int> = [] // 改为整数百分比
    
    // 消息管理
    private let personalizedMessages = PersonalizedMessages()
    private var lastMessageTime: Date = Date()
    private var isFirstWorkSession: Bool = true
    private var workStartTime: Date?
    
    // 数据持久化
    private let persistenceManager = DataPersistenceManager.shared
    
    // 复杂功能更新管理器
    private let complicationUpdateManager = ComplicationUpdateManager.shared
    
    // 用户体验管理器（延迟初始化以避免循环依赖）
    private var userExperienceManager: UserExperienceManager?
    private var onboardingManager: OnboardingManager?
    private var testingManager: TestingManager?
    
    // Tab切换回调
    var switchToSettingsTab: (() -> Void)?
    
    // MARK: - Initialization
    
    init() {
        self.salaryCalculator = SalaryCalculator()
        
        // 加载持久化数据
        loadPersistedData()
        
        // 设置GoalTracker回调
        setupGoalTracker()
        
        // 监听GoalTracker的变化并转发到AppState
        setupGoalTrackerBinding()
        
        // 设置初始状态
        setupInitialState()
        
        // 绑定SalaryCalculator的状态
        bindSalaryCalculator()
        
        // 自动开始计算
        startCalculation()
        
        // 设置通知
        setupNotifications()
        
        // 电池优化
        setupBatteryOptimization()
        
        // 设置复杂功能更新
        setupComplicationUpdates()
        
        // 设置智能场景检测通知监听
        setupScenarioDetectionObservers()
        
        // 设置用户增强的智能场景检测
        setupEnhancedScenarioDetectionObservers()
        
        // 设置全局错误处理
        setupGlobalErrorHandling()
        
        // 延迟设置用户体验管理器（避免初始化时的循环依赖）
        DispatchQueue.main.async { [weak self] in
            self?.setupUserExperience()
        }
    }
    
    deinit {
        stopCalculation()
        cancellables.removeAll()
    }
}

// MARK: - Public Methods
extension AppState {
    /// 开始工资计算
    func startCalculation() {
        guard calculationState != .calculating else { return }
        
        do {
            try salaryCalculator.updateSettings(userSettings)
            try salaryCalculator.startCalculation()
            
            // 更新角色状态和消息
            updateCharacterStateAndMessage()
            
            // 开始消息更新定时器
            startMessageUpdateTimer()
        } catch {
            handleCalculationError(error as? SalaryCalculatorError ?? .calculationFailed(error.localizedDescription))
        }
    }
    
    /// 停止工资计算
    func stopCalculation() {
        salaryCalculator.stopCalculation()
        stopMessageUpdateTimer()
    }
    
    /// 暂停工资计算
    func pauseCalculation() {
        salaryCalculator.pauseCalculation()
        stopMessageUpdateTimer()
    }
    
    /// 恢复工资计算
    func resumeCalculation() {
        do {
            try salaryCalculator.resumeCalculation()
            startMessageUpdateTimer()
        } catch {
            handleCalculationError(error as? SalaryCalculatorError ?? .calculationFailed(error.localizedDescription))
        }
    }
    
    /// 更新用户设置
    func updateSettings(_ newSettings: UserSettings) {
        userSettings = newSettings
        
        do {
            try salaryCalculator.updateSettings(newSettings)
            saveSettings()
            
            // 重新计算当前状态
            updateCharacterStateAndMessage()
        } catch {
            handleCalculationError(error as? SalaryCalculatorError ?? .calculationFailed(error.localizedDescription))
        }
    }
    
    /// 添加新目标
    func addGoal(name: String, amount: Double) {
        goalTracker.addGoal(name: name, targetAmount: amount)
    }
    
    /// 删除目标
    func removeGoal(at index: Int) {
        let currentGoals = goalTracker.goals
        print("AppState: 删除目标索引 \(index), 当前目标数: \(currentGoals.count)")
        guard index >= 0 && index < currentGoals.count else {
            print("AppState: 索引越界，取消删除")
            return
        }
        
        let goalName = currentGoals[index].name
        print("AppState: 准备删除目标: \(goalName)")
        
        // 设置手动操作标志，防止自动分配干扰
        isManualOperating = true
        
        // 执行删除
        goalTracker.removeGoal(at: index)
        
        print("AppState: 目标删除完成, 当前目标数: \(goalTracker.goals.count)")
        
        // 延迟清除手动操作标志
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.isManualOperating = false
            print("AppState: 手动操作标志已清除")
        }
    }
    
    /// 重置目标进度
    func resetGoal(at index: Int) {
        let currentGoals = goalTracker.goals
        print("AppState: 重置目标索引 \(index), 当前目标数: \(currentGoals.count)")
        guard index >= 0 && index < currentGoals.count else {
            print("AppState: 索引越界，取消重置")
            return
        }
        
        let goalName = currentGoals[index].name
        let goalId = currentGoals[index].id
        print("AppState: 准备重置目标: \(goalName)")
        
        // 设置手动操作标志，防止自动分配干扰
        isManualOperating = true
        
        // 重置里程碑
        milestoneDetector.resetMilestones(for: goalId)
        
        // 执行重置
        goalTracker.resetGoal(at: index)
        
        print("AppState: 目标重置完成")
        
        // 延迟清除手动操作标志
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            self?.isManualOperating = false
            print("AppState: 手动操作标志已清除")
        }
    }
    
    /// 获取目标列表
    func getGoals() -> [Goal] {
        return goalTracker.getAllGoals()
    }
    
    /// 获取每日目标工资
    func getDailyTargetSalary() -> Double {
        return salaryCalculator.getDailyTargetSalary()
    }
    
    /// 获取详细统计信息
    func getDetailedStats() -> SalaryStatistics {
        return salaryCalculator.getDetailedStats()
    }
    
    /// 手动触发状态更新
    func refreshState() {
        do {
            try salaryCalculator.forceCalculation()
            updateCharacterStateAndMessage()
        } catch {
            handleCalculationError(error as? SalaryCalculatorError ?? .calculationFailed(error.localizedDescription))
        }
    }
    
    /// 配置电池优化
    func configureForBatteryOptimization() {
        let device = WKInterfaceDevice.current()
        let batteryLevel = device.batteryLevel
        
        updateBatteryOptimization(batteryLevel: batteryLevel)
    }
    
    /// 记录用户交互，用于电池优化中的空闲检测
    func recordUserInteraction() {
        batteryOptimizationManager.recordUserInteraction()
    }
    
    /// 手动触发电池状态更新
    func updateBatteryStatus() {
        batteryOptimizationManager.updateBatteryStatus()
    }
    
    /// 获取当前是否应该显示动画
    func shouldShowAnimation() -> Bool {
        return batteryOptimizationManager.shouldShowAnimation()
    }
    
    /// 获取电池优化状态摘要
    func getBatteryOptimizationStatus() -> BatteryStatusSummary {
        return batteryOptimizationManager.getBatteryStatusSummary()
    }
    
    /// 测试电池优化系统（用于调试）
    func testBatteryOptimization() {
        print("=== 电池优化系统测试 ===")
        let status = getBatteryOptimizationStatus()
        print("当前状态: \(status.statusDescription)")
        print("建议: \(status.recommendations.joined(separator: ", "))")
        print("计算间隔: \(status.calculationInterval)秒")
        print("动画帧率: \(status.animationFrameRate)fps")
        print("应显示动画: \(status.shouldShowAnimation)")
        print("========================")
    }
    
    /// 更新用户设置
    func updateUserSettings(_ newSettings: UserSettings) {
        userSettings = newSettings
        saveSettings()
        
        // 同步通知设置到通知管理器
        notificationManager.updateSettings(newSettings.notificationSettings)
        
        // 如果通知被禁用，清理待发送的通知
        if !newSettings.notificationSettings.isEnabled {
            clearPendingNotifications()
        }
    }
    
    /// 手动发送测试通知
    func sendTestNotification() {
        notificationManager.sendSalaryThresholdNotification(
            amount: dailyEarnings,
            percentage: Int(currentProgress * 100),
            dailyTarget: userSettings.dailyBaseSalary
        )
    }
}

// MARK: - Private Setup Methods
private extension AppState {
    func setupInitialState() {
        // 检查是否需要重置每日数据
        checkAndResetDailyData()
        
        // 更新初始状态
        updateCharacterStateAndMessage()
    }
    
    func updateCharacterStateAndMessage() {
        // 确定新的角色状态
        let newState = determineCharacterState()
        
        // 更新状态
        currentCharacterState = newState
        currentWorkScenario = salaryCalculator.getCurrentScenario()
        currentProgress = salaryCalculator.getCurrentProgress()
        
        // 更新消息
        updatePersonalizedMessage()
        
        // 分配收入到目标
        distributeEarningsToGoals()
    }
    
    func determineCharacterState() -> CharacterState {
        let progress = salaryCalculator.getCurrentProgress()
        let scenario = salaryCalculator.getCurrentScenario()
        
        // 检查是否有目标完成
        if goals.contains(where: { $0.isCompleted }) {
            return .celebration
        }
        
        // 根据进度确定状态
        if progress >= 1.0 {
            return .celebration
        } else if progress >= 0.25 {
            return .encouraging
        }
        
        // 根据工作场景确定状态
        return CharacterState.fromWorkScenario(scenario)
    }
    
    func updatePersonalizedMessage() {
        let scenario = currentWorkScenario
        let progress = currentProgress
        let hasCompletedGoals = goals.contains(where: { $0.isCompleted })
        let now = Date()
        
        // 智能消息选择逻辑
        var newMessage: String
        
        // 检查是否是第一次工作
        if isFirstWorkSession && scenario != .resting {
            newMessage = personalizedMessages.getFirstWorkMessage()
            isFirstWorkSession = false
            workStartTime = now
        }
        // 检查是否长时间工作（超过4小时）
        else if let startTime = workStartTime,
                scenario != .resting,
                now.timeIntervalSince(startTime) > 4 * 3600 {
            newMessage = personalizedMessages.getLongWorkMessage()
        }
        // 检查是否需要欢迎消息（应用启动或长时间未更新消息）
        else if now.timeIntervalSince(lastMessageTime) > 1800 { // 30分钟
            newMessage = personalizedMessages.getWelcomeMessage()
        }
        // 常规消息
        else {
            newMessage = personalizedMessages.getMessage(
                for: scenario,
                progress: progress,
                hasCompletedGoals: hasCompletedGoals
            )
        }
        
        // 避免重复消息
        if newMessage != currentMessage {
            currentMessage = newMessage
            lastMessageTime = now
        }
        
        // 重置工作时间（如果进入休息状态）
        if scenario == .resting && workStartTime != nil {
            workStartTime = nil
        }
    }
    
    func distributeEarningsToGoals() {
        // 手动操作期间跳过自动分配，防止干扰用户操作
        guard !isManualOperating else {
            print("AppState: 手动操作中，跳过自动收入分配")
            return
        }
        
        goalTracker.distributeEarnings(dailyEarnings)
    }
    

    
    func bindSalaryCalculator() {
        // 绑定收入更新
        salaryCalculator.onEarningsUpdated = { [weak self] earnings in
            DispatchQueue.main.async {
                self?.handleEarningsUpdated(earnings)
            }
        }
        
        // 绑定阈值达成
        salaryCalculator.onThresholdReached = { [weak self] amount, percentage in
            DispatchQueue.main.async {
                self?.handleThresholdReached(amount: amount, percentage: Int(percentage))
            }
        }
        
        // 绑定场景变化
        salaryCalculator.onScenarioChanged = { [weak self] scenario in
            DispatchQueue.main.async {
                self?.handleScenarioChanged(scenario)
            }
        }
        
        // 绑定错误处理
        salaryCalculator.onErrorOccurred = { [weak self] error in
            DispatchQueue.main.async {
                self?.handleCalculationError(error)
            }
        }
        
        // 绑定状态变化
        salaryCalculator.onCalculationStateChanged = { [weak self] state in
            DispatchQueue.main.async {
                self?.handleCalculationStateChanged(state)
            }
        }
    }
    
    func setupNotifications() {
        // 同步用户设置到通知管理器
        notificationManager.updateSettings(userSettings.notificationSettings)
    }
    
    private func clearPendingNotifications() {
        // 清理系统待发送的通知
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
    }
    
    func setupBatteryOptimization() {
        // 设置电池优化管理器回调
        batteryOptimizationManager.onOptimizationLevelChanged = { [weak self] level in
            self?.handleBatteryOptimizationLevelChanged(level)
        }
        
        batteryOptimizationManager.onTimeOptimizationChanged = { [weak self] timeOptimization in
            self?.handleTimeOptimizationChanged(timeOptimization)
        }
        
        batteryOptimizationManager.onIdleStateChanged = { [weak self] isIdle in
            self?.handleIdleStateChanged(isIdle)
        }
        
        // 监听电池优化管理器状态变化
        batteryOptimizationManager.$currentOptimizationLevel
            .receive(on: DispatchQueue.main)
            .sink { [weak self] level in
                self?.batteryOptimizationLevel = level
            }
            .store(in: &cancellables)
            
        // 启动电池优化监测
        batteryOptimizationManager.startMonitoring()
    }
    
    func setupComplicationUpdates() {
        // 监听应用状态变化，同步到复杂功能更新管理器
        $calculationState
            .sink { [weak self] state in
                self?.complicationUpdateManager.handleAppStateChange(self ?? AppState())
            }
            .store(in: &cancellables)
        
        // 监听电池优化级别变化
        $batteryOptimizationLevel
            .sink { [weak self] level in
                self?.complicationUpdateManager.handleBatteryOptimization(level)
            }
            .store(in: &cancellables)
    }
}

// MARK: - Event Handlers
private extension AppState {
    func handleEarningsUpdated(_ earnings: Double) {
        dailyEarnings = earnings
        saveDailyEarnings()
        
        // 检测工资阈值
        checkSalaryThresholds(earnings)
        
        updateCharacterStateAndMessage()
        
        // 更新表盘复杂功能数据
        updateComplicationData()
    }
    
    func checkSalaryThresholds(_ currentEarnings: Double) {
        let dailyTarget = userSettings.dailyBaseSalary
        guard dailyTarget > 0 else { return }
        
        let currentProgress = min(currentEarnings / dailyTarget, 1.0)
        let currentPercentage = Int(currentProgress * 100)
        
        // 检查标准阈值: 25%, 50%, 75%, 100%
        let thresholds = [25, 50, 75, 100]
        
        for threshold in thresholds {
            if currentPercentage >= threshold && !reachedThresholds.contains(threshold) {
                reachedThresholds.insert(threshold)
                
                // 发送工资阈值通知
                notificationManager.sendSalaryThresholdNotification(
                    amount: currentEarnings,
                    percentage: threshold,
                    dailyTarget: dailyTarget
                )
            }
        }
    }
    
    func handleThresholdReached(amount: Double, percentage: Int) {
        // 避免重复通知
        guard !reachedThresholds.contains(percentage) else { return }
        reachedThresholds.insert(percentage)
        
        // 发送通知
        notificationManager.sendSalaryThresholdNotification(
            amount: amount,
            percentage: percentage,
            dailyTarget: userSettings.dailyBaseSalary
        )
        
        // 更新角色状态
        updateCharacterStateAndMessage()
    }
    
    func handleScenarioChanged(_ scenario: WorkScenario) {
        currentWorkScenario = scenario
        
        // 发送工作场景相关的提醒通知
        sendScenarioNotification(scenario)
        
        updateCharacterStateAndMessage()
    }
    
    func sendScenarioNotification(_ scenario: WorkScenario) {
        let message: String
        
        switch scenario {
        case .normalWork:
            message = "开始工作了！今天也要加油哦 💪"
        case .nightOvertime:
            message = "夜间加班辛苦了，记得休息哦 🌙"
        case .weekendWithPay:
            message = "周末工作真拼！记得劳逸结合 ⚡"
        case .weekendVoluntary:
            message = "自愿加班，好敬业！注意身体健康 ❤️"
        case .lunchBreak:
            message = "午休时间到！记得放松一下哦 🍽️"
        case .resting:
            return // 休息时间不发送通知
        case .deepWork:
            message = "进入专注模式！深度工作开始 🧠"
        case .meeting:
            message = "会议开始了，保持专注哦 📋"
        case .commuting:
            message = "通勤路上，注意安全 🚗"
        }
        
        notificationManager.sendWorkReminder(message: message)
    }
    
    func handleCalculationError(_ error: SalaryCalculatorError) {
        lastError = error
        
        // 使用全局错误处理器处理错误
        GlobalErrorHandler.shared.handleError(error)
    }
    
    func handleCalculationStateChanged(_ state: CalculationState) {
        calculationState = state
        
        // 根据状态调整UI或其他行为
        switch state {
        case .stopped, .error:
            stopMessageUpdateTimer()
        case .calculating:
            startMessageUpdateTimer()
        case .paused:
            // 保持当前消息显示
            break
        }
    }
}

// MARK: - Timer Management
private extension AppState {
    func startMessageUpdateTimer() {
        messageUpdateTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            self?.updatePersonalizedMessage()
        }
    }
    
    func stopMessageUpdateTimer() {
        messageUpdateTimer?.invalidate()
        messageUpdateTimer = nil
    }
}

// MARK: - Battery Optimization
private extension AppState {
    func updateBatteryOptimization(batteryLevel: Float) {
        let newLevel: BatteryOptimizationLevel
        
        switch batteryLevel {
        case 0.0..<0.2:
            newLevel = .minimal
        case 0.2..<0.5:
            newLevel = .reduced
        default:
            newLevel = .normal
        }
        
        if newLevel != batteryOptimizationLevel {
            batteryOptimizationLevel = newLevel
            applyBatteryOptimization(newLevel)
        }
    }
    
    func applyBatteryOptimization(_ level: BatteryOptimizationLevel) {
        switch level {
        case .minimal:
            // 最小化动画和更新频率
            showCharacterAnimation = false
            salaryCalculator.adjustCalculationFrequency(batteryLevel: 0.1)
            stopMessageUpdateTimer()
            
        case .reduced:
            // 减少动画和更新频率
            showCharacterAnimation = true
            salaryCalculator.adjustCalculationFrequency(batteryLevel: 0.3)
            stopMessageUpdateTimer()
            startMessageUpdateTimer() // 重新启动但频率会调整
            
        case .normal:
            // 正常模式
            showCharacterAnimation = true
            salaryCalculator.adjustCalculationFrequency(batteryLevel: 1.0)
            stopMessageUpdateTimer()
            startMessageUpdateTimer()
        }
    }
    
    /// 处理电池优化级别变化
    func handleBatteryOptimizationLevelChanged(_ level: BatteryOptimizationLevel) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 更新动画显示状态
            self.showCharacterAnimation = self.batteryOptimizationManager.shouldShowAnimation()
            
            // 调整SalaryCalculator的计算频率
            let interval = self.batteryOptimizationManager.getCalculationInterval()
            self.salaryCalculator.adjustFrequencyForBatteryOptimization(interval: interval)
            
            // 重新配置定时器
            self.stopMessageUpdateTimer()
            if level != .minimal {
                self.startMessageUpdateTimer()
            }
            
            // 调整通知管理器的电池优化设置
            self.notificationManager.settings.batteryOptimizationEnabled = (level != .normal)
            
            // 发送电池优化变化通知
            NotificationCenter.default.post(
                name: .batteryOptimizationChanged,
                object: self.batteryOptimizationManager.getBatteryStatusSummary()
            )
            
            print("电池优化级别已应用: \(level), 动画显示: \(self.showCharacterAnimation), 计算间隔: \(interval)秒")
        }
    }
    
    /// 处理时间段优化变化
    func handleTimeOptimizationChanged(_ timeOptimization: TimeBasedOptimization) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 根据时间段调整SalaryCalculator
            self.salaryCalculator.adjustForTimeOfDay()
            
            // 更新角色状态和消息以适应时间段
            self.updateCharacterStateAndMessage()
            
            print("时间段优化已变更: \(timeOptimization.displayName)")
        }
    }
    
    /// 处理空闲状态变化
    func handleIdleStateChanged(_ isIdle: Bool) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            if isIdle {
                // 进入空闲模式：暂停非关键操作
                self.pauseNonEssentialOperations()
            } else {
                // 退出空闲模式：恢复操作
                self.resumeNonEssentialOperations()
            }
            
            // 更新动画状态
            self.showCharacterAnimation = self.batteryOptimizationManager.shouldShowAnimation()
            
            // 如果需要暂停动画，发送通知
            if self.batteryOptimizationManager.shouldPauseAnimations() {
                NotificationCenter.default.post(name: .pauseAnimations, object: nil)
            } else {
                NotificationCenter.default.post(name: .resumeAnimations, object: nil)
            }
            
            print("空闲状态变更: \(isIdle ? "进入空闲" : "退出空闲")")
        }
    }
    
    /// 暂停非关键操作
    func pauseNonEssentialOperations() {
        // 暂停消息更新
        stopMessageUpdateTimer()
        
        // 降低SalaryCalculator更新频率
        let interval = batteryOptimizationManager.getCalculationInterval()
        salaryCalculator.adjustFrequencyForBatteryOptimization(interval: interval)
    }
    
    /// 恢复非关键操作
    func resumeNonEssentialOperations() {
        // 根据当前优化级别决定是否恢复消息更新
        if batteryOptimizationManager.currentOptimizationLevel != .minimal {
            startMessageUpdateTimer()
        }
        
        // 恢复正常的SalaryCalculator更新频率
        let interval = batteryOptimizationManager.getCalculationInterval()
        salaryCalculator.adjustFrequencyForBatteryOptimization(interval: interval)
    }
}

// MARK: - Data Persistence
private extension AppState {
    func loadPersistedData() {
        loadSettings()
        loadDailyEarnings()
    }
    
    func setupGoalTracker() {
        // 只保留重要的事件回调，移除会触发循环的onGoalsChanged
        goalTracker.onGoalCompleted = { [weak self] goal in
            self?.handleGoalCompleted(goal)
        }
        
        goalTracker.onMilestoneReached = { [weak self] goal, percentage in
            self?.handleMilestoneReached(goal: goal, percentage: percentage)
        }
        
        print("AppState: GoalTracker回调已设置（简化版）")
    }
    
    /// 设置GoalTracker的数据绑定，确保UI能响应数据变化
    func setupGoalTrackerBinding() {
        // 使用标准的@Published机制，不需要手动绑定
        print("AppState: 使用标准@Published机制，无需手动绑定")
    }
    
    #if DEBUG
    /// 测试数据绑定是否工作（调试用）
    func testDataBinding() {
        print("=== 数据绑定测试 ===")
        print("AppState.goals 计算属性: \(goals.count) 个目标")
        print("GoalTracker.goals 直接访问: \(goalTracker.goals.count) 个目标")
        
        if goals.count == goalTracker.goals.count {
            print("✅ 数据一致性正常")
        } else {
            print("❌ 数据不一致！")
        }
        
        // 强制触发UI更新
        objectWillChange.send()
        print("已手动触发 objectWillChange")
        print("==================")
    }
    #endif
    
    func handleGoalCompleted(_ goal: Goal) {
        // 触发目标完成触觉反馈
        userExperienceManager?.triggerHapticFeedback(.goalComplete)
        
        // 发送目标完成通知
        notificationManager.sendGoalCompletionNotification(
            goalName: goal.name, 
            amount: goal.targetAmount
        )
        
        // 触发庆祝状态
        if currentCharacterState != .celebration {
            currentCharacterState = .celebration
            currentMessage = personalizedMessages.getMessage(
                for: currentWorkScenario,
                progress: currentProgress,
                hasCompletedGoals: true
            )
        }
        
        // 显示庆祝界面
        celebratingGoal = goal
    }
    
    // handleGoalsChanged方法已移除，使用@Published自动更新
    
    func handleMilestoneReached(goal: Goal, percentage: Int) {
        // 触发里程碑触觉反馈
        userExperienceManager?.triggerHapticFeedback(.milestone)
        
        // 创建里程碑对象
        let milestone = ProgressMilestone(
            goalId: goal.id,
            goalName: goal.name,
            percentage: percentage,
            currentAmount: goal.currentAmount,
            targetAmount: goal.targetAmount
        )
        
        // 显示里程碑通知
        currentMilestone = milestone
        
        // 发送系统通知
        notificationManager.sendMilestoneNotification(
            goalName: goal.name,
            percentage: percentage,
            currentAmount: goal.currentAmount
        )
        
        // 更新角色状态为鼓励状态
        if currentCharacterState != .celebration {
            currentCharacterState = .encouraging
            currentMessage = personalizedMessages.getMessage(
                for: currentWorkScenario,
                progress: currentProgress,
                hasCompletedGoals: false
            )
        }
    }
    
    func loadSettings() {
        userSettings = persistenceManager.loadUserSettings()
    }
    
    func saveSettings() {
        do {
            try persistenceManager.saveUserSettings(userSettings)
        } catch {
            GlobalErrorHandler.shared.handleError(DataPersistenceError.saveOperationFailed("UserSettings"))
            lastError = .persistenceError(error.localizedDescription)
        }
    }
    

    
    func loadDailyEarnings() {
        dailyEarnings = persistenceManager.loadDailyEarnings()
    }
    
    func saveDailyEarnings() {
        do {
            try persistenceManager.saveDailyEarnings(dailyEarnings)
        } catch {
            GlobalErrorHandler.shared.handleError(DataPersistenceError.saveOperationFailed("DailyEarnings"))
            lastError = .persistenceError(error.localizedDescription)
        }
    }
    
    func checkAndResetDailyData() {
        let calendar = Calendar.current
        let now = Date()
        
        if let lastResetDate = persistenceManager.loadLastResetDate() {
            if !calendar.isDate(lastResetDate, inSameDayAs: now) {
                resetDailyData()
            }
        } else {
            resetDailyData()
        }
    }
    
    func resetDailyData() {
        dailyEarnings = 0.0
        reachedThresholds.removeAll()
        
        // 重置通知管理器的每日状态
        notificationManager.resetDailyNotifications()
        
        do {
            try persistenceManager.saveLastResetDate(Date())
        } catch {
            GlobalErrorHandler.shared.handleError(DataPersistenceError.saveOperationFailed("LastResetDate"))
            lastError = .persistenceError(error.localizedDescription)
        }
        
        saveDailyEarnings()
    }
}

// MARK: - Supporting Types
// BatteryOptimizationLevel已在ComplicationUpdateManager中定义

// MARK: - Notification Names
extension Notification.Name {
    static let pauseAnimations = Notification.Name("pauseAnimations")
    static let resumeAnimations = Notification.Name("resumeAnimations")
}

/// 个性化消息管理器 - 增强版
class PersonalizedMessages {
    // MARK: - 基础工作消息
    private let workMessages = [
        "💪 加油！主人工作的样子最棒了！",
        "✨ 今天也要努力工作哦～",
        "🎯 一步一步向目标前进！",
        "💼 工作虽辛苦，但收获满满！",
        "🌟 专注工作的主人真帅气！",
        "📈 每一分钟都在创造价值～",
        "🔥 保持这个节奏，太棒了！"
    ]
    
    // MARK: - 加班消息
    private let overtimeMessages = [
        "🌙 主人加班辛苦了，记得喝水休息！",
        "☕ 加班也要注意身体哦～",
        "⭐ 辛苦的付出会有回报的！",
        "💝 主人真努力，但别忘了休息！",
        "🌃 夜晚工作要多保重身体呀～",
        "💪 加班狂魔，但要劳逸结合！"
    ]
    
    // MARK: - 周末工作消息
    private let weekendMessages = [
        "🌴 周末也在工作，主人好拼！",
        "🎉 周末加班辛苦啦～",
        "👀 主人的努力我都看在眼里！",
        "😊 周末工作也要保持好心情！",
        "🌅 周末早起工作，真是勤奋呢！",
        "⚡ 连周末都不放松，太敬业了！"
    ]
    
    // MARK: - 休息消息
    private let restMessages = [
        "😴 休息时间到啦，放松一下吧～",
        "⚖️ 劳逸结合才是王道！",
        "🌙 好好休息，明天更有精神！",
        "🎊 周末愉快！休息一下充充电吧～",
        "🛋️ 是时候给自己一个拥抱了～",
        "🌸 休息是为了走更长的路哦！"
    ]
    
    // MARK: - 鼓励消息（按进度分级）
    private let encouragingMessages25 = [
        "🎯 不错的开始！继续保持！",
        "📊 已经完成四分之一了，棒棒的！",
        "🌱 小有进展，继续努力呀～",
        "⭐ 开局不错，稳步前进！"
    ]
    
    private let encouragingMessages50 = [
        "🎉 已经过半啦！太厉害了！",
        "🔥 进度过半，势不可挡！",
        "💪 中场休息，下半场继续加油！",
        "🌟 一半完成，胜利在望！"
    ]
    
    private let encouragingMessages75 = [
        "🚀 四分之三完成，冲刺阶段！",
        "⚡ 最后冲刺，胜利就在前方！",
        "🎯 距离目标只有一步之遥了！",
        "🔥 进入最后阶段，加油冲鸭！"
    ]
    
    // MARK: - 庆祝消息
    private let celebrationMessages = [
        "🎉 太棒了！今日目标已完成！",
        "👏 恭喜主人达成目标！",
        "🏆 目标达成！主人最棒了！",
        "✨ 完美！今天的努力有了回报！",
        "🎊 任务完成！主人太棒了！",
        "🌟 主人真是太优秀了！",
        "🎈 今天的你闪闪发光！"
    ]
    
    // MARK: - 时间相关消息
    private let morningMessages = [
        "🌅 早上好！新的一天开始啦！",
        "☀️ 早起的鸟儿有虫吃，主人真勤奋！",
        "🌄 晨光正好，适合工作呢～",
        "🌱 一日之计在于晨，加油！"
    ]
    
    private let afternoonMessages = [
        "🌞 下午好！继续保持活力哦～",
        "⏰ 午后时光，稳步前进中～",
        "💪 下午也要保持工作热情！",
        "🌈 午后阳光正好，心情棒棒！"
    ]
    
    private let eveningMessages = [
        "🌆 傍晚了，今天辛苦啦～",
        "🌇 夕阳西下，一天的努力值得称赞！",
        "⭐ 夜幕降临，但主人依然在努力！",
        "🌃 晚上工作要注意休息哦～"
    ]
    
    // MARK: - 特殊情况消息
    private let firstWorkMessages = [
        "🚀 今天第一次开始工作，加油！",
        "🌟 新的开始，新的希望！",
        "💪 今日首次启动，元气满满！"
    ]
    
    private let longWorkMessages = [
        "⏰ 工作时间挺长了，记得休息哦～",
        "💝 长时间工作辛苦啦，要劳逸结合！",
        "🌿 持续工作中，注意保护眼睛～"
    ]
    
    // MARK: - 主要消息获取方法
    func getMessage(for scenario: WorkScenario, progress: Double, hasCompletedGoals: Bool) -> String {
        // 优先处理庆祝情况
        if hasCompletedGoals || progress >= 1.0 {
            return getRandomMessage(from: celebrationMessages)
        }
        
        // 根据进度选择鼓励消息
        if progress >= 0.75 {
            return getRandomMessage(from: encouragingMessages75)
        } else if progress >= 0.50 {
            return getRandomMessage(from: encouragingMessages50)
        } else if progress >= 0.25 {
            return getRandomMessage(from: encouragingMessages25)
        }
        
        // 根据场景和时间选择消息
        return getContextualMessage(for: scenario)
    }
    
    // MARK: - 获取欢迎消息
    func getWelcomeMessage() -> String {
        let hour = Calendar.current.component(.hour, from: Date())
        
        switch hour {
        case 5...11:
            return getRandomMessage(from: morningMessages)
        case 12...17:
            return getRandomMessage(from: afternoonMessages)
        case 18...23, 0...4:
            return getRandomMessage(from: eveningMessages)
        default:
            return "🦊 小福狸陪你一起加油！"
        }
    }
    
    // MARK: - 获取特殊情况消息
    func getFirstWorkMessage() -> String {
        return getRandomMessage(from: firstWorkMessages)
    }
    
    func getLongWorkMessage() -> String {
        return getRandomMessage(from: longWorkMessages)
    }
    
    // MARK: - 私有辅助方法
    private func getContextualMessage(for scenario: WorkScenario) -> String {
        let hour = Calendar.current.component(.hour, from: Date())
        
        switch scenario {
        case .normalWork:
            // 根据时间添加上下文
            if hour <= 9 {
                return getRandomMessage(from: morningMessages + workMessages)
            } else if hour >= 18 {
                return getRandomMessage(from: eveningMessages + workMessages)
            } else {
                return getRandomMessage(from: workMessages)
            }
            
        case .nightOvertime:
            return getRandomMessage(from: overtimeMessages)
            
        case .weekendWithPay, .weekendVoluntary:
            return getRandomMessage(from: weekendMessages)
            
        case .resting, .lunchBreak:
            return getRandomMessage(from: restMessages)
        case .deepWork:
            return getRandomMessage(from: workMessages + ["专注模式！全力以赴！", "深度工作，高效产出！"])
        case .meeting:
            return getRandomMessage(from: workMessages + ["会议加油！", "发言时刻，展现自己！"])
        case .commuting:
            return getRandomMessage(from: restMessages + ["路上小心！", "通勤加油！"])
        }
    }
    
    private func getRandomMessage(from messages: [String]) -> String {
        return messages.randomElement() ?? "继续加油！"
    }
    
    // MARK: - 消息类型枚举
    enum MessageType {
        case welcome
        case work
        case overtime
        case weekend
        case rest
        case encouraging
        case celebration
        case firstWork
        case longWork
        
        var priority: Int {
            switch self {
            case .celebration: return 100
            case .firstWork: return 90
            case .longWork: return 80
            case .encouraging: return 70
            case .overtime: return 60
            case .weekend: return 50
            case .work: return 40
            case .welcome: return 30
            case .rest: return 20
            }
        }
    }
}

// MARK: - 智能场景检测集成
extension AppState {
    
    /// 设置智能场景检测通知监听
    private func setupScenarioDetectionObservers() {
        // 监听场景变化
        NotificationCenter.default.addObserver(
            forName: .scenarioChanged,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            self?.handleScenarioChanged(notification)
        }
        
        // 监听工作时长提醒
        NotificationCenter.default.addObserver(
            forName: .workDurationReminder,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            self?.handleWorkDurationReminder(notification)
        }
        
        // 监听休息提醒
        NotificationCenter.default.addObserver(
            forName: .restReminder,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            self?.handleRestReminder(notification)
        }
        
        // 监听姿势提醒
        NotificationCenter.default.addObserver(
            forName: .postureReminder,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            self?.handlePostureReminder(notification)
        }
    }
    
    /// 处理场景变化
    private func handleScenarioChanged(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let fromScenario = userInfo["from"] as? String,
              let toScenario = userInfo["to"] as? String,
              let confidence = userInfo["confidence"] as? Double else { return }
        
        print("🔄 场景自动切换: \(fromScenario) → \(toScenario) (置信度: \(String(format: "%.1f", confidence * 100))%)")
        
        // 更新工作场景
        if let newScenario = WorkScenario(rawValue: toScenario) {
            currentWorkScenario = newScenario
            
            // 发送场景通知
            sendScenarioNotification(newScenario)
            
            // 更新角色状态
            updateCharacterStateAndMessage()
            
            // 检查是否需要重新配置计算器
            configureCalculatorForScenario(newScenario)
        }
    }
    
    /// 处理工作时长提醒
    private func handleWorkDurationReminder(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let duration = userInfo["duration"] as? Int else { return }
        
        let message = "⏰ 已连续工作\(duration)分钟，建议休息10-15分钟！"
        
        // 发送本地通知（使用现有的通知系统）
        print("📢 工作提醒: \(message)")
        
        // 更新当前消息
        currentMessage = message
        
        // 更新角色状态为提醒状态
        if currentCharacterState != .celebration {
            currentCharacterState = .encouraging
        }
        
        lastWorkReminder = Date()
    }
    
    /// 处理休息提醒
    private func handleRestReminder(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let scenarioRaw = userInfo["scenario"] as? String,
              let scenario = WorkScenario(rawValue: scenarioRaw) else { return }
        
        let message = getRestReminderMessage(for: scenario)
        
        // 发送本地通知（使用现有的工作提醒方法）
        print("📢 休息提醒: \(message)")
        
        // 更新当前消息
        currentMessage = message
        
        // 短暂切换到鼓励状态
        if currentCharacterState != .celebration {
            currentCharacterState = .encouraging
            
            // 3秒后恢复正常状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 3) { [weak self] in
                self?.updateCharacterStateAndMessage()
            }
        }
    }
    
    /// 处理姿势提醒
    private func handlePostureReminder(_ notification: Notification) {
        let message = "🧘‍♀️ 记得调整坐姿，保护颈椎和腰椎哦！"
        
        // 发送本地通知（使用现有的通知系统）
        print("📢 健康提醒: \(message)")
        
        // 更新当前消息
        currentMessage = message
        
        lastPostureReminder = Date()
    }
    
    /// 为不同场景配置计算器
    private func configureCalculatorForScenario(_ scenario: WorkScenario) {
        switch scenario {
        case .deepWork:
            // 深度工作模式：更频繁的更新
            print("📈 进入深度工作模式，提高计算频率")
            
        case .meeting:
            // 会议模式：降低更新频率
            print("📋 进入会议模式，降低计算频率")
            
        case .commuting:
            // 通勤模式：暂停计算
            print("🚗 进入通勤模式，暂停工资计算")
            
        case .resting:
            // 休息模式：暂停计算并显示休息消息
            print("😌 进入休息模式")
            
        default:
            // 其他模式：正常计算
            print("⚡ 正常工作模式")
        }
    }
    
    /// 获取休息提醒消息
    private func getRestReminderMessage(for scenario: WorkScenario) -> String {
        switch scenario {
        case .deepWork:
            return "🧠 深度工作模式已持续较长时间，大脑需要休息啦！"
        case .normalWork:
            return "💼 工作告一段落，起来走走、喝口水吧！"
        case .nightOvertime:
            return "🌙 夜间加班辛苦了，更要注意休息哦！"
        case .meeting:
            return "📋 长时间开会容易疲劳，记得适时休息！"
        default:
            return "⏰ 该休息一下啦，劳逸结合效率更高！"
        }
    }
    
    /// 启用智能场景检测
    func enableSmartScenarioDetection(_ enabled: Bool) {
        scenarioDetector.isAutoDetectionEnabled = enabled
        
        if enabled {
            scenarioDetector.startAutoDetection()
            print("🤖 智能场景检测已启用")
        } else {
            scenarioDetector.stopAutoDetection()
            print("⏸️ 智能场景检测已禁用")
        }
    }
    
    /// 手动设置工作场景
    func manuallySetScenario(_ scenario: WorkScenario) {
        scenarioDetector.setScenario(scenario, confidence: 1.0)
        print("✋ 手动设置场景: \(scenario.displayName)")
    }
    
    /// 获取场景统计信息
    func getScenarioStatistics(for timeRange: TimeInterval = 24 * 60 * 60) -> [WorkScenario: TimeInterval] {
        return scenarioDetector.getScenarioStatistics(for: timeRange)
    }
    
    /// 获取工作效率建议
    func getWorkEfficiencyTips() -> [String] {
        let history = scenarioDetector.scenarioHistory
        let analysis = WorkModeAdvisor.analyzeWorkPatterns(history: history)
        
        var tips: [String] = []
        
        if analysis.deepWorkRatio < 0.3 {
            tips.append("💡 建议增加深度工作时间，提高工作效率")
        }
        
        if analysis.overtimeFrequency > 3 {
            tips.append("⚠️ 最近加班较频繁，注意休息避免过劳")
        }
        
        if analysis.efficiency < 0.6 {
            tips.append("📈 工作场景切换过于频繁，尝试保持专注")
        }
        
        if tips.isEmpty {
            tips.append("👍 工作状态很好，继续保持！")
        }
        
        return tips
    }
}

// MARK: - 用户增强的智能提醒扩展
extension AppState {
    
    /// 设置智能场景检测观察者 (用户增强版)
    func setupEnhancedScenarioDetectionObservers() {
        // 监听场景变化通知
        NotificationCenter.default.addObserver(
            forName: .scenarioChanged,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            self?.handleSmartScenarioChange(notification)
        }
        
        // 监听工作时长提醒
        NotificationCenter.default.addObserver(
            forName: .workDurationReminder,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            self?.handleWorkDurationReminder(notification)
        }
        
        // 监听休息提醒
        NotificationCenter.default.addObserver(
            forName: .restReminder,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            self?.handleRestReminder(notification)
        }
        
        // 监听姿势提醒
        NotificationCenter.default.addObserver(
            forName: .postureReminder,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            self?.handleEnhancedPostureReminder(notification)
        }
        
        // 同步用户设置到智能检测器
        syncSettingsToScenarioDetector()
        
        // 启动智能检测
        scenarioDetector.startAutoDetection()
        
        // 设置智能提醒管理器
        setupReminderManager()
    }
    
    /// 同步用户设置到场景检测器
    private func syncSettingsToScenarioDetector() {
        // 同步工作时间设置
        scenarioDetector.workHours = userSettings.workStartHour...userSettings.workEndHour
        
        // 同步午休时间设置（默认12-13点）
        scenarioDetector.lunchHours = 12...13
        
        // 启用基于活动的检测
        scenarioDetector.enableActivityBasedDetection = true
        
        // 根据用户偏好启用位置检测（暂时禁用）
        scenarioDetector.enableLocationBasedDetection = false
        
        // 启用日历集成（暂时禁用）
        scenarioDetector.enableCalendarIntegration = false
    }
    
    /// 处理智能场景变化
    private func handleSmartScenarioChange(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let fromRaw = userInfo["from"] as? String,
              let toRaw = userInfo["to"] as? String,
              let confidence = userInfo["confidence"] as? Double,
              let fromScenario = WorkScenario(rawValue: fromRaw),
              let toScenario = WorkScenario(rawValue: toRaw) else {
            return
        }
        
        print("智能场景检测: \(fromScenario.displayName) -> \(toScenario.displayName) (置信度: \(confidence))")
        
        // 更新当前工作场景
        currentWorkScenario = toScenario
        
        // 同步到工资计算器（如果需要）
        if toScenario != salaryCalculator.getCurrentScenario() {
            // 这里可以考虑是否要覆盖工资计算器的场景检测
            // 目前保持工资计算器的独立检测逻辑
        }
        
        // 更新角色状态和消息
        updateCharacterStateAndMessage()
        
        // 发送场景变化相关的通知
        sendSmartScenarioNotification(from: fromScenario, to: toScenario, confidence: confidence)
        
        // 根据新场景调整系统行为
        adjustSystemBehaviorForScenario(toScenario)
        
        // 同步场景到智能提醒管理器
        reminderManager.updateWorkScenario(toScenario)
    }
    
    /// 发送智能场景变化通知
    private func sendSmartScenarioNotification(from: WorkScenario, to: WorkScenario, confidence: Double) {
        // 只在高置信度且重要场景变化时发送通知
        guard confidence >= 0.8 else { return }
        
        let message: String
        
        switch to {
        case .normalWork:
            if from == .resting {
                message = "检测到开始工作，今天也要加油哦！💪"
            } else {
                return // 其他情况不发送通知
            }
        case .nightOvertime:
            message = "检测到夜间加班，记得适当休息哦 🌙"
        case .weekendWithPay, .weekendVoluntary:
            message = "检测到周末工作，注意劳逸结合 ⚡"
        case .deepWork:
            message = "进入专注模式，保持高效工作状态 🧠"
        case .lunchBreak:
            message = "午休时间到，记得好好休息 🍽️"
        case .resting:
            if from.isWorkingState {
                message = "工作结束，好好休息吧 😴"
            } else {
                return
            }
        case .meeting:
            message = "检测到会议状态，保持专注 📋"
        case .commuting:
            return // 通勤状态不发送通知
        }
        
        // 发送系统通知
        notificationManager.sendWorkReminder(message: message)
    }
    
    /// 根据场景调整系统行为
    private func adjustSystemBehaviorForScenario(_ scenario: WorkScenario) {
        switch scenario {
        case .deepWork:
            // 深度工作模式：减少干扰，提高计算精度
            salaryCalculator.intelligentFrequencyAdjustment()
            // 可以考虑暂停非关键通知
            
        case .meeting:
            // 会议模式：降低更新频率，减少干扰
            salaryCalculator.intelligentFrequencyAdjustment()
            
        case .lunchBreak, .resting:
            // 休息模式：大幅降低系统活动
            salaryCalculator.intelligentFrequencyAdjustment()
            
        case .nightOvertime:
            // 加班模式：提醒用户注意健康
            scheduleHealthReminders()
            
        default:
            // 其他场景：正常频率
            salaryCalculator.intelligentFrequencyAdjustment()
        }
    }
    
    /// 处理工作时长提醒 (用户增强版)
    private func handleEnhancedWorkDurationReminder(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let duration = userInfo["duration"] as? Int else {
            return
        }
        
        let hours = duration / 60
        let message = "已连续工作\(hours)小时，建议休息一下 ⏰"
        
        // 发送长时间工作提醒
        notificationManager.sendWorkReminder(message: message)
        
        // 更新角色状态为关怀模式
        if currentCharacterState != .celebration {
            currentCharacterState = .encouraging
            currentMessage = "主人已经工作很久了，记得休息哦～ 💝"
        }
        
        print("发送工作时长提醒: \(message)")
    }
    
    /// 处理休息提醒 (用户增强版)
    private func handleEnhancedRestReminder(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let scenarioRaw = userInfo["scenario"] as? String,
              let scenario = WorkScenario(rawValue: scenarioRaw) else {
            return
        }
        
        let message: String
        switch scenario {
        case .normalWork:
            message = "工作50分钟了，休息10分钟吧 ☕"
        case .deepWork:
            message = "专注工作90分钟了，该放松一下了 🧘"
        case .nightOvertime:
            message = "夜间加班辛苦了，一定要注意休息 🌙"
        default:
            message = "该休息一下了，劳逸结合更高效 ⚖️"
        }
        
        // 发送休息提醒
        notificationManager.sendWorkReminder(message: message)
        
        // 更新角色消息
        if currentWorkScenario.isWorkingState {
            currentMessage = message
        }
        
        print("发送休息提醒: \(message)")
    }
    
    /// 处理姿势提醒 (用户增强版)
    private func handleEnhancedPostureReminder(_ notification: Notification) {
        let message = "长时间保持同一姿势，活动一下身体吧 🤸"
        
        // 发送姿势提醒
        notificationManager.sendWorkReminder(message: message)
        
        // 更新角色消息
        if currentWorkScenario == .deepWork {
            currentMessage = "专注工作的同时，也要注意身体哦～ 💪"
        }
        
        print("发送姿势提醒: \(message)")
    }
    
    /// 安排健康提醒
    private func scheduleHealthReminders() {
        // 在加班时安排额外的健康提醒
        DispatchQueue.main.asyncAfter(deadline: .now() + 30 * 60) { [weak self] in
            guard let self = self,
                  self.currentWorkScenario == .nightOvertime else { return }
            
            let healthMessages = [
                "加班时记得多喝水哦 💧",
                "长时间工作要注意眼睛休息 👀",
                "深呼吸，放松一下肩膀 🫁",
                "站起来活动活动筋骨吧 🚶"
            ]
            
            let message = healthMessages.randomElement() ?? healthMessages[0]
            self.notificationManager.sendWorkReminder(message: message)
        }
    }
    
    /// 获取智能检测统计信息
    func getScenarioDetectionStats() -> [WorkScenario: TimeInterval] {
        return scenarioDetector.getScenarioStatistics()
    }
    
    /// 手动设置工作场景 (用户增强版)
    func manuallySetEnhancedScenario(_ scenario: WorkScenario) {
        scenarioDetector.setScenario(scenario, confidence: 1.0)
    }
    
    /// 启用/禁用自动场景检测
    func setAutoDetectionEnabled(_ enabled: Bool) {
        scenarioDetector.isAutoDetectionEnabled = enabled
        
        if enabled {
            scenarioDetector.startAutoDetection()
        } else {
            scenarioDetector.stopAutoDetection()
        }
    }
    
    /// 获取当前场景检测置信度
    func getCurrentDetectionConfidence() -> Double {
        return scenarioDetector.detectionConfidence
    }
    
    /// 获取场景历史记录
    func getScenarioHistory() -> [ScenarioHistoryEntry] {
        return scenarioDetector.scenarioHistory
    }
    
    /// 设置智能提醒管理器
    private func setupReminderManager() {
        // 设置提醒触发回调
        reminderManager.onReminderTriggered = { [weak self] (message: String) in
            DispatchQueue.main.async {
                self?.notificationManager.sendWorkReminder(message: message)
                self?.currentMessage = message
            }
        }
        
        // 启动提醒系统
        reminderManager.startReminders()
        
        // 同步当前工作场景
        reminderManager.updateWorkScenario(currentWorkScenario)
    }
    
    /// 设置全局错误处理
    private func setupGlobalErrorHandling() {
        let errorHandler = GlobalErrorHandler.shared
        
        // 注册各模块的错误处理器
        setupErrorHandlers(errorHandler)
        
        // 注册恢复动作处理器
        setupRecoveryHandlers(errorHandler)
        
        // 监听数据重新加载通知
        NotificationCenter.default.addObserver(
            forName: .appShouldReloadData,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.reloadAllData()
        }
        
        NotificationCenter.default.addObserver(
            forName: .appShouldReloadSettings,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.reloadSettings()
        }
    }
    
    /// 设置错误处理器
    private func setupErrorHandlers(_ errorHandler: GlobalErrorHandler) {
        // 计算错误处理器
        errorHandler.registerErrorHandler(for: .calculation) { [weak self] error in
            DispatchQueue.main.async {
                // 停止当前计算
                self?.stopCalculation()
                
                // 更新UI状态
                self?.calculationState = .error
                
                // 显示友好的错误消息
                self?.currentMessage = "计算暂时出现问题，正在尝试修复..."
            }
        }
        
        // 持久化错误处理器
        errorHandler.registerErrorHandler(for: .persistence) { [weak self] error in
            DispatchQueue.main.async {
                // 如果是数据损坏，切换到设置页面
                if let dataError = error as? DataPersistenceError,
                   case .corruptedData = dataError {
                    self?.switchToSettingsTab?()
                }
            }
        }
        
        // 通知错误处理器
        errorHandler.registerErrorHandler(for: .notification) { [weak self] error in
            DispatchQueue.main.async {
                // 禁用通知相关功能
                self?.notificationManager.settings.isEnabled = false
            }
        }
        
        // 系统错误处理器
        errorHandler.registerErrorHandler(for: .system) { [weak self] error in
            DispatchQueue.main.async {
                if let systemError = error as? SystemError {
                    switch systemError {
                    case .lowBattery(_):
                        self?.batteryOptimizationLevel = .minimal
                        self?.applyBatteryOptimization(.minimal)
                    case .lowMemory:
                        self?.enableMemoryOptimization()
                    default:
                        break
                    }
                }
            }
        }
        
        // 配置错误处理器
        errorHandler.registerErrorHandler(for: .configuration) { [weak self] error in
            DispatchQueue.main.async {
                // 显示配置相关的错误消息
                self?.currentMessage = "设置异常，已重置为默认配置"
            }
        }
    }
    
    /// 设置恢复动作处理器
    private func setupRecoveryHandlers(_ errorHandler: GlobalErrorHandler) {
        // 重试处理器
        errorHandler.registerRecoveryHandler(for: .retry) { [weak self] option, error in
            DispatchQueue.main.async {
                if error is SalaryCalculatorError {
                    // 重新启动计算
                    self?.startCalculation()
                } else if error is DataPersistenceError {
                    // 重新尝试保存数据
                    self?.saveAllData()
                }
            }
        }
        
        // 重置处理器
        errorHandler.registerRecoveryHandler(for: .reset) { [weak self] option, error in
            DispatchQueue.main.async {
                self?.resetToDefaultState()
            }
        }
        
        // 使用默认设置处理器
        errorHandler.registerRecoveryHandler(for: .useDefault) { [weak self] option, error in
            DispatchQueue.main.async {
                self?.applyDefaultSettings()
            }
        }
        
        // 忽略处理器
        errorHandler.registerRecoveryHandler(for: .ignore) { option, error in
            DispatchQueue.main.async {
                // 继续正常操作
                if error.category == .notification {
                    // 如果是通知错误，静默忽略
                    print("通知错误已忽略: \(error.userMessage)")
                }
            }
        }
    }
    
    /// 重新加载所有数据
    private func reloadAllData() {
        loadPersistedData()
        setupInitialState()
        startCalculation()
    }
    
    /// 重新加载设置
    private func reloadSettings() {
        userSettings = persistenceManager.loadUserSettings()
        
        // 应用新设置
        do {
            try salaryCalculator.updateSettings(userSettings)
            notificationManager.updateSettings(userSettings.notificationSettings)
        } catch {
            print("更新设置失败: \(error)")
        }
    }
    
    /// 重置到默认状态
    private func resetToDefaultState() {
        // 停止所有计算
        stopCalculation()
        
        // 重置到默认设置
        userSettings = .default
        goalTracker = GoalTracker()
        dailyEarnings = 0.0
        currentProgress = 0.0
        currentCharacterState = .normal
        currentMessage = ""
        
        // 保存默认数据
        saveAllData()
        
        // 重新启动
        startCalculation()
        
        currentMessage = "应用已重置，可以重新开始使用"
    }
    
    /// 应用默认设置
    private func applyDefaultSettings() {
        userSettings = .default
        do {
            try salaryCalculator.updateSettings(userSettings)
            notificationManager.updateSettings(userSettings.notificationSettings)
        } catch {
            print("应用默认设置失败: \(error)")
        }
        
        // 保存默认设置
        do {
            try persistenceManager.saveUserSettings(userSettings)
        } catch {
            print("保存默认设置失败: \(error)")
        }
        
        currentMessage = "已应用默认设置"
    }
    
    /// 启用内存优化
    private func enableMemoryOptimization() {
        // 停止不必要的动画
        showCharacterAnimation = false
        
        // 减少更新频率
        salaryCalculator.adjustFrequencyForBatteryOptimization(interval: 60.0) // 降低到每分钟更新一次
        
        // 停止消息更新定时器
        stopMessageUpdateTimer()
        
        currentMessage = "内存不足，已启用优化模式"
    }
    
    /// 保存所有数据
    private func saveAllData() {
        do {
            try persistenceManager.saveUserSettings(userSettings)
            try persistenceManager.saveGoals(goalTracker.goals)
        } catch {
            let persistenceError = DataPersistenceError.saveOperationFailed(error.localizedDescription)
            GlobalErrorHandler.shared.handleError(persistenceError)
        }
    }
    
    // MARK: - User Experience Setup
    
    /// 设置用户体验管理器
    private func setupUserExperience() {
        // 监听引导完成通知
        NotificationCenter.default.addObserver(
            forName: .onboardingCompleted,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.userExperienceManager?.triggerHapticFeedback(.success)
            self?.startCalculation()
            
            // 引导完成后跳转到设置页面，让用户进行详细配置
            // 增加延迟确保引导界面完全消失后再跳转
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self?.switchToSettingsTab?()
            }
        }
        
        // 监听性能级别变化
        NotificationCenter.default.addObserver(
            forName: .performanceLevelChanged,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let level = notification.object as? UIPerformanceLevel {
                self?.applyPerformanceOptimizations(level)
            }
        }
        
        // 根据电池状态优化用户体验
        let batteryLevel = WKInterfaceDevice.current().batteryLevel
        if batteryLevel > 0 && batteryLevel < 0.2 {
            userExperienceManager?.optimizeForBattery(true)
        }
    }
    
    /// 应用性能优化
    private func applyPerformanceOptimizations(_ level: UIPerformanceLevel) {
        // 调整动画显示
        switch level {
        case .minimal:
            showCharacterAnimation = false
            salaryCalculator.adjustFrequencyForBatteryOptimization(interval: 10.0)
        case .low:
            showCharacterAnimation = false
            salaryCalculator.adjustFrequencyForBatteryOptimization(interval: 5.0)
        case .medium:
            showCharacterAnimation = true
            salaryCalculator.adjustFrequencyForBatteryOptimization(interval: 2.0)
        case .high:
            showCharacterAnimation = true
            salaryCalculator.adjustFrequencyForBatteryOptimization(interval: 1.0)
        }
    }
}
