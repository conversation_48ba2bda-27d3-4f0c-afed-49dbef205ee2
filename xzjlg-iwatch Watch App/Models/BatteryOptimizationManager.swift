//
//  BatteryOptimizationManager.swift
//  xzjlg-iwatch Watch App
//
//  Created by Kiro on 2025/7/25.
//

import Foundation
import WatchKit
import Combine

/// 电池优化管理器，负责监测电池状态并应用优化策略
class BatteryOptimizationManager: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 当前电池电量 (0.0 - 1.0)
    @Published var currentBatteryLevel: Float = 1.0
    
    /// 当前优化级别
    @Published var currentOptimizationLevel: BatteryOptimizationLevel = .normal
    
    /// 是否处于低电量模式
    @Published var isLowPowerModeEnabled: Bool = false
    
    /// 当前时间段优化级别
    @Published var currentTimeOptimization: TimeBasedOptimization = .workHours
    
    /// 是否检测到长时间无交互
    @Published var isInIdleMode: Bool = false
    
    /// 是否启用电池优化
    @Published var isBatteryOptimizationEnabled: Bool = true
    
    // MARK: - Private Properties
    
    private var cancellables = Set<AnyCancellable>()
    
    // 电池监测相关
    private var batteryMonitorTimer: Timer?
    private let batteryCheckInterval: TimeInterval = 60.0 // 1分钟检查一次
    
    // 无交互检测相关
    private var lastInteractionTime: Date = Date()
    private var idleDetectionTimer: Timer?
    private let idleThreshold: TimeInterval = 300.0 // 5分钟无交互视为空闲
    private let idleCheckInterval: TimeInterval = 30.0 // 30秒检查一次
    
    // 时间段优化相关
    private var timeOptimizationTimer: Timer?
    private let timeCheckInterval: TimeInterval = 300.0 // 5分钟检查一次时间段
    
    // 优化策略配置
    private let optimizationConfig = OptimizationConfig()
    
    // MARK: - Callbacks
    
    /// 优化级别变化回调
    var onOptimizationLevelChanged: ((BatteryOptimizationLevel) -> Void)?
    
    /// 时间优化变化回调
    var onTimeOptimizationChanged: ((TimeBasedOptimization) -> Void)?
    
    /// 空闲状态变化回调
    var onIdleStateChanged: ((Bool) -> Void)?
    
    // MARK: - Initialization
    
    init() {
        startMonitoring()
        updateCurrentBatteryLevel()
        updateTimeOptimization()
    }
    
    deinit {
        stopMonitoring()
    }
    
    // MARK: - Public Methods
    
    /// 开始电池优化监测
    func startMonitoring() {
        startBatteryMonitoring()
        startIdleDetection()
        startTimeOptimization()
    }
    
    /// 停止电池优化监测
    func stopMonitoring() {
        stopBatteryMonitoring()
        stopIdleDetection()
        stopTimeOptimization()
    }
    
    /// 记录用户交互，重置空闲计时
    func recordUserInteraction() {
        lastInteractionTime = Date()
        
        if isInIdleMode {
            isInIdleMode = false
            onIdleStateChanged?(false)
            updateOptimizationLevel()
        }
    }
    
    /// 强制更新电池状态
    func updateBatteryStatus() {
        updateCurrentBatteryLevel()
        updateOptimizationLevel()
    }
    
    /// 获取当前应用的计算更新间隔
    func getCalculationInterval() -> TimeInterval {
        return optimizationConfig.getCalculationInterval(
            batteryLevel: currentOptimizationLevel,
            timeOptimization: currentTimeOptimization,
            isIdle: isInIdleMode
        )
    }
    
    /// 获取当前应用的动画更新间隔
    func getAnimationInterval() -> TimeInterval {
        return optimizationConfig.getAnimationInterval(
            batteryLevel: currentOptimizationLevel,
            timeOptimization: currentTimeOptimization,
            isIdle: isInIdleMode
        )
    }
    
    /// 是否应该显示动画
    func shouldShowAnimation() -> Bool {
        guard isBatteryOptimizationEnabled else { return true }
        
        // 极简模式或空闲模式下不显示动画
        if currentOptimizationLevel == .minimal || isInIdleMode {
            return false
        }
        
        // 深夜时段减少动画
        if currentTimeOptimization == .sleepHours {
            return false
        }
        
        return true
    }
    
    /// 是否应该发送通知
    func shouldSendNotification(priority: NotificationPriority) -> Bool {
        guard isBatteryOptimizationEnabled else { return true }
        
        // 极简模式下只发送关键通知
        if currentOptimizationLevel == .minimal && priority < NotificationPriority.critical {
            return false
        }
        
        // 节能模式下只发送中高优先级通知
        if currentOptimizationLevel == .reduced && priority < NotificationPriority.medium {
            return false
        }
        
        // 深夜时段只发送关键通知
        if currentTimeOptimization == .sleepHours && priority < NotificationPriority.critical {
            return false
        }
        
        return true
    }
    
    /// 是否应该暂停动画
    func shouldPauseAnimations() -> Bool {
        guard isBatteryOptimizationEnabled else { return false }
        
        // 极简模式或空闲模式下暂停动画
        if currentOptimizationLevel == .minimal || isInIdleMode {
            return true
        }
        
        // 深夜时段暂停动画
        if currentTimeOptimization == .sleepHours {
            return true
        }
        
        return false
    }
    
    /// 获取动画更新频率（帧率）
    func getAnimationFrameRate() -> Double {
        guard isBatteryOptimizationEnabled else { return 60.0 }
        
        switch currentOptimizationLevel {
        case .normal:
            return isInIdleMode ? 30.0 : 60.0
        case .reduced:
            return isInIdleMode ? 15.0 : 30.0
        case .minimal:
            return 0.0 // 完全停止动画
        }
    }
    
    /// 获取综合优化建议
    func getOptimizationRecommendations() -> [String] {
        var recommendations: [String] = []
        
        if currentBatteryLevel < 0.3 {
            recommendations.append("电池电量较低，建议减少动画效果")
        }
        
        if isInIdleMode {
            recommendations.append("检测到长时间无交互，已自动降低更新频率")
        }
        
        if currentTimeOptimization == .sleepHours {
            recommendations.append("当前为休息时间，已启用节能模式")
        }
        
        if isLowPowerModeEnabled {
            recommendations.append("系统低电量模式已启用，应用已自动优化")
        }
        
        return recommendations
    }
    
    /// 获取电池状态摘要
    func getBatteryStatusSummary() -> BatteryStatusSummary {
        return BatteryStatusSummary(
            batteryLevel: currentBatteryLevel,
            optimizationLevel: currentOptimizationLevel,
            timeOptimization: currentTimeOptimization,
            isLowPowerMode: isLowPowerModeEnabled,
            isIdleMode: isInIdleMode,
            shouldShowAnimation: shouldShowAnimation(),
            calculationInterval: getCalculationInterval(),
            animationFrameRate: getAnimationFrameRate(),
            recommendations: getOptimizationRecommendations()
        )
    }
}

// MARK: - Private Methods - Battery Monitoring

private extension BatteryOptimizationManager {
    
    /// 开始电池监测
    func startBatteryMonitoring() {
        updateCurrentBatteryLevel()
        
        batteryMonitorTimer = Timer.scheduledTimer(withTimeInterval: batteryCheckInterval, repeats: true) { [weak self] _ in
            self?.updateCurrentBatteryLevel()
            self?.updateOptimizationLevel()
        }
    }
    
    /// 停止电池监测
    func stopBatteryMonitoring() {
        batteryMonitorTimer?.invalidate()
        batteryMonitorTimer = nil
    }
    
    /// 更新当前电池电量
    func updateCurrentBatteryLevel() {
        let device = WKInterfaceDevice.current()
        device.isBatteryMonitoringEnabled = true
        
        currentBatteryLevel = device.batteryLevel
        isLowPowerModeEnabled = ProcessInfo.processInfo.isLowPowerModeEnabled
        
        print("电池电量更新: \(Int(currentBatteryLevel * 100))%, 低电量模式: \(isLowPowerModeEnabled)")
    }
    
    /// 更新优化级别
    func updateOptimizationLevel() {
        guard isBatteryOptimizationEnabled else { return }
        
        let newLevel: BatteryOptimizationLevel
        
        // 如果启用了低电量模式，强制使用极简模式
        if isLowPowerModeEnabled {
            newLevel = .minimal
        } else {
            switch currentBatteryLevel {
            case 0.0..<0.2:
                newLevel = .minimal
            case 0.2..<0.5:
                newLevel = .reduced
            default:
                newLevel = .normal
            }
        }
        
        if newLevel != currentOptimizationLevel {
            currentOptimizationLevel = newLevel
            onOptimizationLevelChanged?(newLevel)
            print("电池优化级别变更为: \(newLevel)")
        }
    }
}

// MARK: - Private Methods - Idle Detection

private extension BatteryOptimizationManager {
    
    /// 开始空闲检测
    func startIdleDetection() {
        idleDetectionTimer = Timer.scheduledTimer(withTimeInterval: idleCheckInterval, repeats: true) { [weak self] _ in
            self?.checkIdleState()
        }
    }
    
    /// 停止空闲检测
    func stopIdleDetection() {
        idleDetectionTimer?.invalidate()
        idleDetectionTimer = nil
    }
    
    /// 检查空闲状态
    func checkIdleState() {
        guard isBatteryOptimizationEnabled else { return }
        
        let timeSinceLastInteraction = Date().timeIntervalSince(lastInteractionTime)
        let shouldBeIdle = timeSinceLastInteraction >= idleThreshold
        
        if shouldBeIdle != isInIdleMode {
            isInIdleMode = shouldBeIdle
            onIdleStateChanged?(shouldBeIdle)
            
            if shouldBeIdle {
                print("检测到长时间无交互，进入空闲模式")
            } else {
                print("检测到用户交互，退出空闲模式")
            }
        }
    }
}

// MARK: - Private Methods - Time Optimization

private extension BatteryOptimizationManager {
    
    /// 开始时间段优化
    func startTimeOptimization() {
        updateTimeOptimization()
        
        timeOptimizationTimer = Timer.scheduledTimer(withTimeInterval: timeCheckInterval, repeats: true) { [weak self] _ in
            self?.updateTimeOptimization()
        }
    }
    
    /// 停止时间段优化
    func stopTimeOptimization() {
        timeOptimizationTimer?.invalidate()
        timeOptimizationTimer = nil
    }
    
    /// 更新时间段优化
    func updateTimeOptimization() {
        guard isBatteryOptimizationEnabled else { return }
        
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: Date())
        
        let newOptimization: TimeBasedOptimization
        
        switch hour {
        case 22...23, 0...6:
            // 深夜时段 (10PM-7AM)
            newOptimization = .sleepHours
        case 7...8, 19...21:
            // 扩展工作时段 (7AM-9AM, 7PM-10PM)
            newOptimization = .extendedHours
        default:
            // 正常工作时段 (9AM-7PM)
            newOptimization = .workHours
        }
        
        if newOptimization != currentTimeOptimization {
            currentTimeOptimization = newOptimization
            onTimeOptimizationChanged?(newOptimization)
            print("时间段优化变更为: \(newOptimization)")
        }
    }
    

}

// MARK: - Supporting Types

/// 时间段优化类型
enum TimeBasedOptimization: String, CaseIterable {
    case workHours = "work"         // 正常工作时段
    case extendedHours = "extended" // 扩展工作时段
    case sleepHours = "sleep"       // 深夜休息时段
    
    var displayName: String {
        switch self {
        case .workHours:
            return "工作时段"
        case .extendedHours:
            return "扩展时段"
        case .sleepHours:
            return "休息时段"
        }
    }
}



/// 电池状态摘要
struct BatteryStatusSummary {
    let batteryLevel: Float
    let optimizationLevel: BatteryOptimizationLevel
    let timeOptimization: TimeBasedOptimization
    let isLowPowerMode: Bool
    let isIdleMode: Bool
    let shouldShowAnimation: Bool
    let calculationInterval: TimeInterval
    let animationFrameRate: Double
    let recommendations: [String]
    
    var batteryPercentage: Int {
        return Int(batteryLevel * 100)
    }
    
    var statusDescription: String {
        var components: [String] = []
        
        components.append("电量: \(batteryPercentage)%")
        components.append("优化级别: \(optimizationLevel.rawValue)")
        components.append("时间段: \(timeOptimization.displayName)")
        
        if isLowPowerMode {
            components.append("低电量模式")
        }
        
        if isIdleMode {
            components.append("空闲模式")
        }
        
        return components.joined(separator: " | ")
    }
}

/// 优化策略配置
private struct OptimizationConfig {
    
    /// 获取计算更新间隔
    func getCalculationInterval(
        batteryLevel: BatteryOptimizationLevel,
        timeOptimization: TimeBasedOptimization,
        isIdle: Bool
    ) -> TimeInterval {
        
        // 基础间隔
        let baseInterval: TimeInterval
        switch batteryLevel {
        case .normal:
            baseInterval = 10.0  // 10秒
        case .reduced:
            baseInterval = 60.0  // 1分钟
        case .minimal:
            baseInterval = 300.0 // 5分钟
        }
        
        // 时间段调整
        let timeMultiplier: Double
        switch timeOptimization {
        case .workHours:
            timeMultiplier = 1.0
        case .extendedHours:
            timeMultiplier = 2.0
        case .sleepHours:
            timeMultiplier = 6.0
        }
        
        // 空闲状态调整
        let idleMultiplier: Double = isIdle ? 3.0 : 1.0
        
        return baseInterval * timeMultiplier * idleMultiplier
    }
    
    /// 获取动画更新间隔
    func getAnimationInterval(
        batteryLevel: BatteryOptimizationLevel,
        timeOptimization: TimeBasedOptimization,
        isIdle: Bool
    ) -> TimeInterval {
        
        // 动画间隔通常比计算间隔更快
        let calculationInterval = getCalculationInterval(
            batteryLevel: batteryLevel,
            timeOptimization: timeOptimization,
            isIdle: isIdle
        )
        
        // 动画间隔为计算间隔的1/10，但最少50ms
        return max(0.05, calculationInterval / 10.0)
    }
} 