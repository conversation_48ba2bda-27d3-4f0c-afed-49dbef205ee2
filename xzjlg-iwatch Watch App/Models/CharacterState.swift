//
//  CharacterState.swift
//  xzjlg-iwatch Watch App
//
//  Created by Kiro on 2025/7/25.
//

import Foundation
import SwiftUI

/// 小福狸角色状态枚举，定义不同的表情和动作状态
enum CharacterState: String, CaseIterable, Codable {
    /// 正常工作状态 - 微笑招手
    case normal = "normal"
    
    /// 鼓励状态 - 达到阶段性目标时的鼓励表情
    case encouraging = "encouraging"
    
    /// 庆祝状态 - 达成目标时的庆祝表情
    case celebration = "celebration"
    
    /// 加班状态 - 疲倦但坚持的表情
    case overtime = "overtime"
    
    /// 周末工作状态 - 努力工作的表情
    case weekendWork = "weekend_work"
    
    /// 休息状态 - 放松惬意的表情
    case resting = "resting"
    
    /// 状态显示名称
    var displayName: String {
        switch self {
        case .normal:
            return "正常工作"
        case .encouraging:
            return "加油鼓励"
        case .celebration:
            return "庆祝达成"
        case .overtime:
            return "加班状态"
        case .weekendWork:
            return "周末工作"
        case .resting:
            return "休息状态"
        }
    }
    
    /// 状态描述
    var description: String {
        switch self {
        case .normal:
            return "微笑招手，正常工作状态"
        case .encouraging:
            return "鼓励表情，为用户加油"
        case .celebration:
            return "欢呼庆祝，目标达成"
        case .overtime:
            return "疲倦但坚持，加班状态"
        case .weekendWork:
            return "努力工作，周末加班"
        case .resting:
            return "闭眼休息，放松状态"
        }
    }
}

// MARK: - Visual Properties
extension CharacterState {
    /// 眼睛状态
    var eyeState: EyeState {
        switch self {
        case .normal, .encouraging, .weekendWork:
            return .open
        case .celebration:
            return .happy
        case .overtime:
            return .tired
        case .resting:
            return .closed
        }
    }
    
    /// 嘴巴状态
    var mouthState: MouthState {
        switch self {
        case .normal, .encouraging, .weekendWork:
            return .smile
        case .celebration:
            return .bigSmile
        case .overtime:
            return .neutral
        case .resting:
            return .neutral
        }
    }
    
    /// 手臂动作状态
    var armState: ArmState {
        switch self {
        case .normal:
            return .waving
        case .encouraging:
            return .cheering
        case .celebration:
            return .celebrating
        case .overtime:
            return .working
        case .weekendWork:
            return .activeWorking
        case .resting:
            return .relaxed
        }
    }
    
    /// 动画速度倍数
    var animationSpeedMultiplier: Double {
        switch self {
        case .normal:
            return 1.0
        case .encouraging:
            return 1.2
        case .celebration:
            return 1.5
        case .overtime:
            return 0.8
        case .weekendWork:
            return 1.3
        case .resting:
            return 0.5
        }
    }
    
    /// 是否显示特效
    var showEffects: Bool {
        switch self {
        case .encouraging, .celebration:
            return true
        default:
            return false
        }
    }
    
    /// 特效类型
    var effectType: EffectType? {
        switch self {
        case .encouraging:
            return .stars
        case .celebration:
            return .confetti
        case .resting:
            return .hearts
        default:
            return nil
        }
    }
}

// MARK: - Supporting Enums
/// 眼睛状态
enum EyeState: String, CaseIterable {
    case open = "open"
    case closed = "closed"
    case happy = "happy"
    case tired = "tired"
}

/// 嘴巴状态
enum MouthState: String, CaseIterable {
    case neutral = "neutral"
    case smile = "smile"
    case bigSmile = "big_smile"
    case worried = "worried"
}

/// 手臂动作状态
enum ArmState: String, CaseIterable {
    case relaxed = "relaxed"
    case waving = "waving"
    case working = "working"
    case cheering = "cheering"
    case celebrating = "celebrating"
    case activeWorking = "active_working"
}

/// 特效类型
enum EffectType: String, CaseIterable {
    case stars = "stars"
    case confetti = "confetti"
    case hearts = "hearts"
}

// MARK: - State Mapping
extension CharacterState {
    /// 根据工作场景映射角色状态
    static func fromWorkScenario(_ scenario: WorkScenario) -> CharacterState {
        switch scenario {
        case .normalWork:
            return .normal
        case .nightOvertime:
            return .overtime
        case .weekendWithPay, .weekendVoluntary:
            return .weekendWork
        case .resting:
            return .resting
        case .lunchBreak:
            return .resting
        case .deepWork:
            return .normal // 深度工作使用正常状态
        case .meeting:
            return .normal // 会议使用正常状态
        case .commuting:
            return .resting // 通勤使用休息状态
        }
    }
    
    /// 根据工资进度获取状态
    static func fromSalaryProgress(_ progress: Double) -> CharacterState {
        if progress >= 1.0 {
            return .celebration
        } else if progress >= 0.25 {
            return .encouraging
        } else {
            return .normal
        }
    }
    
    /// 根据目标完成情况获取状态
    static func fromGoalCompletion(isCompleted: Bool) -> CharacterState {
        return isCompleted ? .celebration : .normal
    }
}

// MARK: - Animation Properties
extension CharacterState {
    /// 获取动画持续时间
    var animationDuration: Double {
        switch self {
        case .normal:
            return 2.0
        case .encouraging:
            return 1.5
        case .celebration:
            return 3.0
        case .overtime:
            return 2.5
        case .weekendWork:
            return 1.8
        case .resting:
            return 4.0
        }
    }
    
    /// 是否循环动画
    var shouldLoop: Bool {
        switch self {
        case .celebration:
            return false // 庆祝动画播放一次后回到正常状态
        default:
            return true
        }
    }
    
    /// 动画延迟
    var animationDelay: Double {
        switch self {
        case .celebration:
            return 0.5 // 庆祝状态稍微延迟，配合通知
        default:
            return 0.0
        }
    }
}