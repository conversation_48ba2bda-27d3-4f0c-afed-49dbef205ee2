//
//  ComplicationStyleManager.swift
//  xzjlg-iwatch Watch App
//
//  Created by Assistant on 2025/7/26.
//

import Foundation
import ClockKit
import SwiftUI

/// 复杂功能样式管理器
/// 负责管理不同样式的复杂功能模板和主题
class ComplicationStyleManager: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = ComplicationStyleManager()
    
    // MARK: - Properties
    
    /// 当前主题
    var currentTheme: ComplicationTheme = .default
    
    /// 支持的复杂功能样式
    let supportedStyles: [ComplicationStyle] = [
        .earnings,
        .progress,
        .combined,
        .minimal
    ]
    
    // MARK: - Initialization
    
    private init() {}
    
    // MARK: - Public Methods
    
    /// 获取指定样式的模板配置
    func getTemplateConfiguration(for style: ComplicationStyle, family: CLKComplicationFamily) -> ComplicationTemplateConfiguration {
        return ComplicationTemplateConfiguration(
            style: style,
            family: family,
            theme: currentTheme
        )
    }
    
    /// 创建自定义模板
    func createCustomTemplate(for family: CLKComplicationFamily, with data: ComplicationDisplayData, style: ComplicationStyle) -> CLKComplicationTemplate? {
        let config = getTemplateConfiguration(for: style, family: family)
        
        switch family {
        case .modularSmall:
            return createCustomModularSmall(with: data, config: config)
        case .modularLarge:
            return createCustomModularLarge(with: data, config: config)
        case .graphicCircular:
            return createCustomGraphicCircular(with: data, config: config)
        case .graphicRectangular:
            return createCustomGraphicRectangular(with: data, config: config)
        default:
            return nil
        }
    }
    
    /// 更新主题
    func updateTheme(_ theme: ComplicationTheme) {
        currentTheme = theme
        // 触发复杂功能更新
        // 更新复杂功能显示
        DispatchQueue.main.async {
            let server = CLKComplicationServer.sharedInstance()
            if let activeComplications = server.activeComplications {
                for complication in activeComplications {
                    server.reloadTimeline(for: complication)
                }
            }
        }
    }
}

// MARK: - Custom Template Creation
private extension ComplicationStyleManager {
    
    /// 创建自定义小型模块化模板
    func createCustomModularSmall(with data: ComplicationDisplayData, config: ComplicationTemplateConfiguration) -> CLKComplicationTemplate {
        let textProvider: CLKSimpleTextProvider
        
        switch config.style {
        case .earnings:
            textProvider = CLKSimpleTextProvider(text: data.earnings)
        case .progress:
            textProvider = CLKSimpleTextProvider(text: data.progress)
        case .combined:
            textProvider = CLKSimpleTextProvider(text: "\(data.earnings)")
        case .minimal:
            textProvider = CLKSimpleTextProvider(text: data.shortStatus)
        }
        
        textProvider.tintColor = config.theme.primaryColor
        let template = CLKComplicationTemplateModularSmallSimpleText(textProvider: textProvider)
        return template
    }
    
    /// 创建自定义大型模块化模板
    func createCustomModularLarge(with data: ComplicationDisplayData, config: ComplicationTemplateConfiguration) -> CLKComplicationTemplate {
        let headerTextProvider = CLKSimpleTextProvider(text: config.theme.appName)
        headerTextProvider.tintColor = config.theme.headerColor
        
        let body1TextProvider: CLKSimpleTextProvider
        let body2TextProvider: CLKSimpleTextProvider
        
        switch config.style {
        case .earnings:
            body1TextProvider = CLKSimpleTextProvider(text: data.earnings)
            body2TextProvider = CLKSimpleTextProvider(text: "今日收入")
        case .progress:
            body1TextProvider = CLKSimpleTextProvider(text: data.progress)
            body2TextProvider = CLKSimpleTextProvider(text: data.shortStatus)
        case .combined:
            body1TextProvider = CLKSimpleTextProvider(text: data.earnings)
            body2TextProvider = CLKSimpleTextProvider(text: "\(data.progress) • \(data.shortStatus)")
        case .minimal:
            body1TextProvider = CLKSimpleTextProvider(text: data.shortStatus)
            body2TextProvider = CLKSimpleTextProvider(text: data.progress)
        }
        
        body1TextProvider.tintColor = config.theme.primaryColor
        body2TextProvider.tintColor = config.theme.secondaryColor
        
        let template = CLKComplicationTemplateModularLargeStandardBody(
            headerTextProvider: headerTextProvider,
            body1TextProvider: body1TextProvider,
            body2TextProvider: body2TextProvider
        )
        
        return template
    }
    
    /// 创建自定义图形圆形模板
    func createCustomGraphicCircular(with data: ComplicationDisplayData, config: ComplicationTemplateConfiguration) -> CLKComplicationTemplate {
        let centerTextProvider: CLKSimpleTextProvider
        let bottomTextProvider: CLKSimpleTextProvider
        
        switch config.style {
        case .earnings:
            centerTextProvider = CLKSimpleTextProvider(text: data.earnings)
            bottomTextProvider = CLKSimpleTextProvider(text: "收入")
        case .progress:
            centerTextProvider = CLKSimpleTextProvider(text: data.progress)
            bottomTextProvider = CLKSimpleTextProvider(text: "进度")
        case .combined, .minimal:
            centerTextProvider = CLKSimpleTextProvider(text: data.progress)
            bottomTextProvider = CLKSimpleTextProvider(text: "今日")
        }
        
        let gaugeProvider = CLKSimpleGaugeProvider(
            style: .fill,
            gaugeColor: config.theme.gaugeColor,
            fillFraction: Float(data.progressValue)
        )
        
        let template = CLKComplicationTemplateGraphicCircularOpenGaugeSimpleText(
            gaugeProvider: gaugeProvider,
            bottomTextProvider: bottomTextProvider,
            centerTextProvider: centerTextProvider
        )
        
        return template
    }
    
    /// 创建自定义图形矩形模板
    func createCustomGraphicRectangular(with data: ComplicationDisplayData, config: ComplicationTemplateConfiguration) -> CLKComplicationTemplate {
        let headerTextProvider = CLKSimpleTextProvider(text: config.theme.appName)
        
        let body1TextProvider: CLKSimpleTextProvider
        let body2TextProvider: CLKSimpleTextProvider
        
        switch config.style {
        case .earnings:
            body1TextProvider = CLKSimpleTextProvider(text: data.earnings)
            body2TextProvider = CLKSimpleTextProvider(text: "今日收入")
        case .progress:
            body1TextProvider = CLKSimpleTextProvider(text: data.progress)
            body2TextProvider = CLKSimpleTextProvider(text: data.shortStatus)
        case .combined:
            body1TextProvider = CLKSimpleTextProvider(text: data.earnings)
            body2TextProvider = CLKSimpleTextProvider(text: "\(data.progress) • \(data.goalProgress)")
        case .minimal:
            body1TextProvider = CLKSimpleTextProvider(text: data.shortStatus)
            body2TextProvider = CLKSimpleTextProvider(text: data.progress)
        }
        
        let template = CLKComplicationTemplateGraphicRectangularStandardBody(
            headerTextProvider: headerTextProvider,
            body1TextProvider: body1TextProvider,
            body2TextProvider: body2TextProvider
        )
        
        return template
    }
}

// MARK: - Supporting Types

/// 复杂功能样式
enum ComplicationStyle: String, CaseIterable {
    case earnings = "earnings"      // 仅显示收入
    case progress = "progress"      // 仅显示进度
    case combined = "combined"      // 组合显示
    case minimal = "minimal"        // 最简显示
    
    var displayName: String {
        switch self {
        case .earnings:
            return "收入显示"
        case .progress:
            return "进度显示"
        case .combined:
            return "组合显示"
        case .minimal:
            return "简洁显示"
        }
    }
    
    var description: String {
        switch self {
        case .earnings:
            return "主要显示当前收入金额"
        case .progress:
            return "主要显示工作进度百分比"
        case .combined:
            return "同时显示收入和进度信息"
        case .minimal:
            return "显示最基本的状态信息"
        }
    }
}

/// 复杂功能主题
struct ComplicationTheme {
    let appName: String
    let primaryColor: UIColor
    let secondaryColor: UIColor
    let headerColor: UIColor
    let gaugeColor: UIColor
    
    static let `default` = ComplicationTheme(
        appName: "薪资奖励官",
        primaryColor: UIColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0),
        secondaryColor: UIColor(red: 0.56, green: 0.56, blue: 0.58, alpha: 1.0),
        headerColor: UIColor(red: 1.0, green: 0.58, blue: 0.0, alpha: 1.0),
        gaugeColor: UIColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0)
    )
    
    static let green = ComplicationTheme(
        appName: "薪资奖励官",
        primaryColor: UIColor(red: 0.2, green: 0.78, blue: 0.35, alpha: 1.0),
        secondaryColor: UIColor(red: 0.56, green: 0.56, blue: 0.58, alpha: 1.0),
        headerColor: UIColor(red: 0.2, green: 0.78, blue: 0.35, alpha: 1.0),
        gaugeColor: UIColor(red: 0.2, green: 0.78, blue: 0.35, alpha: 1.0)
    )
    
    static let orange = ComplicationTheme(
        appName: "薪资奖励官",
        primaryColor: UIColor(red: 1.0, green: 0.58, blue: 0.0, alpha: 1.0),
        secondaryColor: UIColor(red: 0.56, green: 0.56, blue: 0.58, alpha: 1.0),
        headerColor: UIColor(red: 1.0, green: 0.58, blue: 0.0, alpha: 1.0),
        gaugeColor: UIColor(red: 1.0, green: 0.58, blue: 0.0, alpha: 1.0)
    )
    
    static let purple = ComplicationTheme(
        appName: "薪资奖励官",
        primaryColor: UIColor(red: 0.69, green: 0.32, blue: 0.87, alpha: 1.0),
        secondaryColor: UIColor(red: 0.56, green: 0.56, blue: 0.58, alpha: 1.0),
        headerColor: UIColor(red: 0.69, green: 0.32, blue: 0.87, alpha: 1.0),
        gaugeColor: UIColor(red: 0.69, green: 0.32, blue: 0.87, alpha: 1.0)
    )
}

/// 复杂功能模板配置
struct ComplicationTemplateConfiguration {
    let style: ComplicationStyle
    let family: CLKComplicationFamily
    let theme: ComplicationTheme
    
    /// 获取深度链接 URL
    var deepLinkURL: URL? {
        return URL(string: "salary-reward-app://complication?style=\(style.rawValue)")
    }
}

// CLKComplicationFamily已经遵循CaseIterable协议