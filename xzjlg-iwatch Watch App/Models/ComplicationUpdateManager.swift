//
//  ComplicationUpdateManager.swift
//  xzjlg-iwatch Watch App
//
//  Created by Assistant on 2025/7/26.
//

import Foundation
import ClockKit
import Combine

/// 复杂功能更新管理器
/// 负责管理复杂功能的数据更新策略和时机
class ComplicationUpdateManager: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = ComplicationUpdateManager()
    
    // MARK: - Properties
    
    /// 更新策略
    @Published var updateStrategy: UpdateStrategy = .automatic
    
    /// 最后更新时间
    @Published var lastUpdateTime: Date = Date()
    
    /// 更新间隔（秒）
    @Published var updateInterval: TimeInterval = 300 // 5分钟
    
    /// 是否启用复杂功能
    @Published var isComplicationEnabled: Bool = true
    
    // Private properties
    private var updateTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    private let complicationProvider = WatchComplicationProvider.shared
    
    // MARK: - Initialization
    
    private init() {
        setupUpdateStrategy()
        startPeriodicUpdates()
    }
    
    deinit {
        stopPeriodicUpdates()
    }
    
    // MARK: - Public Methods
    
    /// 立即更新复杂功能
    func updateImmediately() {
        guard isComplicationEnabled else { return }
        
        complicationProvider.updateComplicationData()
        lastUpdateTime = Date()
        
        // 请求系统更新复杂功能
        requestSystemUpdate()
    }
    
    /// 设置更新策略
    func setUpdateStrategy(_ strategy: UpdateStrategy) {
        updateStrategy = strategy
        setupUpdateStrategy()
    }
    
    /// 设置更新间隔
    func setUpdateInterval(_ interval: TimeInterval) {
        updateInterval = max(interval, 60) // 最小1分钟
        restartPeriodicUpdates()
    }
    
    /// 启用/禁用复杂功能
    func setComplicationEnabled(_ enabled: Bool) {
        isComplicationEnabled = enabled
        
        if enabled {
            updateImmediately()
            startPeriodicUpdates()
        } else {
            stopPeriodicUpdates()
        }
    }
    
    /// 处理应用状态变化
    func handleAppStateChange(_ newState: AppState) {
        // 根据应用状态调整更新策略
        switch newState.calculationState {
        case .calculating:
            // 工作时更频繁更新
            setUpdateInterval(60) // 1分钟
        case .paused, .stopped:
            // 暂停时降低更新频率
            setUpdateInterval(300) // 5分钟
        case .error:
            // 错误时停止更新
            stopPeriodicUpdates()
        }
    }
    
    /// 处理电池优化
    func handleBatteryOptimization(_ level: BatteryOptimizationLevel) {
        switch level {
        case .normal:
            setUpdateInterval(60) // 1分钟
        case .reduced:
            setUpdateInterval(300) // 5分钟
        case .minimal:
            setUpdateInterval(900) // 15分钟
        }
    }
}

// MARK: - Private Methods
private extension ComplicationUpdateManager {
    
    /// 设置更新策略
    func setupUpdateStrategy() {
        switch updateStrategy {
        case .automatic:
            startPeriodicUpdates()
        case .manual:
            stopPeriodicUpdates()
        case .smart:
            setupSmartUpdates()
        }
    }
    
    /// 开始周期性更新
    func startPeriodicUpdates() {
        stopPeriodicUpdates()
        
        updateTimer = Timer.scheduledTimer(withTimeInterval: updateInterval, repeats: true) { [weak self] _ in
            self?.updateImmediately()
        }
    }
    
    /// 停止周期性更新
    func stopPeriodicUpdates() {
        updateTimer?.invalidate()
        updateTimer = nil
    }
    
    /// 重启周期性更新
    func restartPeriodicUpdates() {
        if updateStrategy == .automatic || updateStrategy == .smart {
            startPeriodicUpdates()
        }
    }
    
    /// 设置智能更新
    func setupSmartUpdates() {
        // 智能更新策略：根据时间和使用情况调整更新频率
        let hour = Calendar.current.component(.hour, from: Date())
        
        switch hour {
        case 9...18: // 工作时间
            setUpdateInterval(60) // 1分钟
        case 19...22: // 晚上
            setUpdateInterval(300) // 5分钟
        default: // 深夜和早晨
            setUpdateInterval(900) // 15分钟
        }
        
        startPeriodicUpdates()
    }
    
    /// 请求系统更新复杂功能
    private func requestSystemUpdate() {
        let server = CLKComplicationServer.sharedInstance()
        
        // 更新所有活跃的复杂功能
        for complication in server.activeComplications ?? [] {
            server.reloadTimeline(for: complication)
        }
        
        // 扩展时间线
        for complication in server.activeComplications ?? [] {
            server.extendTimeline(for: complication)
        }
    }
}

// MARK: - Supporting Types

/// 更新策略
enum UpdateStrategy: String, CaseIterable {
    case automatic = "automatic"    // 自动更新
    case manual = "manual"         // 手动更新
    case smart = "smart"           // 智能更新
    
    var displayName: String {
        switch self {
        case .automatic:
            return "自动更新"
        case .manual:
            return "手动更新"
        case .smart:
            return "智能更新"
        }
    }
    
    var description: String {
        switch self {
        case .automatic:
            return "定期自动更新复杂功能数据"
        case .manual:
            return "仅在手动触发时更新"
        case .smart:
            return "根据时间和使用情况智能调整更新频率"
        }
    }
}

/// 电池优化级别（从 AppState 复制）
enum BatteryOptimizationLevel: String, CaseIterable {
    case normal = "normal"
    case reduced = "reduced"
    case minimal = "minimal"
}

// MARK: - Notification Extensions
extension ComplicationUpdateManager {
    
    /// 注册通知观察者
    func registerNotificationObservers() {
        // 监听应用进入前台
        NotificationCenter.default.publisher(for: .NSExtensionHostWillEnterForeground)
            .sink { [weak self] _ in
                self?.updateImmediately()
            }
            .store(in: &cancellables)
        
        // 监听应用进入后台
        NotificationCenter.default.publisher(for: .NSExtensionHostDidEnterBackground)
            .sink { [weak self] _ in
                self?.handleAppEnterBackground()
            }
            .store(in: &cancellables)
        
        // 监听电池状态变化
        NotificationCenter.default.publisher(for: .NSProcessInfoPowerStateDidChange)
            .sink { [weak self] _ in
                self?.handlePowerStateChange()
            }
            .store(in: &cancellables)
    }
    
    /// 处理应用进入后台
    private func handleAppEnterBackground() {
        // 在后台时降低更新频率
        if updateStrategy == .smart {
            setUpdateInterval(900) // 15分钟
        }
    }
    
    /// 处理电源状态变化
    private func handlePowerStateChange() {
        // 根据电池状态调整更新策略
        // 这里可以集成电池监测逻辑
    }
}