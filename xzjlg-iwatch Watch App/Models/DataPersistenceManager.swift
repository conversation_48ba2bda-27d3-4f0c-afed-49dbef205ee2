//
//  DataPersistenceManager.swift
//  xzjlg-iwatch Watch App
//
//  Created by Assistant on 2025/1/20.
//

import Foundation
import os.log

/// 数据持久化错误类型
enum DataPersistenceError: Error, LocalizedError {
    case encodingFailed(String)
    case decodingFailed(String)
    case corruptedData(String)
    case saveOperationFailed(String)
    case loadOperationFailed(String)
    case migrationFailed(String)
    case repairFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .encodingFailed(let message):
            return "数据编码失败: \(message)"
        case .decodingFailed(let message):
            return "数据解码失败: \(message)"
        case .corruptedData(let message):
            return "数据损坏: \(message)"
        case .saveOperationFailed(let message):
            return "保存操作失败: \(message)"
        case .loadOperationFailed(let message):
            return "加载操作失败: \(message)"
        case .migrationFailed(let message):
            return "数据迁移失败: \(message)"
        case .repairFailed(let message):
            return "数据修复失败: \(message)"
        }
    }
}

/// 数据完整性验证协议
protocol DataIntegrityValidator {
    func validate() -> Bool
    mutating func repair() -> Bool
}

/// 数据版本化协议
protocol VersionedData: Codable {
    static var currentVersion: Int { get }
    var version: Int { get set }
    
    func migrateToCurrentVersion() -> Self?
}

/// 数据持久化管理器
class DataPersistenceManager {
    
    // MARK: - Singleton
    
    static let shared = DataPersistenceManager()
    
    // MARK: - Properties
    
    private let userDefaults = UserDefaults.standard
    private let logger = Logger(subsystem: "com.xzjlg.iwatch", category: "DataPersistence")
    
    // 存储键常量
    private enum StorageKeys {
        static let userSettings = "user_settings_v2"
        static let goals = "user_goals_v2"
        static let dailyEarnings = "daily_earnings_v2"
        static let notificationSettings = "notification_settings_v2"
        static let notificationHistory = "notification_history_v2"
        static let lastResetDate = "last_reset_date_v2"
        static let appVersion = "app_version"
        static let dataIntegrityCheck = "data_integrity_check"
        
        // 备份键
        static let userSettingsBackup = "user_settings_backup"
        static let goalsBackup = "user_goals_backup"
    }
    
    // 重试配置
    private let maxRetryAttempts = 3
    private let retryDelay: TimeInterval = 0.5
    
    // MARK: - Initialization
    
    private init() {
        setupDataMigration()
        performIntegrityCheck()
    }
    
    // MARK: - Public Save Methods
    
    /// 保存用户设置
    func saveUserSettings(_ settings: UserSettings) throws {
        var versionedSettings = VersionedUserSettings(from: settings)
        versionedSettings.version = VersionedUserSettings.currentVersion
        
        try saveWithRetry(versionedSettings, key: StorageKeys.userSettings)
        
        // 创建备份
        try? saveWithRetry(versionedSettings, key: StorageKeys.userSettingsBackup, maxRetries: 1)
        
        logger.info("用户设置已保存")
    }
    
    /// 保存目标列表
    func saveGoals(_ goals: [Goal]) throws {
        let versionedGoals = VersionedGoals(goals: goals, version: VersionedGoals.currentVersion)
        
        try saveWithRetry(versionedGoals, key: StorageKeys.goals)
        
        // 创建备份
        try? saveWithRetry(versionedGoals, key: StorageKeys.goalsBackup, maxRetries: 1)
        
        logger.info("目标数据已保存，共 \(goals.count) 个目标")
    }
    
    /// 保存每日收入
    func saveDailyEarnings(_ earnings: Double) throws {
        let data = ["earnings": earnings, "timestamp": Date().timeIntervalSince1970]
        try saveWithRetry(data, key: StorageKeys.dailyEarnings)
        
        logger.debug("每日收入已保存: ¥\(String(format: "%.2f", earnings))")
    }
    
    /// 保存通知设置
    func saveNotificationSettings(_ settings: NotificationSettings) throws {
        let versionedSettings = VersionedNotificationSettings(from: settings)
        try saveWithRetry(versionedSettings, key: StorageKeys.notificationSettings)
        
        logger.info("通知设置已保存")
    }
    
    /// 保存通知历史
    func saveNotificationHistory(_ history: [NotificationHistory]) throws {
        // 只保存最近100条记录
        let recentHistory = Array(history.suffix(100))
        let versionedHistory = VersionedNotificationHistory(history: recentHistory)
        
        try saveWithRetry(versionedHistory, key: StorageKeys.notificationHistory)
        
        logger.debug("通知历史已保存，共 \(recentHistory.count) 条记录")
    }
    
    /// 保存最后重置日期
    func saveLastResetDate(_ date: Date) throws {
        try saveWithRetry(date.timeIntervalSince1970, key: StorageKeys.lastResetDate)
        
        logger.debug("最后重置日期已保存: \(date)")
    }
    
    // MARK: - Public Load Methods
    
    /// 加载用户设置
    func loadUserSettings() -> UserSettings {
        do {
            let versionedSettings: VersionedUserSettings = try loadWithValidation(key: StorageKeys.userSettings)
            
            // 检查版本并迁移
            if let migratedSettings = versionedSettings.migrateToCurrentVersion() {
                let userSettings = migratedSettings.toUserSettings()
                
                // 保存迁移后的数据
                try? saveUserSettings(userSettings)
                
                logger.info("用户设置已加载并迁移到最新版本")
                return userSettings
            } else {
                logger.warning("用户设置迁移失败，使用默认设置")
                return UserSettings.default
            }
        } catch {
            logger.error("加载用户设置失败: \(error.localizedDescription)")
            
            // 尝试从备份恢复
            if let backupSettings = try? loadFromBackup(VersionedUserSettings.self, key: StorageKeys.userSettingsBackup) {
                logger.info("从备份恢复用户设置")
                return backupSettings.toUserSettings()
            }
            
            return UserSettings.default
        }
    }
    
    /// 加载目标列表
    func loadGoals() -> [Goal] {
        do {
            let versionedGoals: VersionedGoals = try loadWithValidation(key: StorageKeys.goals)
            
            if let migratedGoals = versionedGoals.migrateToCurrentVersion() {
                // 保存迁移后的数据
                try? saveGoals(migratedGoals.goals)
                
                logger.info("目标数据已加载，共 \(migratedGoals.goals.count) 个目标")
                return migratedGoals.goals
            } else {
                logger.warning("目标数据迁移失败，返回空列表")
                return []
            }
        } catch {
            logger.error("加载目标数据失败: \(error.localizedDescription)")
            
            // 尝试从备份恢复
            if let backupGoals = try? loadFromBackup(VersionedGoals.self, key: StorageKeys.goalsBackup) {
                logger.info("从备份恢复目标数据")
                return backupGoals.goals
            }
            
            return []
        }
    }
    
    /// 加载每日收入
    func loadDailyEarnings() -> Double {
        do {
            let data: [String: Double] = try loadWithValidation(key: StorageKeys.dailyEarnings)
            
            // 检查数据时效性
            if let timestamp = data["timestamp"], 
               let earnings = data["earnings"],
               Calendar.current.isDateInToday(Date(timeIntervalSince1970: timestamp)) {
                                 logger.debug("每日收入已加载: ¥\(String(format: "%.2f", earnings))")
                return earnings
            } else {
                logger.debug("每日收入数据过期，返回0")
                return 0.0
            }
        } catch {
            logger.error("加载每日收入失败: \(error.localizedDescription)")
            return 0.0
        }
    }
    
    /// 加载通知设置
    func loadNotificationSettings() -> NotificationSettings {
        do {
            let versionedSettings: VersionedNotificationSettings = try loadWithValidation(key: StorageKeys.notificationSettings)
            
            if let migratedSettings = versionedSettings.migrateToCurrentVersion() {
                try? saveNotificationSettings(migratedSettings.toNotificationSettings())
                
                logger.info("通知设置已加载")
                return migratedSettings.toNotificationSettings()
            } else {
                logger.warning("通知设置迁移失败，使用默认设置")
                return NotificationSettings.default
            }
        } catch {
            logger.error("加载通知设置失败: \(error.localizedDescription)")
            return NotificationSettings.default
        }
    }
    
    /// 加载通知历史
    func loadNotificationHistory() -> [NotificationHistory] {
        do {
            let versionedHistory: VersionedNotificationHistory = try loadWithValidation(key: StorageKeys.notificationHistory)
            
            logger.debug("通知历史已加载，共 \(versionedHistory.history.count) 条记录")
            return versionedHistory.history
        } catch {
            logger.error("加载通知历史失败: \(error.localizedDescription)")
            return []
        }
    }
    
    /// 加载最后重置日期
    func loadLastResetDate() -> Date? {
        do {
            let timestamp: Double = try loadWithValidation(key: StorageKeys.lastResetDate)
            let date = Date(timeIntervalSince1970: timestamp)
            
            logger.debug("最后重置日期已加载: \(date)")
            return date
        } catch {
            logger.error("加载最后重置日期失败: \(error.localizedDescription)")
            return nil
        }
    }
    
    // MARK: - Batch Operations
    
    /// 批量保存所有数据
    func saveAllData(
        userSettings: UserSettings,
        goals: [Goal],
        dailyEarnings: Double,
        notificationSettings: NotificationSettings,
        notificationHistory: [NotificationHistory]
    ) throws {
        var errors: [Error] = []
        
        do {
            try saveUserSettings(userSettings)
        } catch {
            errors.append(error)
        }
        
        do {
            try saveGoals(goals)
        } catch {
            errors.append(error)
        }
        
        do {
            try saveDailyEarnings(dailyEarnings)
        } catch {
            errors.append(error)
        }
        
        do {
            try saveNotificationSettings(notificationSettings)
        } catch {
            errors.append(error)
        }
        
        do {
            try saveNotificationHistory(notificationHistory)
        } catch {
            errors.append(error)
        }
        
        if !errors.isEmpty {
            logger.error("批量保存过程中发生 \(errors.count) 个错误")
            throw DataPersistenceError.saveOperationFailed("批量保存失败: \(errors.count) 个操作失败")
        }
        
        logger.info("所有数据批量保存成功")
    }
    
    // MARK: - Data Management
    
    /// 清除所有数据
    func clearAllData() {
        let keys = [
            StorageKeys.userSettings,
            StorageKeys.goals,
            StorageKeys.dailyEarnings,
            StorageKeys.notificationSettings,
            StorageKeys.notificationHistory,
            StorageKeys.lastResetDate,
            StorageKeys.userSettingsBackup,
            StorageKeys.goalsBackup
        ]
        
        for key in keys {
            userDefaults.removeObject(forKey: key)
        }
        
        logger.info("所有数据已清除")
    }
    
    /// 检查数据完整性
    func checkDataIntegrity() -> Bool {
        var isValid = true
        
        // 检查用户设置
        if let settings = try? loadWithValidation(VersionedUserSettings.self, key: StorageKeys.userSettings) {
            isValid = isValid && settings.validate()
        }
        
        // 检查目标数据
        if let goals = try? loadWithValidation(VersionedGoals.self, key: StorageKeys.goals) {
            isValid = isValid && goals.validate()
        }
        
        // 记录检查结果
        userDefaults.set(Date().timeIntervalSince1970, forKey: StorageKeys.dataIntegrityCheck)
        
        logger.info("数据完整性检查完成: \(isValid ? "通过" : "失败")")
        return isValid
    }
    
    /// 修复损坏的数据
    func repairCorruptedData() -> Bool {
        var repairSuccess = true
        
        // 修复用户设置
        if var settings = try? loadWithValidation(VersionedUserSettings.self, key: StorageKeys.userSettings) {
            if !settings.validate() {
                if settings.repair() {
                    try? saveUserSettings(settings.toUserSettings())
                    logger.info("用户设置已修复")
                } else {
                    // 从备份恢复
                    if let backup = try? loadFromBackup(VersionedUserSettings.self, key: StorageKeys.userSettingsBackup) {
                        try? saveUserSettings(backup.toUserSettings())
                        logger.info("用户设置已从备份恢复")
                    } else {
                        try? saveUserSettings(UserSettings.default)
                        logger.warning("用户设置已重置为默认值")
                        repairSuccess = false
                    }
                }
            }
        }
        
        // 修复目标数据
        if var goals = try? loadWithValidation(VersionedGoals.self, key: StorageKeys.goals) {
            if !goals.validate() {
                if goals.repair() {
                    try? saveGoals(goals.goals)
                    logger.info("目标数据已修复")
                } else {
                    // 从备份恢复
                    if let backup = try? loadFromBackup(VersionedGoals.self, key: StorageKeys.goalsBackup) {
                        try? saveGoals(backup.goals)
                        logger.info("目标数据已从备份恢复")
                    } else {
                        try? saveGoals([])
                        logger.warning("目标数据已重置为空")
                        repairSuccess = false
                    }
                }
            }
        }
        
        logger.info("数据修复完成: \(repairSuccess ? "成功" : "部分失败")")
        return repairSuccess
    }
    
    // MARK: - Private Methods
    
    /// 带重试的保存操作
    private func saveWithRetry<T: Codable>(_ data: T, key: String, maxRetries: Int? = nil) throws {
        let retries = maxRetries ?? maxRetryAttempts
        var lastError: Error?
        
        for attempt in 0..<retries {
            do {
                let encodedData = try JSONEncoder().encode(data)
                userDefaults.set(encodedData, forKey: key)
                
                // 验证保存是否成功
                if userDefaults.data(forKey: key) != nil {
                    return
                }
            } catch {
                lastError = error
                logger.warning("保存失败 (尝试 \(attempt + 1)/\(retries)): \(error.localizedDescription)")
                
                if attempt < retries - 1 {
                    Thread.sleep(forTimeInterval: retryDelay * Double(attempt + 1))
                }
            }
        }
        
        throw DataPersistenceError.saveOperationFailed(lastError?.localizedDescription ?? "未知错误")
    }
    
    /// 带验证的加载操作
    private func loadWithValidation<T: Codable>(_ type: T.Type, key: String) throws -> T {
        guard let data = userDefaults.data(forKey: key) else {
            throw DataPersistenceError.loadOperationFailed("数据不存在: \(key)")
        }
        
        do {
            let decodedData = try JSONDecoder().decode(type, from: data)
            return decodedData
        } catch {
            throw DataPersistenceError.decodingFailed("解码失败: \(error.localizedDescription)")
        }
    }
    
    /// 简化版加载（泛型推断）
    private func loadWithValidation<T: Codable>(key: String) throws -> T {
        return try loadWithValidation(T.self, key: key)
    }
    
    /// 从备份加载数据
    private func loadFromBackup<T: Codable>(_ type: T.Type, key: String) throws -> T {
        guard let data = userDefaults.data(forKey: key) else {
            throw DataPersistenceError.loadOperationFailed("备份数据不存在: \(key)")
        }
        
        return try JSONDecoder().decode(type, from: data)
    }
    
    /// 设置数据迁移
    private func setupDataMigration() {
        let currentAppVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
        let savedAppVersion = userDefaults.string(forKey: StorageKeys.appVersion)
        
        if savedAppVersion != currentAppVersion {
            logger.info("检测到应用版本更新: \(savedAppVersion ?? "未知") -> \(currentAppVersion)")
            
            // 执行数据迁移
            performDataMigration(from: savedAppVersion, to: currentAppVersion)
            
            // 更新版本号
            userDefaults.set(currentAppVersion, forKey: StorageKeys.appVersion)
        }
    }
    
    /// 执行数据迁移
    private func performDataMigration(from oldVersion: String?, to newVersion: String) {
        logger.info("开始数据迁移...")
        
        // 这里可以添加版本特定的迁移逻辑
        // 例如：旧版本的key迁移到新版本
        
        logger.info("数据迁移完成")
    }
    
    /// 执行完整性检查
    private func performIntegrityCheck() {
        let lastCheck = userDefaults.double(forKey: StorageKeys.dataIntegrityCheck)
        let daysSinceLastCheck = (Date().timeIntervalSince1970 - lastCheck) / (24 * 3600)
        
        // 每7天进行一次完整性检查
        if daysSinceLastCheck > 7 || lastCheck == 0 {
            DispatchQueue.global(qos: .utility).async { [weak self] in
                let isValid = self?.checkDataIntegrity() ?? false
                
                if !isValid {
                    DispatchQueue.main.async {
                        _ = self?.repairCorruptedData()
                    }
                }
            }
        }
    }
} 