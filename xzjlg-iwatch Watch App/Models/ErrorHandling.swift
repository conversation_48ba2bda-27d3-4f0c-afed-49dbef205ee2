//
//  ErrorHandling.swift
//  xzjlg-iwatch Watch App
//
//  Created by Assistant on 2025/1/20.
//

import Foundation
import SwiftUI
import UserNotifications
import os.log

// MARK: - 统一错误类型定义

/// 应用程序错误协议
protocol AppError: Error, LocalizedError {
    var errorCode: String { get }
    var severity: ErrorSeverity { get }
    var category: ErrorCategory { get }
    var userMessage: String { get }
    var technicalMessage: String { get }
    var recoveryOptions: [ErrorRecoveryOption] { get }
}

/// 错误严重程度
enum ErrorSeverity: String, CaseIterable, Codable {
    case info = "info"
    case warning = "warning"
    case error = "error"
    case critical = "critical"
    
    var displayName: String {
        switch self {
        case .info: return "信息"
        case .warning: return "警告"
        case .error: return "错误"
        case .critical: return "严重错误"
        }
    }
    
    var priority: Int {
        switch self {
        case .info: return 1
        case .warning: return 2
        case .error: return 3
        case .critical: return 4
        }
    }
}

/// 错误分类
enum ErrorCategory: String, CaseIterable, Codable {
    case calculation = "calculation"
    case persistence = "persistence"
    case notification = "notification"
    case configuration = "configuration"
    case network = "network"
    case system = "system"
    case userInput = "user_input"
    
    var displayName: String {
        switch self {
        case .calculation: return "计算"
        case .persistence: return "数据存储"
        case .notification: return "通知"
        case .configuration: return "配置"
        case .network: return "网络"
        case .system: return "系统"
        case .userInput: return "用户输入"
        }
    }
}

/// 错误恢复选项
enum ErrorRecoveryOption: String, CaseIterable, Codable {
    case retry = "retry"
    case reset = "reset"
    case useDefault = "use_default"
    case ignore = "ignore"
    case restart = "restart"
    case contact = "contact"
    
    var displayName: String {
        switch self {
        case .retry: return "重试"
        case .reset: return "重置"
        case .useDefault: return "使用默认设置"
        case .ignore: return "忽略"
        case .restart: return "重启应用"
        case .contact: return "联系支持"
        }
    }
}

// MARK: - 具体错误类型

/// 薪资计算错误 (扩展现有的)
extension SalaryCalculatorError: AppError {
    var errorCode: String {
        switch self {
        case .invalidSettings: return "CALC_001"
        case .calculationFailed: return "CALC_002"
        case .timerCreationFailed: return "CALC_003"
        case .invalidTimeInterval: return "CALC_004"
        case .persistenceError: return "CALC_005"
        }
    }
    
    var severity: ErrorSeverity {
        switch self {
        case .invalidSettings: return .warning
        case .calculationFailed: return .error
        case .timerCreationFailed: return .error
        case .invalidTimeInterval: return .warning
        case .persistenceError: return .error
        }
    }
    
    var category: ErrorCategory {
        switch self {
        case .invalidSettings: return .configuration
        case .calculationFailed: return .calculation
        case .timerCreationFailed: return .system
        case .invalidTimeInterval: return .configuration
        case .persistenceError: return .persistence
        }
    }
    
    var userMessage: String {
        switch self {
        case .invalidSettings: return "工资设置有误，请检查您的工资配置"
        case .calculationFailed: return "工资计算遇到问题，正在尝试修复"
        case .timerCreationFailed: return "系统计时器异常，正在重新启动"
        case .invalidTimeInterval: return "时间设置异常，已重置为默认值"
        case .persistenceError: return "数据保存失败，请稍后再试"
        }
    }
    
    var technicalMessage: String {
        return self.localizedDescription
    }
    
    var recoveryOptions: [ErrorRecoveryOption] {
        switch self {
        case .invalidSettings: return [.reset, .useDefault]
        case .calculationFailed: return [.retry, .reset]
        case .timerCreationFailed: return [.retry, .restart]
        case .invalidTimeInterval: return [.useDefault]
        case .persistenceError: return [.retry, .ignore]
        }
    }
}

/// 数据持久化错误 (扩展现有的)
extension DataPersistenceError: AppError {
    var errorCode: String {
        switch self {
        case .encodingFailed: return "PERS_001"
        case .decodingFailed: return "PERS_002"
        case .corruptedData: return "PERS_003"
        case .saveOperationFailed: return "PERS_004"
        case .loadOperationFailed: return "PERS_005"
        case .migrationFailed: return "PERS_006"
        case .repairFailed: return "PERS_007"
        }
    }
    
    var severity: ErrorSeverity {
        switch self {
        case .encodingFailed: return .error
        case .decodingFailed: return .error
        case .corruptedData: return .critical
        case .saveOperationFailed: return .error
        case .loadOperationFailed: return .warning
        case .migrationFailed: return .critical
        case .repairFailed: return .error
        }
    }
    
    var category: ErrorCategory {
        return .persistence
    }
    
    var userMessage: String {
        switch self {
        case .encodingFailed: return "数据编码失败，请重试"
        case .decodingFailed: return "数据读取失败，将尝试恢复"
        case .corruptedData: return "数据已损坏，将重置为默认设置"
        case .saveOperationFailed: return "保存失败，请检查存储空间"
        case .loadOperationFailed: return "加载数据失败，将使用默认设置"
        case .migrationFailed: return "数据升级失败，将重置应用数据"
        case .repairFailed: return "数据修复失败，请联系支持"
        }
    }
    
    var technicalMessage: String {
        return self.localizedDescription
    }
    
    var recoveryOptions: [ErrorRecoveryOption] {
        switch self {
        case .encodingFailed: return [.retry, .useDefault]
        case .decodingFailed: return [.retry, .reset, .useDefault]
        case .corruptedData: return [.reset, .useDefault]
        case .saveOperationFailed: return [.retry, .ignore]
        case .loadOperationFailed: return [.retry, .useDefault]
        case .migrationFailed: return [.reset, .restart]
        case .repairFailed: return [.reset, .contact]
        }
    }
}

/// 通知错误
enum NotificationError: AppError {
    case permissionDenied
    case scheduleFailure(String)
    case contentCreationFailed(String)
    case deliveryFailed(String)
    
    var errorCode: String {
        switch self {
        case .permissionDenied: return "NOTIF_001"
        case .scheduleFailure: return "NOTIF_002"
        case .contentCreationFailed: return "NOTIF_003"
        case .deliveryFailed: return "NOTIF_004"
        }
    }
    
    var severity: ErrorSeverity {
        switch self {
        case .permissionDenied: return .warning
        case .scheduleFailure: return .error
        case .contentCreationFailed: return .error
        case .deliveryFailed: return .warning
        }
    }
    
    var category: ErrorCategory {
        return .notification
    }
    
    var userMessage: String {
        switch self {
        case .permissionDenied: return "通知权限未授权，请在设置中开启"
        case .scheduleFailure: return "通知发送失败，请稍后再试"
        case .contentCreationFailed: return "通知内容创建失败"
        case .deliveryFailed: return "通知发送失败"
        }
    }
    
    var technicalMessage: String {
        switch self {
        case .permissionDenied: return "Notification permission not granted"
        case .scheduleFailure(let message): return "Notification schedule failed: \(message)"
        case .contentCreationFailed(let message): return "Notification content creation failed: \(message)"
        case .deliveryFailed(let message): return "Notification delivery failed: \(message)"
        }
    }
    
    var recoveryOptions: [ErrorRecoveryOption] {
        switch self {
        case .permissionDenied: return [.contact]
        case .scheduleFailure: return [.retry, .ignore]
        case .contentCreationFailed: return [.retry, .useDefault]
        case .deliveryFailed: return [.retry, .ignore]
        }
    }
    
    var localizedDescription: String {
        return userMessage
    }
    
    var errorDescription: String? {
        return userMessage
    }
}

/// 系统错误
enum SystemError: AppError {
    case lowMemory
    case lowBattery(Double)
    case deviceCapabilityMissing(String)
    case osVersionIncompatible
    
    var errorCode: String {
        switch self {
        case .lowMemory: return "SYS_001"
        case .lowBattery(_): return "SYS_002"
        case .deviceCapabilityMissing: return "SYS_003"
        case .osVersionIncompatible: return "SYS_004"
        }
    }
    
    var severity: ErrorSeverity {
        switch self {
        case .lowMemory: return .warning
        case .lowBattery(_): return .info
        case .deviceCapabilityMissing: return .error
        case .osVersionIncompatible: return .critical
        }
    }
    
    var category: ErrorCategory {
        return .system
    }
    
    var userMessage: String {
        switch self {
        case .lowMemory: return "设备内存不足，已启用节能模式"
        case .lowBattery(let level): return "电量较低(\(Int(level * 100))%)，已优化性能以延长续航"
        case .deviceCapabilityMissing: return "设备不支持此功能"
        case .osVersionIncompatible: return "系统版本不兼容，请升级watchOS"
        }
    }
    
    var technicalMessage: String {
        switch self {
        case .lowMemory: return "Low memory detected"
        case .lowBattery(let level): return "Low battery level detected: \(level)"
        case .deviceCapabilityMissing(let capability): return "Device capability missing: \(capability)"
        case .osVersionIncompatible: return "OS version incompatible"
        }
    }
    
    var recoveryOptions: [ErrorRecoveryOption] {
        switch self {
        case .lowMemory: return [.restart, .contact]
        case .lowBattery(_): return [.ignore]
        case .deviceCapabilityMissing: return [.contact]
        case .osVersionIncompatible: return [.contact]
        }
    }
    
    var localizedDescription: String {
        return userMessage
    }
    
    var errorDescription: String? {
        return userMessage
    }
}

// MARK: - 错误日志记录

/// 错误日志条目
struct ErrorLogEntry: Codable, Identifiable {
    let id: UUID
    let timestamp: Date
    let errorCode: String
    let category: ErrorCategory
    let severity: ErrorSeverity
    let userMessage: String
    let technicalMessage: String
    let recoveryAction: ErrorRecoveryOption?
    let stackTrace: String?
    
    init(error: any AppError, recoveryAction: ErrorRecoveryOption? = nil, stackTrace: String? = nil) {
        self.id = UUID()
        self.timestamp = Date()
        self.errorCode = error.errorCode
        self.category = error.category
        self.severity = error.severity
        self.userMessage = error.userMessage
        self.technicalMessage = error.technicalMessage
        self.recoveryAction = recoveryAction
        self.stackTrace = stackTrace
    }
}

/// 错误日志管理器
class ErrorLogger {
    static let shared = ErrorLogger()
    
    private let logger = Logger(subsystem: "com.xzjlg.iwatch", category: "ErrorHandling")
    private let maxLogEntries = 100
    private var logEntries: [ErrorLogEntry] = []
    private let persistenceManager = DataPersistenceManager.shared
    
    private init() {
        loadErrorLogs()
    }
    
    /// 记录错误
    func logError(_ error: any AppError, recoveryAction: ErrorRecoveryOption? = nil) {
        let stackTrace = Thread.callStackSymbols.joined(separator: "\n")
        let entry = ErrorLogEntry(error: error, recoveryAction: recoveryAction, stackTrace: stackTrace)
        
        logEntries.append(entry)
        
        // 保持日志条目数量在限制内
        if logEntries.count > maxLogEntries {
            logEntries.removeFirst(logEntries.count - maxLogEntries)
        }
        
        // 记录到系统日志
        switch error.severity {
        case .info:
            logger.info("\(entry.errorCode): \(entry.technicalMessage)")
        case .warning:
            logger.warning("\(entry.errorCode): \(entry.technicalMessage)")
        case .error:
            logger.error("\(entry.errorCode): \(entry.technicalMessage)")
        case .critical:
            logger.critical("\(entry.errorCode): \(entry.technicalMessage)")
        }
        
        // 保存到持久化存储
        saveErrorLogs()
    }
    
    /// 获取错误日志
    func getErrorLogs() -> [ErrorLogEntry] {
        return logEntries.sorted { $0.timestamp > $1.timestamp }
    }
    
    /// 清理过期日志
    func cleanupOldLogs() {
        let oneWeekAgo = Date().addingTimeInterval(-7 * 24 * 60 * 60)
        logEntries.removeAll { $0.timestamp < oneWeekAgo }
        saveErrorLogs()
    }
    
    /// 获取统计信息
    func getErrorStatistics() -> [ErrorCategory: Int] {
        var stats: [ErrorCategory: Int] = [:]
        for entry in logEntries {
            stats[entry.category, default: 0] += 1
        }
        return stats
    }
    
    private func saveErrorLogs() {
        do {
            let data = try JSONEncoder().encode(logEntries)
            UserDefaults.standard.set(data, forKey: "error_logs")
        } catch {
            logger.error("保存错误日志失败: \(error.localizedDescription)")
        }
    }
    
    private func loadErrorLogs() {
        do {
            guard let data = UserDefaults.standard.data(forKey: "error_logs") else {
                logEntries = []
                return
            }
            logEntries = try JSONDecoder().decode([ErrorLogEntry].self, from: data)
        } catch {
            logger.warning("加载错误日志失败: \(error.localizedDescription)")
            logEntries = []
        }
    }
}

// MARK: - 全局错误处理管理器

/// 错误处理回调类型
typealias ErrorHandlerCallback = (any AppError) -> Void
typealias RecoveryActionCallback = (ErrorRecoveryOption, any AppError) -> Void

/// 全局错误处理管理器
class GlobalErrorHandler: ObservableObject {
    static let shared = GlobalErrorHandler()
    
    // MARK: - Published Properties
    
    /// 当前显示的错误
    @Published var currentError: (any AppError)?
    
    /// 是否显示错误弹窗
    @Published var showErrorAlert: Bool = false
    
    /// 错误历史列表
    @Published var errorHistory: [ErrorLogEntry] = []
    
    /// 错误统计信息
    @Published var errorStatistics: [ErrorCategory: Int] = [:]
    
    // MARK: - Private Properties
    
    private let errorLogger = ErrorLogger.shared
    private var errorHandlers: [ErrorCategory: ErrorHandlerCallback] = [:]
    private var recoveryHandlers: [ErrorRecoveryOption: RecoveryActionCallback] = [:]
    private var autoRecoveryEnabled: Bool = true
    private var criticalErrorThreshold: Int = 3
    private var criticalErrorCount: Int = 0
    
    // 恢复策略管理
    private var retryAttempts: [String: Int] = [:]
    private let maxRetryAttempts: Int = 3
    
    private init() {
        setupDefaultHandlers()
        setupDefaultRecoveryActions()
        loadErrorHistory()
    }
    
    // MARK: - Public Interface
    
    /// 处理错误的主要入口点
    func handleError(_ error: any AppError, autoRecover: Bool = true) {
        DispatchQueue.main.async { [weak self] in
            self?.processError(error, autoRecover: autoRecover)
        }
    }
    
    /// 注册错误处理器
    func registerErrorHandler(for category: ErrorCategory, handler: @escaping ErrorHandlerCallback) {
        errorHandlers[category] = handler
    }
    
    /// 注册恢复动作处理器
    func registerRecoveryHandler(for option: ErrorRecoveryOption, handler: @escaping RecoveryActionCallback) {
        recoveryHandlers[option] = handler
    }
    
    /// 执行恢复动作
    func executeRecoveryAction(_ option: ErrorRecoveryOption, for error: any AppError) {
        // 记录恢复动作
        errorLogger.logError(error, recoveryAction: option)
        
        // 执行注册的恢复处理器
        if let handler = recoveryHandlers[option] {
            handler(option, error)
        } else {
            // 执行默认恢复动作
            executeDefaultRecoveryAction(option, for: error)
        }
        
        // 清除当前错误
        clearCurrentError()
    }
    
    /// 清除当前错误
    func clearCurrentError() {
        currentError = nil
        showErrorAlert = false
    }
    
    /// 获取错误历史
    func getErrorHistory() -> [ErrorLogEntry] {
        return errorLogger.getErrorLogs()
    }
    
    /// 获取错误统计
    func getErrorStatistics() -> [ErrorCategory: Int] {
        return errorLogger.getErrorStatistics()
    }
    
    /// 清理错误历史
    func cleanupErrorHistory() {
        errorLogger.cleanupOldLogs()
        loadErrorHistory()
    }
    
    /// 设置自动恢复
    func setAutoRecoveryEnabled(_ enabled: Bool) {
        autoRecoveryEnabled = enabled
    }
    
    // MARK: - Private Methods
    
    private func processError(_ error: any AppError, autoRecover: Bool) {
        // 记录错误
        errorLogger.logError(error)
        
        // 更新统计
        updateErrorStatistics()
        
        // 检查是否需要自动恢复
        if autoRecover && autoRecoveryEnabled {
            if attemptAutoRecovery(for: error) {
                return // 自动恢复成功，不需要显示错误
            }
        }
        
        // 检查严重错误计数
        if error.severity == .critical {
            criticalErrorCount += 1
            if criticalErrorCount >= criticalErrorThreshold {
                handleCriticalErrorThreshold()
                return
            }
        }
        
        // 调用注册的错误处理器
        if let handler = errorHandlers[error.category] {
            handler(error)
        }
        
        // 设置当前错误并显示
        currentError = error
        showErrorAlert = true
    }
    
    private func attemptAutoRecovery(for error: any AppError) -> Bool {
        let errorKey = error.errorCode
        
        // 检查重试次数
        let attempts = retryAttempts[errorKey, default: 0]
        if attempts >= maxRetryAttempts {
            return false
        }
        
        // 尝试自动恢复
        for option in error.recoveryOptions {
            if isAutoRecoverable(option) {
                retryAttempts[errorKey] = attempts + 1
                executeRecoveryAction(option, for: error)
                return true
            }
        }
        
        return false
    }
    
    private func isAutoRecoverable(_ option: ErrorRecoveryOption) -> Bool {
        switch option {
        case .retry, .useDefault, .ignore:
            return true
        case .reset, .restart, .contact:
            return false
        }
    }
    
    private func executeDefaultRecoveryAction(_ option: ErrorRecoveryOption, for error: any AppError) {
        switch option {
        case .retry:
            // 默认重试逻辑，具体实现由各模块处理
            break
        case .reset:
            resetToDefaults()
        case .useDefault:
            applyDefaultSettings()
        case .ignore:
            // 忽略错误，不做任何操作
            break
        case .restart:
            requestApplicationRestart()
        case .contact:
            // 提供联系方式或反馈渠道
            break
        }
    }
    
    private func resetToDefaults() {
        // 重置应用到默认状态
        let persistenceManager = DataPersistenceManager.shared
        
        // 重置用户设置
        do {
            try persistenceManager.saveUserSettings(UserSettings.default)
        } catch {
            print("重置用户设置失败: \(error)")
        }
        
        // 清除目标数据
        do {
            let emptyGoals: [Goal] = []
            try persistenceManager.saveGoals(emptyGoals)
        } catch {
            print("清除目标数据失败: \(error)")
        }
        
        // 通知应用重新加载数据
        NotificationCenter.default.post(name: .appShouldReloadData, object: nil)
    }
    
    private func applyDefaultSettings() {
        // 应用默认设置，但保留用户数据
        let persistenceManager = DataPersistenceManager.shared
        
        do {
            try persistenceManager.saveUserSettings(UserSettings.default)
            NotificationCenter.default.post(name: .appShouldReloadSettings, object: nil)
        } catch {
            print("应用默认设置失败: \(error)")
        }
    }
    
    private func requestApplicationRestart() {
        // 在watchOS中，我们无法直接重启应用，但可以提示用户
        let restartError = SystemError.osVersionIncompatible
        currentError = restartError
        showErrorAlert = true
    }
    
    private func handleCriticalErrorThreshold() {
        // 达到严重错误阈值，强制重置应用
        resetToDefaults()
        
        // 创建特殊的严重错误提示
        let criticalError = SystemError.lowMemory
        currentError = criticalError
        showErrorAlert = true
        
        // 重置计数器
        criticalErrorCount = 0
    }
    
    private func updateErrorStatistics() {
        errorStatistics = errorLogger.getErrorStatistics()
    }
    
    private func loadErrorHistory() {
        errorHistory = errorLogger.getErrorLogs()
        updateErrorStatistics()
    }
    
    private func setupDefaultHandlers() {
        // 设置默认的错误处理器
        
        registerErrorHandler(for: .calculation) { error in
            print("处理计算错误: \(error.userMessage)")
        }
        
        registerErrorHandler(for: .persistence) { error in
            print("处理持久化错误: \(error.userMessage)")
        }
        
        registerErrorHandler(for: .notification) { error in
            print("处理通知错误: \(error.userMessage)")
        }
        
        registerErrorHandler(for: .system) { error in
            print("处理系统错误: \(error.userMessage)")
        }
        
        registerErrorHandler(for: .configuration) { error in
            print("处理配置错误: \(error.userMessage)")
        }
    }
    
    private func setupDefaultRecoveryActions() {
        // 设置默认的恢复动作处理器
        
        registerRecoveryHandler(for: .retry) { option, error in
            print("执行重试操作: \(error.errorCode)")
        }
        
        registerRecoveryHandler(for: .reset) { option, error in
            print("执行重置操作: \(error.errorCode)")
        }
        
        registerRecoveryHandler(for: .useDefault) { option, error in
            print("使用默认设置: \(error.errorCode)")
        }
        
        registerRecoveryHandler(for: .ignore) { option, error in
            print("忽略错误: \(error.errorCode)")
        }
    }
}

// MARK: - 通知扩展

extension Notification.Name {
    static let appShouldReloadData = Notification.Name("appShouldReloadData")
    static let appShouldReloadSettings = Notification.Name("appShouldReloadSettings")
    static let errorOccurred = Notification.Name("errorOccurred")
    static let errorRecovered = Notification.Name("errorRecovered")
}

// MARK: - 错误处理系统演示和测试

extension GlobalErrorHandler {
    
    /// 测试错误处理系统的完整性
    func testErrorHandlingSystem() {
        print("🧪 开始测试错误处理系统...")
        
        // 测试不同类型的错误
        let testErrors: [any AppError] = [
            SalaryCalculatorError.invalidSettings("测试无效设置"),
            DataPersistenceError.corruptedData("测试数据损坏"),
            NotificationError.permissionDenied,
            SystemError.lowBattery(0.05)
        ]
        
        for error in testErrors {
            print("测试错误: \(error.errorCode) - \(error.userMessage)")
            handleError(error, autoRecover: false)
        }
        
        print("✅ 错误处理系统测试完成")
    }
    
    /// 演示错误恢复流程
    func demonstrateErrorRecovery() {
        print("🔧 演示错误恢复流程...")
        
        // 模拟一个可以自动恢复的错误
        let recoverableError = DataPersistenceError.loadOperationFailed("模拟加载失败")
        handleError(recoverableError, autoRecover: true)
        
        // 模拟一个需要用户干预的错误
        let criticalError = DataPersistenceError.corruptedData("严重数据损坏")
        handleError(criticalError, autoRecover: false)
        
        print("✅ 错误恢复演示完成")
    }
} 