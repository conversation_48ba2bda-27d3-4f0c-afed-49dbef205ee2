//
//  Goal.swift
//  xzjlg-iwatch Watch App
//
//  Created by Ki<PERSON> on 2025/7/25.
//

import Foundation

/// 目标优先级枚举
enum GoalPriority: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case urgent = "urgent"
    
    var displayName: String {
        switch self {
        case .low:
            return "低优先级"
        case .medium:
            return "中优先级"
        case .high:
            return "高优先级"
        case .urgent:
            return "紧急"
        }
    }
    
    var sortOrder: Int {
        switch self {
        case .urgent:
            return 4
        case .high:
            return 3
        case .medium:
            return 2
        case .low:
            return 1
        }
    }
    
    var color: String {
        switch self {
        case .low:
            return "gray"
        case .medium:
            return "blue"
        case .high:
            return "orange"
        case .urgent:
            return "red"
        }
    }
}

/// 目标类别枚举
enum GoalCategory: String, CaseIterable, Codable {
    case electronics = "electronics"
    case travel = "travel"
    case education = "education"
    case fitness = "fitness"
    case fashion = "fashion"
    case food = "food"
    case entertainment = "entertainment"
    case investment = "investment"
    case emergency = "emergency"
    case gift = "gift"
    case home = "home"
    case other = "other"
    
    var displayName: String {
        switch self {
        case .electronics:
            return "数码产品"
        case .travel:
            return "旅行出游"
        case .education:
            return "学习充电"
        case .fitness:
            return "健身运动"
        case .fashion:
            return "时尚穿搭"
        case .food:
            return "美食享受"
        case .entertainment:
            return "娱乐休闲"
        case .investment:
            return "投资理财"
        case .emergency:
            return "应急基金"
        case .gift:
            return "礼物心意"
        case .home:
            return "居家生活"
        case .other:
            return "其他"
        }
    }
    
    var icon: String {
        switch self {
        case .electronics:
            return "iphone"
        case .travel:
            return "airplane"
        case .education:
            return "book"
        case .fitness:
            return "figure.run"
        case .fashion:
            return "tshirt"
        case .food:
            return "fork.knife"
        case .entertainment:
            return "gamecontroller"
        case .investment:
            return "chart.line.uptrend.xyaxis"
        case .emergency:
            return "cross.case"
        case .gift:
            return "gift"
        case .home:
            return "house"
        case .other:
            return "circle"
        }
    }
}

/// 目标结构体，用于追踪用户的购买目标
struct Goal: Identifiable, Codable, Equatable {
    /// 唯一标识符
    let id: UUID
    
    /// 目标名称
    let name: String
    
    /// 目标金额
    let targetAmount: Double
    
    /// 当前已积累金额
    var currentAmount: Double
    
    /// 最后更新时间
    var lastUpdated: Date
    
    /// 创建时间
    let createdAt: Date
    
    /// 目标优先级
    var priority: GoalPriority
    
    /// 目标类别
    var category: GoalCategory
    
    /// 目标描述
    var description: String?
    
    /// 预期完成日期
    var targetDate: Date?
    
    /// 是否启用自动分配
    var isAutoAllocationEnabled: Bool
    
    /// 分配权重 (0.1 - 2.0)
    private var _allocationWeight: Double = 1.0
    var allocationWeight: Double {
        get { _allocationWeight }
        set { _allocationWeight = max(0.1, min(2.0, newValue)) }
    }
    
    /// 自定义图标名称 (可选)
    var customIcon: String?
    
    /// 初始化方法
    init(
        name: String,
        targetAmount: Double,
        currentAmount: Double = 0.0,
        priority: GoalPriority = .medium,
        category: GoalCategory = .other,
        description: String? = nil,
        targetDate: Date? = nil,
        isAutoAllocationEnabled: Bool = true,
        allocationWeight: Double = 1.0,
        customIcon: String? = nil
    ) {
        self.id = UUID()
        self.name = name
        self.targetAmount = targetAmount
        self.currentAmount = currentAmount
        self.priority = priority
        self.category = category
        self.description = description
        self.targetDate = targetDate
        self.isAutoAllocationEnabled = isAutoAllocationEnabled
        self.customIcon = customIcon
        self.lastUpdated = Date()
        self.createdAt = Date()
        
        // 设置权重必须在所有存储属性初始化后
        self.allocationWeight = allocationWeight
    }
    
    /// 用于数据修复的完整初始化方法
    init(
        id: UUID,
        name: String,
        targetAmount: Double,
        currentAmount: Double,
        lastUpdated: Date,
        createdAt: Date,
        priority: GoalPriority,
        category: GoalCategory,
        description: String?,
        targetDate: Date?,
        isAutoAllocationEnabled: Bool,
        allocationWeight: Double,
        customIcon: String? = nil
    ) {
        self.id = id
        self.name = name
        self.targetAmount = targetAmount
        self.currentAmount = currentAmount
        self.lastUpdated = lastUpdated
        self.createdAt = createdAt
        self.priority = priority
        self.category = category
        self.description = description
        self.targetDate = targetDate
        self.isAutoAllocationEnabled = isAutoAllocationEnabled
        self.customIcon = customIcon
        self.allocationWeight = allocationWeight
    }
    
    /// 计算完成进度 (0.0 - 1.0)
    var progress: Double {
        guard targetAmount > 0 else { return 0.0 }
        return min(currentAmount / targetAmount, 1.0)
    }
    
    /// 计算完成百分比 (0 - 100)
    var progressPercentage: Int {
        return Int(progress * 100)
    }
    
    /// 是否已完成
    var isCompleted: Bool {
        return currentAmount >= targetAmount
    }
    
    /// 剩余需要的金额
    var remainingAmount: Double {
        return max(targetAmount - currentAmount, 0.0)
    }
    
    /// 获取显示图标
    var displayIcon: String {
        return customIcon ?? category.icon
    }
    
    /// 是否临近截止日期 (30天内)
    var isNearDeadline: Bool {
        guard let targetDate = targetDate else { return false }
        let daysUntilTarget = Calendar.current.dateComponents([.day], from: Date(), to: targetDate).day ?? 0
        return daysUntilTarget <= 30 && daysUntilTarget >= 0
    }
    
    /// 是否已过期
    var isOverdue: Bool {
        guard let targetDate = targetDate else { return false }
        return Date() > targetDate && !isCompleted
    }
    
    /// 更新当前金额
    mutating func updateAmount(_ newAmount: Double) {
        currentAmount = max(newAmount, 0.0)
        lastUpdated = Date()
    }
    
    /// 添加金额
    mutating func addAmount(_ amount: Double) {
        guard amount > 0 else { return }
        currentAmount += amount
        lastUpdated = Date()
    }
    
    /// 重置进度
    mutating func resetProgress() {
        currentAmount = 0.0
        lastUpdated = Date()
    }
    
    /// 预估完成日期
    func estimatedCompletionDate(dailyEarnings: Double) -> Date? {
        guard remainingAmount > 0, dailyEarnings > 0 else { return nil }
        let daysNeeded = remainingAmount / dailyEarnings
        return Calendar.current.date(byAdding: .day, value: Int(ceil(daysNeeded)), to: Date())
    }
    
    // MARK: - Formatted Properties
    
    /// 格式化的当前金额
    var formattedCurrentAmount: String {
        return formatCurrency(currentAmount)
    }
    
    /// 格式化的目标金额
    var formattedTargetAmount: String {
        return formatCurrency(targetAmount)
    }
    
    /// 格式化的剩余金额
    var formattedRemainingAmount: String {
        return formatCurrency(remainingAmount)
    }
    
    /// 格式化金额的私有方法
    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencySymbol = "¥"
        formatter.maximumFractionDigits = amount >= 1000 ? 0 : 2
        return formatter.string(from: NSNumber(value: amount)) ?? "¥0"
    }
}

// MARK: - Default Goals & Templates
extension Goal {
    /// 预设的默认目标
    static let defaultGoals: [(name: String, category: GoalCategory, suggestedAmount: Double)] = [
        ("iPhone 15 Pro", .electronics, 8999),
        ("日本旅行", .travel, 8000),
        ("新笔记本电脑", .electronics, 12000),
        ("健身会员卡", .fitness, 2000),
        ("在线课程", .education, 1500),
        ("美食探店基金", .food, 3000),
        ("新衣服", .fashion, 2000),
        ("生日礼物", .gift, 1000),
        ("应急基金", .emergency, 10000),
        ("家具升级", .home, 5000)
    ]
    
    /// 根据预设创建目标
    static func createFromTemplate(name: String, amount: Double? = nil) -> Goal? {
        guard let template = defaultGoals.first(where: { $0.name == name }) else {
            return nil
        }
        
        return Goal(
            name: template.name,
            targetAmount: amount ?? template.suggestedAmount,
            priority: template.category == .emergency ? .high : .medium,
            category: template.category
        )
    }
    
    /// 创建智能推荐目标
    static func createSmartGoal(
        category: GoalCategory,
        userIncome: Double
    ) -> Goal {
        let suggestions = getSmartSuggestions(for: category, income: userIncome)
        return Goal(
            name: suggestions.name,
            targetAmount: suggestions.amount,
            priority: suggestions.priority,
            category: category,
            description: suggestions.description,
            targetDate: suggestions.targetDate
        )
    }
    
    /// 获取智能建议
    private static func getSmartSuggestions(
        for category: GoalCategory,
        income: Double
    ) -> (name: String, amount: Double, priority: GoalPriority, description: String?, targetDate: Date?) {
        let monthlyIncome = income * 22 // 假设22个工作日
        
        switch category {
        case .emergency:
            return (
                name: "应急基金",
                amount: monthlyIncome * 3, // 3个月收入
                priority: .high,
                description: "建议储备3个月的收入作为应急基金",
                targetDate: Calendar.current.date(byAdding: .month, value: 6, to: Date())
            )
        case .electronics:
            let amount = min(monthlyIncome * 0.8, 15000) // 不超过月收入80%或15000
            return (
                name: "新数码产品",
                amount: amount,
                priority: .medium,
                description: "根据您的收入水平推荐的数码产品预算",
                targetDate: Calendar.current.date(byAdding: .month, value: 2, to: Date())
            )
        case .travel:
            let amount = monthlyIncome * 0.5 // 月收入50%
            return (
                name: "旅行基金",
                amount: amount,
                priority: .medium,
                description: "放松身心，探索世界",
                targetDate: Calendar.current.date(byAdding: .month, value: 3, to: Date())
            )
        default:
            return (
                name: category.displayName,
                amount: monthlyIncome * 0.3,
                priority: .medium,
                description: nil,
                targetDate: Calendar.current.date(byAdding: .month, value: 2, to: Date())
            )
        }
    }
}



// MARK: - Sorting & Filtering
extension Goal {
    /// 按优先级排序的比较函数
    static func sortByPriority(_ lhs: Goal, _ rhs: Goal) -> Bool {
        if lhs.priority.sortOrder != rhs.priority.sortOrder {
            return lhs.priority.sortOrder > rhs.priority.sortOrder
        }
        return lhs.createdAt < rhs.createdAt
    }
    
    /// 按进度排序的比较函数
    static func sortByProgress(_ lhs: Goal, _ rhs: Goal) -> Bool {
        if lhs.isCompleted != rhs.isCompleted {
            return !lhs.isCompleted && rhs.isCompleted
        }
        return lhs.progress > rhs.progress
    }
    
    /// 按截止日期排序的比较函数
    static func sortByDeadline(_ lhs: Goal, _ rhs: Goal) -> Bool {
        switch (lhs.targetDate, rhs.targetDate) {
        case (nil, nil):
            return lhs.createdAt < rhs.createdAt
        case (nil, _):
            return false
        case (_, nil):
            return true
        case let (date1?, date2?):
            return date1 < date2
        }
    }
}

// MARK: - Analytics
extension Goal {
    /// 获取目标分析数据
    var analytics: GoalAnalytics {
        let daysSinceCreated = Calendar.current.dateComponents([.day], from: createdAt, to: Date()).day ?? 0
        let averageDailyProgress = daysSinceCreated > 0 ? currentAmount / Double(daysSinceCreated) : 0
        
        return GoalAnalytics(
            daysActive: daysSinceCreated,
            averageDailyProgress: averageDailyProgress,
            completionRate: progress,
            isOnTrack: isOnTrack,
            estimatedDaysToCompletion: estimatedDaysToCompletion(averageDaily: averageDailyProgress)
        )
    }
    
    /// 是否按计划进行
    var isOnTrack: Bool {
        guard let targetDate = targetDate else { return true }
        let totalDays = Calendar.current.dateComponents([.day], from: createdAt, to: targetDate).day ?? 1
        let daysPassed = Calendar.current.dateComponents([.day], from: createdAt, to: Date()).day ?? 0
        let expectedProgress = Double(daysPassed) / Double(totalDays)
        
        return progress >= expectedProgress * 0.8 // 允许20%的偏差
    }
    
    /// 预估完成天数
    private func estimatedDaysToCompletion(averageDaily: Double) -> Int? {
        guard !isCompleted, averageDaily > 0 else { return nil }
        return Int(ceil(remainingAmount / averageDaily))
    }
}

// MARK: - Supporting Types
struct GoalAnalytics {
    let daysActive: Int
    let averageDailyProgress: Double
    let completionRate: Double
    let isOnTrack: Bool
    let estimatedDaysToCompletion: Int?
    
    var formattedAverageDailyProgress: String {
        return String(format: "¥%.2f", averageDailyProgress)
    }
}