//
//  GoalTracker.swift
//  xzjlg-iwatch Watch App
//
//  Created by AI Assistant on 2025/1/25.
//

import Foundation
import Combine

/// 目标追踪器 - 负责管理所有目标相关的逻辑
class GoalTracker: ObservableObject {
    // MARK: - Published Properties
    
    /// 目标列表
    @Published var goals: [Goal] = []
    
    // MARK: - Private Properties
    
    private let persistenceManager = DataPersistenceManager.shared
    
    // 回调闭包
    var onGoalCompleted: ((Goal) -> Void)?
    var onGoalsChanged: (([Goal]) -> Void)?
    var onMilestoneReached: ((Goal, Int) -> Void)?
    
    // MARK: - Initialization
    
    init() {
        loadGoals()
    }
    
    // MARK: - Public Goal Management Methods
    
    /// 添加新目标
    func addGoal(
        name: String, 
        targetAmount: Double,
        priority: GoalPriority = .medium,
        category: GoalCategory = .other,
        description: String? = nil,
        targetDate: Date? = nil,
        isAutoAllocationEnabled: Bool = true,
        allocationWeight: Double = 1.0
    ) {
        let newGoal = Goal(
            name: name,
            targetAmount: targetAmount,
            priority: priority,
            category: category,
            description: description,
            targetDate: targetDate,
            isAutoAllocationEnabled: isAutoAllocationEnabled,
            allocationWeight: allocationWeight
        )
        
        goals.append(newGoal)
        saveGoals()
    }
    
    /// 添加目标（使用Goal对象）
    func addGoal(_ goal: Goal) {
        goals.append(goal)
        saveGoals()
    }
    
    /// 删除目标
    func removeGoal(at index: Int) {
        print("GoalTracker: 删除目标索引 \(index), 当前目标数量: \(goals.count)")
        guard index >= 0 && index < goals.count else { 
            print("GoalTracker: 索引越界，取消删除 (index: \(index), count: \(goals.count))")
            return 
        }
        let goalName = goals[index].name
        print("GoalTracker: 执行删除操作，删除目标: \(goalName)")
        
        // 删除目标
        goals.remove(at: index)
        print("GoalTracker: 删除后目标数量: \(goals.count)")
        
        // 保存数据
        saveGoals()
        
        // 发送一次变化通知
        objectWillChange.send()
    }
    
    /// 根据ID删除目标
    func removeGoal(withId id: UUID) {
        goals.removeAll { $0.id == id }
        saveGoals()
    }
    
    /// 更新目标
    func updateGoal(at index: Int, with updatedGoal: Goal) {
        guard index < goals.count else { return }
        goals[index] = updatedGoal
        saveGoals()
    }
    
    /// 重置目标进度
    func resetGoal(at index: Int) {
        print("GoalTracker: 重置目标索引 \(index), 当前目标数量: \(goals.count)")
        guard index >= 0 && index < goals.count else { 
            print("GoalTracker: 索引越界，取消重置 (index: \(index), count: \(goals.count))")
            return 
        }
        let goalName = goals[index].name
        let oldAmount = goals[index].currentAmount
        print("GoalTracker: 执行重置操作，目标: \(goalName), 当前进度: \(oldAmount)")
        
        // 重置进度
        goals[index].resetProgress()
        print("GoalTracker: 重置后进度: \(goals[index].currentAmount)")
        
        // 保存数据
        saveGoals()
        
        // 发送一次变化通知
        objectWillChange.send()
    }
    
    /// 根据ID重置目标进度
    func resetGoal(withId id: UUID) {
        if let index = goals.firstIndex(where: { $0.id == id }) {
            goals[index].resetProgress()
            saveGoals()
        }
    }
    
    // MARK: - Income Distribution Methods
    
    /// 将收入分配到目标
    func distributeEarnings(_ totalEarnings: Double) {
        guard totalEarnings > 0 else { return }
        
        // 获取启用自动分配且未完成的目标
        let activeGoals = goals.enumerated().compactMap { index, goal in
            goal.isAutoAllocationEnabled && !goal.isCompleted ? (index, goal) : nil
        }
        
        guard !activeGoals.isEmpty else { return }
        
        // 记录分配前的进度，用于里程碑检测
        let previousProgress = goals.map { ($0.id, $0.progressPercentage) }
        
        // 使用智能分配算法
        let allocatedAmounts = calculateSmartAllocation(for: activeGoals, totalEarnings: totalEarnings)
        
        // 应用分配结果
        for (index, amount) in allocatedAmounts {
            goals[index].updateAmount(goals[index].currentAmount + amount)
        }
        
        saveGoals()
        
        // 检查是否有目标刚刚完成
        checkForNewlyCompletedGoals()
        
        // 检查进度里程碑
        checkProgressMilestones(previousProgress: previousProgress)
    }
    
    /// 检查进度里程碑
    private func checkProgressMilestones(previousProgress: [(UUID, Int)]) {
        for goal in goals {
            if let (_, oldProgress) = previousProgress.first(where: { $0.0 == goal.id }) {
                let currentProgress = goal.progressPercentage
                
                // 检查是否跨越了里程碑
                let milestones = [25, 50, 75]
                for milestone in milestones {
                    if currentProgress >= milestone && oldProgress < milestone {
                        // 触发里程碑回调
                        onMilestoneReached?(goal, milestone)
                    }
                }
            }
        }
    }
    
    /// 智能分配算法：考虑优先级、权重和剩余金额
    private func calculateSmartAllocation(
        for activeGoals: [(Int, Goal)], 
        totalEarnings: Double
    ) -> [(Int, Double)] {
        var allocations: [(Int, Double)] = []
        var remainingEarnings = totalEarnings
        
        // 第一步：按优先级分组
        let urgentGoals = activeGoals.filter { $0.1.priority == .urgent }
        let highGoals = activeGoals.filter { $0.1.priority == .high }
        let mediumGoals = activeGoals.filter { $0.1.priority == .medium }
        let lowGoals = activeGoals.filter { $0.1.priority == .low }
        
        // 第二步：优先满足紧急目标
        if !urgentGoals.isEmpty {
            let urgentAllocation = min(remainingEarnings * 0.6, // 最多60%给紧急目标
                                     urgentGoals.map { $0.1.remainingAmount }.reduce(0, +))
            
            let urgentAllocations = distributeByWeight(
                goals: urgentGoals,
                totalAmount: urgentAllocation
            )
            allocations.append(contentsOf: urgentAllocations)
            remainingEarnings -= urgentAllocation
        }
        
        // 第三步：分配给高优先级目标
        if !highGoals.isEmpty && remainingEarnings > 0 {
            let highAllocation = min(remainingEarnings * 0.5, // 剩余的50%给高优先级
                                   highGoals.map { $0.1.remainingAmount }.reduce(0, +))
            
            let highAllocations = distributeByWeight(
                goals: highGoals,
                totalAmount: highAllocation
            )
            allocations.append(contentsOf: highAllocations)
            remainingEarnings -= highAllocation
        }
        
        // 第四步：分配给其他目标
        let remainingGoals = mediumGoals + lowGoals
        if !remainingGoals.isEmpty && remainingEarnings > 0 {
            let remainingAllocations = distributeByWeight(
                goals: remainingGoals,
                totalAmount: remainingEarnings
            )
            allocations.append(contentsOf: remainingAllocations)
        }
        
        return allocations
    }
    
    /// 按权重分配金额
    private func distributeByWeight(
        goals: [(Int, Goal)], 
        totalAmount: Double
    ) -> [(Int, Double)] {
        guard !goals.isEmpty, totalAmount > 0 else { return [] }
        
        let totalWeight = goals.map { $0.1.allocationWeight }.reduce(0, +)
        
        return goals.map { index, goal in
            let allocation = totalAmount * (goal.allocationWeight / totalWeight)
            let maxAllocation = goal.remainingAmount
            return (index, min(allocation, maxAllocation))
        }
    }
    
    // MARK: - Goal Analysis Methods
    
    /// 获取所有目标
    func getAllGoals() -> [Goal] {
        return goals
    }
    
    /// 获取未完成的目标
    func getIncompleteGoals() -> [Goal] {
        return goals.filter { !$0.isCompleted }
    }
    
    /// 获取已完成的目标
    func getCompletedGoals() -> [Goal] {
        return goals.filter { $0.isCompleted }
    }
    
    /// 获取启用自动分配的目标
    func getAutoAllocationGoals() -> [Goal] {
        return goals.filter { $0.isAutoAllocationEnabled && !$0.isCompleted }
    }
    
    /// 获取过期的目标
    func getOverdueGoals() -> [Goal] {
        return goals.filter { $0.isOverdue }
    }
    
    /// 获取临近截止日期的目标
    func getNearDeadlineGoals() -> [Goal] {
        return goals.filter { $0.isNearDeadline }
    }
    
    /// 计算总目标金额
    func getTotalTargetAmount() -> Double {
        return goals.map { $0.targetAmount }.reduce(0, +)
    }
    
    /// 计算总当前金额
    func getTotalCurrentAmount() -> Double {
        return goals.map { $0.currentAmount }.reduce(0, +)
    }
    
    /// 计算总剩余金额
    func getTotalRemainingAmount() -> Double {
        return goals.map { $0.remainingAmount }.reduce(0, +)
    }
    
    /// 计算总体完成进度
    func getOverallProgress() -> Double {
        let totalTarget = getTotalTargetAmount()
        let totalCurrent = getTotalCurrentAmount()
        
        guard totalTarget > 0 else { return 0.0 }
        return min(totalCurrent / totalTarget, 1.0)
    }
    
    /// 获取目标统计信息
    func getGoalStatistics() -> GoalStatistics {
        return GoalStatistics(
            totalGoals: goals.count,
            completedGoals: getCompletedGoals().count,
            incompleteGoals: getIncompleteGoals().count,
            overdueGoals: getOverdueGoals().count,
            nearDeadlineGoals: getNearDeadlineGoals().count,
            totalTargetAmount: getTotalTargetAmount(),
            totalCurrentAmount: getTotalCurrentAmount(),
            totalRemainingAmount: getTotalRemainingAmount(),
            overallProgress: getOverallProgress()
        )
    }
    
    // MARK: - Private Helper Methods
    
    /// 检查新完成的目标并触发庆祝
    private func checkForNewlyCompletedGoals() {
        let newlyCompleted = goals.filter { goal in
            goal.isCompleted && 
            Calendar.current.isDateInToday(goal.lastUpdated)
        }
        
        for goal in newlyCompleted {
            onGoalCompleted?(goal)
        }
    }
    
    /// 通知目标变化
    private func notifyGoalsChanged() {
        onGoalsChanged?(goals)
    }
    
    // MARK: - Data Persistence
    
    /// 保存目标到持久化存储
    private func saveGoals() {
        do {
            try persistenceManager.saveGoals(goals)
        } catch {
            GlobalErrorHandler.shared.handleError(DataPersistenceError.saveOperationFailed("Goals"))
        }
    }
    
    /// 从持久化存储加载目标
    private func loadGoals() {
        goals = persistenceManager.loadGoals()
    }
    
    /// 清空所有目标
    func clearAllGoals() {
        goals.removeAll()
        saveGoals()
        notifyGoalsChanged()
    }
    
    /// 导出目标数据
    func exportGoals() -> Data? {
        return try? JSONEncoder().encode(goals)
    }
    
    /// 导入目标数据
    func importGoals(from data: Data) -> Bool {
        if let importedGoals = try? JSONDecoder().decode([Goal].self, from: data) {
            goals = importedGoals
            saveGoals()
            return true
        }
        return false
    }
}

// MARK: - Supporting Types

/// 目标统计信息
struct GoalStatistics {
    let totalGoals: Int
    let completedGoals: Int
    let incompleteGoals: Int
    let overdueGoals: Int
    let nearDeadlineGoals: Int
    let totalTargetAmount: Double
    let totalCurrentAmount: Double
    let totalRemainingAmount: Double
    let overallProgress: Double
    
    var completionRate: Double {
        guard totalGoals > 0 else { return 0.0 }
        return Double(completedGoals) / Double(totalGoals)
    }
    
    var overdueRate: Double {
        guard totalGoals > 0 else { return 0.0 }
        return Double(overdueGoals) / Double(totalGoals)
    }
}
