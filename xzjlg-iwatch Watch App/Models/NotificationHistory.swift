//
//  NotificationHistory.swift
//  xzjlg-iwatch Watch App
//
//  Created by Assistant on 2025/1/20.
//

import Foundation

/// 通知历史记录类型
enum NotificationHistoryType: String, CaseIterable, Codable {
    case salaryThreshold = "salary_threshold"
    case goalCompletion = "goal_completion"
    case milestone = "milestone"
    case workReminder = "work_reminder"
    case restReminder = "rest_reminder"
    case batteryOptimization = "battery_optimization"
    
    var displayName: String {
        switch self {
        case .salaryThreshold:
            return "工资阈值通知"
        case .goalCompletion:
            return "目标完成通知"
        case .milestone:
            return "里程碑通知"
        case .workReminder:
            return "工作提醒"
        case .restReminder:
            return "休息提醒"
        case .batteryOptimization:
            return "电池优化通知"
        }
    }
    
    var icon: String {
        switch self {
        case .salaryThreshold:
            return "dollarsign.circle"
        case .goalCompletion:
            return "checkmark.circle.fill"
        case .milestone:
            return "flag.fill"
        case .workReminder:
            return "briefcase"
        case .restReminder:
            return "moon.fill"
        case .batteryOptimization:
            return "battery.25"
        }
    }
}

/// 通知历史记录结构体
struct NotificationHistory: Identifiable, Codable, Equatable {
    /// 唯一标识符
    let id: UUID
    
    /// 通知类型
    let type: NotificationHistoryType
    
    /// 通知内容
    var content: String
    
    /// 通知标题
    let title: String
    
    /// 发送时间戳
    var timestamp: Date
    
    /// 是否已读
    var isRead: Bool
    
    /// 关联的金额（如果适用）
    let associatedAmount: Double?
    
    /// 关联的目标ID（如果适用）
    let associatedGoalId: UUID?
    
    /// 通知优先级
    let priority: HistoryPriority
    
    /// 额外的元数据
    var metadata: [String: String]
    
    /// 初始化方法
    init(
        type: NotificationHistoryType,
        title: String,
        content: String,
        timestamp: Date = Date(),
        isRead: Bool = false,
        associatedAmount: Double? = nil,
        associatedGoalId: UUID? = nil,
        priority: HistoryPriority = .normal,
        metadata: [String: String] = [:]
    ) {
        self.id = UUID()
        self.type = type
        self.title = title
        self.content = content
        self.timestamp = timestamp
        self.isRead = isRead
        self.associatedAmount = associatedAmount
        self.associatedGoalId = associatedGoalId
        self.priority = priority
        self.metadata = metadata
    }
    
    /// 标记为已读
    mutating func markAsRead() {
        isRead = true
    }
    
    /// 更新内容
    mutating func updateContent(_ newContent: String) {
        content = newContent
    }
    
    /// 获取格式化的时间
    var formattedTime: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        return formatter.string(from: timestamp)
    }
    
    /// 获取格式化的日期
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: timestamp)
    }
    
    /// 获取相对时间描述
    var relativeTimeDescription: String {
        let now = Date()
        let timeInterval = now.timeIntervalSince(timestamp)
        
        if timeInterval < 60 {
            return "刚刚"
        } else if timeInterval < 3600 {
            let minutes = Int(timeInterval / 60)
            return "\(minutes)分钟前"
        } else if timeInterval < 86400 {
            let hours = Int(timeInterval / 3600)
            return "\(hours)小时前"
        } else {
            let days = Int(timeInterval / 86400)
            if days == 1 {
                return "昨天"
            } else if days < 7 {
                return "\(days)天前"
            } else {
                return formattedDate
            }
        }
    }
    
    /// 获取格式化的关联金额
    var formattedAssociatedAmount: String? {
        guard let amount = associatedAmount else { return nil }
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencySymbol = "¥"
        formatter.maximumFractionDigits = amount >= 1000 ? 0 : 2
        return formatter.string(from: NSNumber(value: amount))
    }
    
    /// 是否是今天的通知
    var isToday: Bool {
        return Calendar.current.isDateInToday(timestamp)
    }
    
    /// 是否是本周的通知
    var isThisWeek: Bool {
        let calendar = Calendar.current
        let now = Date()
        guard let weekInterval = calendar.dateInterval(of: .weekOfYear, for: now) else {
            return false
        }
        return weekInterval.contains(timestamp)
    }
}

/// 历史记录优先级枚举
enum HistoryPriority: String, CaseIterable, Codable {
    case low = "low"
    case normal = "normal"
    case high = "high"
    case urgent = "urgent"
    
    var displayName: String {
        switch self {
        case .low:
            return "低"
        case .normal:
            return "普通"
        case .high:
            return "高"
        case .urgent:
            return "紧急"
        }
    }
    
    var sortOrder: Int {
        switch self {
        case .urgent:
            return 4
        case .high:
            return 3
        case .normal:
            return 2
        case .low:
            return 1
        }
    }
}

// MARK: - NotificationHistory Extensions
extension NotificationHistory {
    /// 创建工资阈值通知历史
    static func createSalaryThresholdHistory(
        amount: Double,
        percentage: Int,
        dailyTarget: Double
    ) -> NotificationHistory {
        let title = "工资阈值达成"
        let content = "恭喜！今日收入已达到 \(percentage)%（¥\(String(format: "%.0f", amount))）"
        
        return NotificationHistory(
            type: .salaryThreshold,
            title: title,
            content: content,
            associatedAmount: amount,
            priority: percentage >= 100 ? .high : .normal,
            metadata: [
                "percentage": "\(percentage)",
                "dailyTarget": "\(dailyTarget)"
            ]
        )
    }
    
    /// 创建目标完成通知历史
    static func createGoalCompletionHistory(
        goalName: String,
        amount: Double,
        goalId: UUID
    ) -> NotificationHistory {
        let title = "目标达成"
        let content = "🎉 恭喜完成目标「\(goalName)」！"
        
        return NotificationHistory(
            type: .goalCompletion,
            title: title,
            content: content,
            associatedAmount: amount,
            associatedGoalId: goalId,
            priority: .high,
            metadata: [
                "goalName": goalName
            ]
        )
    }
    
    /// 创建里程碑通知历史
    static func createMilestoneHistory(
        goalName: String,
        percentage: Int,
        currentAmount: Double,
        goalId: UUID
    ) -> NotificationHistory {
        let title = "目标进度更新"
        let content = "目标「\(goalName)」已完成 \(percentage)%！"
        
        return NotificationHistory(
            type: .milestone,
            title: title,
            content: content,
            associatedAmount: currentAmount,
            associatedGoalId: goalId,
            priority: .normal,
            metadata: [
                "goalName": goalName,
                "percentage": "\(percentage)"
            ]
        )
    }
    
    /// 创建工作提醒通知历史
    static func createWorkReminderHistory(message: String) -> NotificationHistory {
        return NotificationHistory(
            type: .workReminder,
            title: "工作提醒",
            content: message,
            priority: .normal
        )
    }
    
    /// 创建休息提醒通知历史
    static func createRestReminderHistory(message: String) -> NotificationHistory {
        return NotificationHistory(
            type: .restReminder,
            title: "休息提醒",
            content: message,
            priority: .normal
        )
    }
    
    /// 创建电池优化通知历史
    static func createBatteryOptimizationHistory(
        batteryLevel: Float,
        optimizationLevel: String
    ) -> NotificationHistory {
        let title = "电池优化"
        let content = "电量 \(Int(batteryLevel * 100))%，已切换到\(optimizationLevel)"
        
        return NotificationHistory(
            type: .batteryOptimization,
            title: title,
            content: content,
            priority: .low,
            metadata: [
                "batteryLevel": "\(batteryLevel)",
                "optimizationLevel": optimizationLevel
            ]
        )
    }
}

// MARK: - Collection Extensions
extension Array where Element == NotificationHistory {
    /// 按时间排序（最新的在前）
    func sortedByTime() -> [NotificationHistory] {
        return sorted { $0.timestamp > $1.timestamp }
    }
    
    /// 按优先级排序
    func sortedByPriority() -> [NotificationHistory] {
        return sorted { lhs, rhs in
            if lhs.priority.sortOrder != rhs.priority.sortOrder {
                return lhs.priority.sortOrder > rhs.priority.sortOrder
            }
            return lhs.timestamp > rhs.timestamp
        }
    }
    
    /// 筛选未读通知
    var unreadNotifications: [NotificationHistory] {
        return filter { !$0.isRead }
    }
    
    /// 筛选今天的通知
    var todayNotifications: [NotificationHistory] {
        return filter { $0.isToday }
    }
    
    /// 筛选本周的通知
    var thisWeekNotifications: [NotificationHistory] {
        return filter { $0.isThisWeek }
    }
    
    /// 按类型分组
    func groupedByType() -> [NotificationHistoryType: [NotificationHistory]] {
        return Dictionary(grouping: self) { $0.type }
    }
    
    /// 按日期分组
    func groupedByDate() -> [String: [NotificationHistory]] {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        
        return Dictionary(grouping: self) { notification in
            formatter.string(from: notification.timestamp)
        }
    }
    
    /// 获取统计信息
    var statistics: NotificationStatistics {
        let total = count
        let unread = unreadNotifications.count
        let today = todayNotifications.count
        let thisWeek = thisWeekNotifications.count
        
        let typeCount = groupedByType().mapValues { $0.count }
        let priorityCount = Dictionary(grouping: self) { $0.priority }
            .mapValues { $0.count }
        
        return NotificationStatistics(
            total: total,
            unread: unread,
            today: today,
            thisWeek: thisWeek,
            byType: typeCount,
            byPriority: priorityCount
        )
    }
}

// MARK: - Supporting Types
struct NotificationStatistics {
    let total: Int
    let unread: Int
    let today: Int
    let thisWeek: Int
    let byType: [NotificationHistoryType: Int]
    let byPriority: [HistoryPriority: Int]
    
    var readRate: Double {
        guard total > 0 else { return 0.0 }
        return Double(total - unread) / Double(total)
    }
    
    var todayRate: Double {
        guard total > 0 else { return 0.0 }
        return Double(today) / Double(total)
    }
}