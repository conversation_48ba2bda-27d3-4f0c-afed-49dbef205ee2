//
//  NotificationManager.swift
//  xzjlg-iwatch Watch App
//
//  Created by Assistant on 2025/1/20.
//

import Foundation
import UserNotifications
import WatchKit

/// 通知类型枚举
enum NotificationType: String, CaseIterable, Codable {
    case salaryThreshold = "salary_threshold"
    case goalCompletion = "goal_completion"
    case milestoneReached = "milestone_reached"
    case workReminder = "work_reminder"
    case restReminder = "rest_reminder"
    
    var identifier: String {
        return self.rawValue
    }
}

/// 通知优先级
enum NotificationPriority: Int, Comparable {
    case low = 1
    case medium = 2
    case high = 3
    case critical = 4
    
    static func < (lhs: NotificationPriority, rhs: NotificationPriority) -> Bool {
        return lhs.rawValue < rhs.rawValue
    }
}

// NotificationSettings现在在UserSettings.swift中定义

/// 智能通知管理器
class NotificationManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var settings: NotificationSettings = .default
    @Published var permissionGranted: Bool = false
    
    // MARK: - Private Properties
    
    private var sentThresholds: Set<Int> = []
    private var lastNotificationTimes: [NotificationType: Date] = [:]
    private var notificationQueue: [PendingNotification] = []
    private var notificationHistory: [NotificationHistory] = []
    
    private let persistenceManager = DataPersistenceManager.shared
    
    // MARK: - Initialization
    
    init() {
        loadSettings()
        loadNotificationHistory()
        requestNotificationPermission()
        setupNotificationDelegate()
    }
    
    // MARK: - Permission Management
    
    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { [weak self] granted, error in
            DispatchQueue.main.async {
                self?.permissionGranted = granted
            }
            
            if granted {
                print("通知权限已获得")
            } else if error != nil {
                GlobalErrorHandler.shared.handleError(NotificationError.permissionDenied)
            }
        }
    }
    
    private func setupNotificationDelegate() {
        UNUserNotificationCenter.current().delegate = NotificationDelegate.shared
    }
    
    // MARK: - Core Notification Methods
    
    /// 发送工资阈值通知
    func sendSalaryThresholdNotification(amount: Double, percentage: Int, dailyTarget: Double) {
        guard settings.isEnabled && settings.salaryThresholdEnabled else { return }
        guard !sentThresholds.contains(percentage) else { return }
        guard canSendNotification(type: .salaryThreshold) else { return }
        
        sentThresholds.insert(percentage)
        
        let content = createSalaryThresholdContent(amount: amount, percentage: percentage, dailyTarget: dailyTarget)
        let notification = PendingNotification(
            type: .salaryThreshold,
            content: content,
            priority: getThresholdPriority(percentage: percentage),
            delay: 0.5
        )
        
        scheduleNotification(notification)
        recordNotification(type: .salaryThreshold, content: content.body)
    }
    
    /// 发送目标完成通知
    func sendGoalCompletionNotification(goalName: String, amount: Double) {
        guard settings.isEnabled && settings.goalCompletionEnabled else { return }
        guard canSendNotification(type: .goalCompletion) else { return }
        
        let content = createGoalCompletionContent(goalName: goalName, amount: amount)
        let notification = PendingNotification(
            type: .goalCompletion,
            content: content,
            priority: .critical,
            delay: 0.5
        )
        
        scheduleNotification(notification)
        recordNotification(type: .goalCompletion, content: content.body)
    }
    
    /// 发送里程碑达成通知
    func sendMilestoneNotification(goalName: String, percentage: Int, currentAmount: Double) {
        guard settings.isEnabled && settings.milestoneEnabled else { return }
        guard canSendNotification(type: .milestoneReached) else { return }
        
        let content = createMilestoneContent(goalName: goalName, percentage: percentage, currentAmount: currentAmount)
        let notification = PendingNotification(
            type: .milestoneReached,
            content: content,
            priority: .high,
            delay: 1.0
        )
        
        scheduleNotification(notification)
        recordNotification(type: .milestoneReached, content: content.body)
    }
    
    /// 发送工作提醒通知
    func sendWorkReminder(message: String) {
        guard settings.isEnabled && settings.workReminderEnabled else { return }
        guard canSendNotification(type: .workReminder, respectQuietHours: true) else { return }
        
        let content = createWorkReminderContent(message: message)
        let notification = PendingNotification(
            type: .workReminder,
            content: content,
            priority: .medium,
            delay: 0
        )
        
        scheduleNotification(notification)
        recordNotification(type: .workReminder, content: content.body)
    }
    
    // MARK: - Content Creation
    
    private func createSalaryThresholdContent(amount: Double, percentage: Int, dailyTarget: Double) -> UNMutableNotificationContent {
        let content = UNMutableNotificationContent()
        
        let title = getThresholdTitle(percentage: percentage)
        let emoji = getThresholdEmoji(percentage: percentage)
        let encouragement = getThresholdEncouragement(percentage: percentage)
        
        content.title = "\(emoji) \(title)"
        content.body = String(format: "已赚取 ¥%.0f (%.0f%%) / ¥%.0f\n%@", amount, Double(percentage), dailyTarget, encouragement)
        content.sound = getThresholdSound(percentage: percentage)
        content.categoryIdentifier = "SALARY_THRESHOLD"
        content.userInfo = [
            "type": NotificationType.salaryThreshold.rawValue,
            "percentage": percentage,
            "amount": amount
        ]
        
        return content
    }
    
    private func createGoalCompletionContent(goalName: String, amount: Double) -> UNMutableNotificationContent {
        let content = UNMutableNotificationContent()
        content.title = "🎉 目标达成！"
        content.body = "恭喜！目标「\(goalName)」已完成\n金额：¥\(Int(amount))\n\n又朝梦想迈进了一步！"
        content.sound = .default
        content.categoryIdentifier = "GOAL_COMPLETION"
        content.userInfo = [
            "type": NotificationType.goalCompletion.rawValue,
            "goalName": goalName,
            "amount": amount
        ]
        
        return content
    }
    
    private func createMilestoneContent(goalName: String, percentage: Int, currentAmount: Double) -> UNMutableNotificationContent {
        let content = UNMutableNotificationContent()
        content.title = "🚀 里程碑达成"
        content.body = "目标「\(goalName)」进度已达到 \(percentage)%\n当前金额：¥\(Int(currentAmount))\n\n继续加油，胜利在望！"
        content.sound = .default
        content.categoryIdentifier = "MILESTONE"
        content.userInfo = [
            "type": NotificationType.milestoneReached.rawValue,
            "goalName": goalName,
            "percentage": percentage
        ]
        
        return content
    }
    
    private func createWorkReminderContent(message: String) -> UNMutableNotificationContent {
        let content = UNMutableNotificationContent()
        content.title = "💼 工作提醒"
        content.body = message
        content.sound = .default
        content.categoryIdentifier = "WORK_REMINDER"
        content.userInfo = [
            "type": NotificationType.workReminder.rawValue
        ]
        
        return content
    }
    
    // MARK: - Helper Methods
    
    private func getThresholdTitle(percentage: Int) -> String {
        switch percentage {
        case 25: return "起步阶段"
        case 50: return "半程冲刺"
        case 75: return "最后冲刺"
        case 100: return "今日目标达成"
        default: return "进度更新"
        }
    }
    
    private func getThresholdEmoji(percentage: Int) -> String {
        switch percentage {
        case 25: return "🌱"
        case 50: return "💪"
        case 75: return "🔥"
        case 100: return "🎯"
        default: return "📈"
        }
    }
    
    private func getThresholdEncouragement(percentage: Int) -> String {
        let encouragements = [
            25: ["起步就是胜利！", "良好的开始！", "步入正轨！"],
            50: ["已经过半了！", "继续保持！", "加速冲刺！"],
            75: ["即将胜利！", "最后一搏！", "冲刺阶段！"],
            100: ["完美收官！", "今日目标达成！", "太棒了！"]
        ]
        
        return encouragements[percentage]?.randomElement() ?? "继续加油！"
    }
    
    private func getThresholdPriority(percentage: Int) -> NotificationPriority {
        switch percentage {
        case 25: return .low
        case 50: return .medium
        case 75: return .high
        case 100: return .critical
        default: return .medium
        }
    }
    
    private func getThresholdSound(percentage: Int) -> UNNotificationSound {
        switch percentage {
        case 100: return .default
        default: return .default
        }
    }
    
    // MARK: - Notification Scheduling
    
    private func scheduleNotification(_ notification: PendingNotification) {
        guard permissionGranted else {
            GlobalErrorHandler.shared.handleError(NotificationError.permissionDenied)
            return
        }
        
        // 电池优化检查
        if settings.batteryOptimizationEnabled && shouldDelayForBattery(priority: notification.priority) {
            queueNotification(notification)
            return
        }
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: max(notification.delay, 0.1), repeats: false)
        let request = UNNotificationRequest(
            identifier: "\(notification.type.identifier)_\(UUID().uuidString)",
            content: notification.content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                GlobalErrorHandler.shared.handleError(NotificationError.scheduleFailure(error.localizedDescription))
            } else {
                print("通知已调度: \(notification.type.identifier)")
            }
        }
        
        lastNotificationTimes[notification.type] = Date()
    }
    
    private func queueNotification(_ notification: PendingNotification) {
        notificationQueue.append(notification)
        // 可以实现延迟队列处理逻辑
    }
    
    // MARK: - Notification Filtering
    
    private func canSendNotification(type: NotificationType, respectQuietHours: Bool = false) -> Bool {
        // 检查通知设置
        guard settings.isEnabled else { return false }
        
        // 检查安静时间
        if respectQuietHours && isInQuietHours() {
            return false
        }
        
        // 检查通知频率限制
        if hasExceededRateLimit(type: type) {
            return false
        }
        
        return true
    }
    
    private func isInQuietHours() -> Bool {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: Date())
        
        if settings.quietHoursStart < settings.quietHoursEnd {
            return hour >= settings.quietHoursStart && hour < settings.quietHoursEnd
        } else {
            return hour >= settings.quietHoursStart || hour < settings.quietHoursEnd
        }
    }
    
    private func hasExceededRateLimit(type: NotificationType) -> Bool {
        guard let lastTime = lastNotificationTimes[type] else { return false }
        
        let minimumInterval: TimeInterval
        switch type {
        case .salaryThreshold: minimumInterval = 300 // 5分钟
        case .goalCompletion: minimumInterval = 10   // 10秒
        case .milestoneReached: minimumInterval = 60 // 1分钟
        case .workReminder: minimumInterval = 3600   // 1小时
        case .restReminder: minimumInterval = 1800   // 30分钟
        }
        
        return Date().timeIntervalSince(lastTime) < minimumInterval
    }
    
    // MARK: - Battery Optimization
    
    private func shouldDelayForBattery(priority: NotificationPriority) -> Bool {
        guard settings.batteryOptimizationEnabled else { return false }
        
        let batteryLevel = WKInterfaceDevice.current().batteryLevel
        
        if batteryLevel < 0.2 && priority < .critical {
            return true
        }
        
        if batteryLevel < 0.5 && priority < .high {
            // 随机延迟低优先级通知
            return Bool.random()
        }
        
        return false
    }
    
    // MARK: - Daily Reset
    
    func resetDailyNotifications() {
        sentThresholds.removeAll()
        lastNotificationTimes.removeAll()
        print("每日通知状态已重置")
    }
    
    // MARK: - History Management
    
    /// 转换NotificationType为NotificationHistoryType
    private func convertToHistoryType(_ type: NotificationType) -> NotificationHistoryType {
        switch type {
        case .salaryThreshold:
            return .salaryThreshold
        case .goalCompletion:
            return .goalCompletion
        case .milestoneReached:
            return .milestone
        case .workReminder:
            return .workReminder
        case .restReminder:
            return .restReminder
        }
    }
    
    /// 获取通知标题
    private func getNotificationTitle(for type: NotificationHistoryType) -> String {
        switch type {
        case .salaryThreshold:
            return "工资通知"
        case .goalCompletion:
            return "目标完成"
        case .milestone:
            return "里程碑"
        case .workReminder:
            return "工作提醒"
        case .restReminder:
            return "休息提醒"
        case .batteryOptimization:
            return "电池优化"
        }
    }
    
    private func recordNotification(type: NotificationType, content: String) {
        let historyType = convertToHistoryType(type)
        let record = NotificationHistory(
            type: historyType,
            title: getNotificationTitle(for: historyType),
            content: content,
            timestamp: Date(),
            isRead: false,
            associatedAmount: nil,
            associatedGoalId: nil,
            priority: .normal,
            metadata: [:]
        )
        
        notificationHistory.append(record)
        
        // 保持最近100条记录
        if notificationHistory.count > 100 {
            notificationHistory.removeFirst(notificationHistory.count - 100)
        }
        
        saveNotificationHistory()
    }
    
    func getNotificationHistory() -> [NotificationHistory] {
        return notificationHistory.sorted { $0.timestamp > $1.timestamp }
    }
    
    // MARK: - Settings Management
    
    func updateSettings(_ newSettings: NotificationSettings) {
        settings = newSettings
        saveSettings()
    }
    
    private func saveSettings() {
        do {
            try persistenceManager.saveNotificationSettings(settings)
        } catch {
            GlobalErrorHandler.shared.handleError(DataPersistenceError.saveOperationFailed("NotificationSettings"))
        }
    }
    
    private func loadSettings() {
        settings = persistenceManager.loadNotificationSettings()
    }
    
    private func saveNotificationHistory() {
        do {
            try persistenceManager.saveNotificationHistory(notificationHistory)
        } catch {
            GlobalErrorHandler.shared.handleError(DataPersistenceError.saveOperationFailed("NotificationHistory"))
        }
    }
    
    private func loadNotificationHistory() {
        notificationHistory = persistenceManager.loadNotificationHistory()
    }
}

// MARK: - Supporting Types

private struct PendingNotification {
    let type: NotificationType
    let content: UNMutableNotificationContent
    let priority: NotificationPriority
    let delay: TimeInterval
}

// MARK: - Notification Delegate

private class NotificationDelegate: NSObject, UNUserNotificationCenterDelegate {
    static let shared = NotificationDelegate()
    
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        // 在应用运行时也显示通知
        completionHandler([.banner, .sound])
    }
    
    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        // 处理通知点击事件
        let userInfo = response.notification.request.content.userInfo
        if let typeString = userInfo["type"] as? String {
            print("用户点击了通知: \(typeString)")
        }
        completionHandler()
    }
} 