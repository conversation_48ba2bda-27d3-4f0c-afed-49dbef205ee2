//
//  OnboardingManager.swift
//  xzjlg-iwatch Watch App
//
//  Created by Assistant on 2025/1/20.
//

import Foundation
import SwiftUI

/// 引导步骤类型
enum OnboardingStep: String, CaseIterable, Codable {
    case welcome = "welcome"
    case salarySettings = "salary_settings"
    case goalSetup = "goal_setup"
    case notifications = "notifications"
    case character = "character"
    case complete = "complete"
    
    var title: String {
        switch self {
        case .welcome: return "欢迎使用"
        case .salarySettings: return "设置薪资"
        case .goalSetup: return "添加目标"
        case .notifications: return "通知设置"
        case .character: return "认识小福狸"
        case .complete: return "设置完成"
        }
    }
    
    var description: String {
        switch self {
        case .welcome: return "薪资奖励官让您实时追踪收入，激励工作热情"
        case .salarySettings: return "设置您的月薪和工作时间，开始计算实时收入"
        case .goalSetup: return "添加您的第一个储蓄目标，让收入更有意义"
        case .notifications: return "开启通知，及时了解收入里程碑"
        case .character: return "小福狸是您的收入助手，会根据收入状态变化表情"
        case .complete: return "恭喜！基础设置已完成。点击「开始使用」将跳转到设置页面，您可以进行更详细的个性化配置"
        }
    }
    
    var iconName: String {
        switch self {
        case .welcome: return "hand.wave.fill"
        case .salarySettings: return "dollarsign.circle.fill"
        case .goalSetup: return "target"
        case .notifications: return "bell.fill"
        case .character: return "heart.fill"
        case .complete: return "checkmark.circle.fill"
        }
    }
    
    var iconColor: Color {
        switch self {
        case .welcome: return .yellow
        case .salarySettings: return .green
        case .goalSetup: return .blue
        case .notifications: return .orange
        case .character: return .pink
        case .complete: return .green
        }
    }
}

/// 引导进度状态
struct OnboardingProgress: Codable {
    var currentStep: OnboardingStep
    var completedSteps: Set<OnboardingStep>
    var isCompleted: Bool
    var startedAt: Date
    var completedAt: Date?
    
    init() {
        self.currentStep = .welcome
        self.completedSteps = []
        self.isCompleted = false
        self.startedAt = Date()
        self.completedAt = nil
    }
}

/// 引导提示信息
struct OnboardingTip: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let iconName: String
    let iconColor: Color
}

/// 首次使用引导管理器
class OnboardingManager: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = OnboardingManager()
    
    // MARK: - Published Properties
    
    @Published var progress: OnboardingProgress = OnboardingProgress()
    @Published var showOnboarding: Bool = false
    @Published var currentStepIndex: Int = 0
    
    // MARK: - Constants
    
    let allSteps = OnboardingStep.allCases
    
    // MARK: - Initialization
    
    private init() {
        loadProgress()
        checkShouldShowOnboarding()
    }
    
    // MARK: - Public Interface
    
    /// 开始引导流程
    func startOnboarding() {
        progress = OnboardingProgress()
        showOnboarding = true
        currentStepIndex = 0
        saveProgress()
        
        // 触发欢迎触觉反馈
        UserExperienceManager.shared.triggerHapticFeedback(.notification)
    }
    
    /// 下一步
    func nextStep() {
        // 标记当前步骤为完成
        progress.completedSteps.insert(progress.currentStep)
        
        // 检查是否已经在最后一步
        if currentStepIndex == allSteps.count - 1 {
            // 在最后一步，点击按钮表示完成引导
            completeOnboarding()
            return
        }
        
        // 移动到下一步
        currentStepIndex += 1
        progress.currentStep = allSteps[currentStepIndex]
        
        saveProgress()
        
        // 触发步骤完成反馈
        UserExperienceManager.shared.triggerHapticFeedback(.success)
    }
    
    /// 上一步
    func previousStep() {
        if currentStepIndex > 0 {
            currentStepIndex -= 1
            progress.currentStep = allSteps[currentStepIndex]
            saveProgress()
        }
    }
    
    /// 跳转到指定步骤
    func goToStep(_ step: OnboardingStep) {
        if let index = allSteps.firstIndex(of: step) {
            currentStepIndex = index
            progress.currentStep = step
            saveProgress()
        }
    }
    
    /// 完成引导
    func completeOnboarding() {
        progress.isCompleted = true
        progress.completedAt = Date()
        showOnboarding = false
        saveProgress()
        
        // 触发完成反馈
        UserExperienceManager.shared.triggerHapticFeedback(.goalComplete)
        
        // 发送完成通知
        NotificationCenter.default.post(name: .onboardingCompleted, object: nil)
    }
    
    /// 跳过引导
    func skipOnboarding() {
        completeOnboarding()
    }
    
    /// 重置引导
    func resetOnboarding() {
        progress = OnboardingProgress()
        showOnboarding = false
        currentStepIndex = 0
        saveProgress()
    }
    
    /// 检查是否应该显示引导
    func checkShouldShowOnboarding() {
        // 如果是首次启动或引导未完成，显示引导
        if !progress.isCompleted {
            showOnboarding = true
        }
    }
    
    /// 获取当前步骤的提示信息
    func getCurrentStepTips() -> [OnboardingTip] {
        switch progress.currentStep {
        case .welcome:
            return [
                OnboardingTip(
                    title: "实时收入追踪",
                    description: "随时查看您已赚取的金额",
                    iconName: "clock.fill",
                    iconColor: .blue
                ),
                OnboardingTip(
                    title: "目标激励",
                    description: "设置储蓄目标，让收入更有意义",
                    iconName: "target",
                    iconColor: .orange
                ),
                OnboardingTip(
                    title: "智能提醒",
                    description: "收入里程碑自动通知",
                    iconName: "bell.fill",
                    iconColor: .red
                )
            ]
            
        case .salarySettings:
            return [
                OnboardingTip(
                    title: "准确设置",
                    description: "请输入真实的月薪和工作时间",
                    iconName: "checkmark.circle.fill",
                    iconColor: .green
                ),
                OnboardingTip(
                    title: "随时调整",
                    description: "稍后可在设置中修改这些信息",
                    iconName: "gear",
                    iconColor: .gray
                )
            ]
            
        case .goalSetup:
            return [
                OnboardingTip(
                    title: "SMART目标",
                    description: "设置具体、可达成的储蓄目标",
                    iconName: "lightbulb.fill",
                    iconColor: .yellow
                ),
                OnboardingTip(
                    title: "多个目标",
                    description: "您可以同时追踪多个储蓄目标",
                    iconName: "list.bullet",
                    iconColor: .blue
                )
            ]
            
        case .notifications:
            return [
                OnboardingTip(
                    title: "及时通知",
                    description: "在达成收入里程碑时收到通知",
                    iconName: "bell.badge.fill",
                    iconColor: .red
                ),
                OnboardingTip(
                    title: "隐私保护",
                    description: "通知内容不会显示具体金额",
                    iconName: "lock.fill",
                    iconColor: .blue
                )
            ]
            
        case .character:
            return [
                OnboardingTip(
                    title: "表情丰富",
                    description: "小福狸会根据收入进度变化表情",
                    iconName: "face.smiling.fill",
                    iconColor: .yellow
                ),
                OnboardingTip(
                    title: "互动有趣",
                    description: "点击小福狸会有惊喜哦",
                    iconName: "hand.tap.fill",
                    iconColor: .purple
                )
            ]
            
        case .complete:
            return [
                OnboardingTip(
                    title: "开始赚钱",
                    description: "现在开始工作，实时查看收入增长",
                    iconName: "play.fill",
                    iconColor: .green
                ),
                OnboardingTip(
                    title: "查看帮助",
                    description: "在设置中可以重新查看引导",
                    iconName: "questionmark.circle.fill",
                    iconColor: .blue
                )
            ]
        }
    }
    
    /// 获取进度百分比
    func getProgressPercentage() -> Double {
        return Double(currentStepIndex) / Double(allSteps.count - 1)
    }
    
    // MARK: - Data Persistence
    
    private func loadProgress() {
        if let data = UserDefaults.standard.data(forKey: "OnboardingProgress"),
           let decoded = try? JSONDecoder().decode(OnboardingProgress.self, from: data) {
            progress = decoded
            if let index = allSteps.firstIndex(of: progress.currentStep) {
                currentStepIndex = index
            }
        }
    }
    
    private func saveProgress() {
        if let encoded = try? JSONEncoder().encode(progress) {
            UserDefaults.standard.set(encoded, forKey: "OnboardingProgress")
        }
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let onboardingCompleted = Notification.Name("onboardingCompleted")
    static let onboardingStepChanged = Notification.Name("onboardingStepChanged")
} 