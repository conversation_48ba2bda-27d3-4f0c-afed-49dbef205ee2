//
//  ProgressMilestoneDetector.swift
//  xzjlg-iwatch Watch App
//
//  Created by AI Assistant on 2025/1/25.
//

import Foundation
import Combine

/// 进度里程碑检测器 - 检测目标进度达到25%, 50%, 75%等关键节点
class ProgressMilestoneDetector: ObservableObject {
    @Published var reachedMilestones: Set<String> = []
    
    private var previousProgress: [UUID: Int] = [:]
    
    /// 检测目标进度里程碑
    func checkProgressMilestones(for goals: [Goal]) -> [ProgressMilestone] {
        var newMilestones: [ProgressMilestone] = []
        
        for goal in goals {
            let currentProgress = goal.progressPercentage
            let previousProgress = self.previousProgress[goal.id] ?? 0
            
            // 检查25%, 50%, 75%里程碑
            let milestones = [25, 50, 75]
            
            for milestone in milestones {
                let milestoneKey = "\(goal.id.uuidString)_\(milestone)"
                
                if currentProgress >= milestone && 
                   previousProgress < milestone &&
                   !reachedMilestones.contains(milestoneKey) {
                    
                    newMilestones.append(
                        ProgressMilestone(
                            goalId: goal.id,
                            goalName: goal.name,
                            percentage: milestone,
                            currentAmount: goal.currentAmount,
                            targetAmount: goal.targetAmount
                        )
                    )
                    
                    reachedMilestones.insert(milestoneKey)
                }
            }
            
            // 更新记录的进度
            self.previousProgress[goal.id] = currentProgress
        }
        
        return newMilestones
    }
    
    /// 重置里程碑记录
    func resetMilestones(for goalId: UUID) {
        let milestones = [25, 50, 75]
        for milestone in milestones {
            let milestoneKey = "\(goalId.uuidString)_\(milestone)"
            reachedMilestones.remove(milestoneKey)
        }
        previousProgress[goalId] = 0
    }
    
    /// 清除所有里程碑记录
    func clearAllMilestones() {
        reachedMilestones.removeAll()
        previousProgress.removeAll()
    }
}

/// 进度里程碑数据结构
struct ProgressMilestone: Identifiable {
    let id = UUID()
    let goalId: UUID
    let goalName: String
    let percentage: Int
    let currentAmount: Double
    let targetAmount: Double
    let timestamp: Date = Date()
    
    var formattedCurrentAmount: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencySymbol = "¥"
        formatter.maximumFractionDigits = 0
        return formatter.string(from: NSNumber(value: currentAmount)) ?? "¥0"
    }
    
    var formattedTargetAmount: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencySymbol = "¥"
        formatter.maximumFractionDigits = 0
        return formatter.string(from: NSNumber(value: targetAmount)) ?? "¥0"
    }
    
    var remainingAmount: Double {
        return max(targetAmount - currentAmount, 0.0)
    }
    
    var formattedRemainingAmount: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencySymbol = "¥"
        formatter.maximumFractionDigits = 0
        return formatter.string(from: NSNumber(value: remainingAmount)) ?? "¥0"
    }
    
    var milestoneMessage: String {
        switch percentage {
        case 25:
            return "🎯 不错的开始！已完成四分之一！"
        case 50:
            return "🔥 太棒了！已经过半了！"
        case 75:
            return "🚀 最后冲刺！胜利在望！"
        default:
            return "🎉 里程碑达成！"
        }
    }
    
    var milestoneColors: [String] {
        switch percentage {
        case 25:
            return ["green", "mint"]
        case 50:
            return ["blue", "cyan"]
        case 75:
            return ["orange", "yellow"]
        default:
            return ["purple", "pink"]
        }
    }
}