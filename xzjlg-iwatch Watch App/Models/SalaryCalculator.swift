//
//  SalaryCalculator.swift
//  xzjlg-iwatch Watch App
//
//  Created by Kiro on 2025/7/25.
//

import Foundation
import Combine
import WatchKit

/// 工资计算错误类型
enum SalaryCalculatorError: Error, LocalizedError {
    case invalidSettings(String)
    case calculationFailed(String)
    case timerCreationFailed
    case invalidTimeInterval
    case persistenceError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidSettings(let message):
            return "设置无效: \(message)"
        case .calculationFailed(let message):
            return "计算失败: \(message)"
        case .timerCreationFailed:
            return "定时器创建失败"
        case .invalidTimeInterval:
            return "时间间隔无效"
        case .persistenceError(let message):
            return "数据持久化错误: \(message)"
        }
    }
}

/// 工资计算状态
enum CalculationState: String, CaseIterable {
    case stopped = "stopped"
    case calculating = "calculating"
    case paused = "paused"
    case error = "error"
    
    var displayName: String {
        switch self {
        case .stopped:
            return "已停止"
        case .calculating:
            return "计算中"
        case .paused:
            return "已暂停"
        case .error:
            return "错误"
        }
    }
}

/// 工资计算引擎，负责实时计算用户收入
class SalaryCalculator: ObservableObject {
    // MARK: - Published Properties
    @Published var dailyEarnings: Double = 0.0
    @Published var currentWorkScenario: WorkScenario = .resting
    @Published var calculationState: CalculationState = .stopped
    @Published var lastError: SalaryCalculatorError?
    @Published var calculationAccuracy: Double = 1.0 // 计算准确度 (0.0-1.0)
    
    // MARK: - Private Properties
    private var settings: UserSettings
    private var calculationTimer: Timer?
    private var lastCalculationDate: Date?
    private var workStartTime: Date?
    private var dailyWorkMinutes: Int = 0
    private var totalWorkSeconds: TimeInterval = 0
    
    // 错误重试机制
    private var retryCount: Int = 0
    private let maxRetryCount: Int = 3
    private var retryTimer: Timer?
    
    // 性能优化
    private var calculationQueue: DispatchQueue
    private var isCalculationInProgress: Bool = false
    private let performanceThrottler = PerformanceThrottler()
    
    // MARK: - Callbacks
    var onEarningsUpdated: ((Double) -> Void)?
    var onThresholdReached: ((Double, Double) -> Void)? // (amount, percentage)
    var onScenarioChanged: ((WorkScenario) -> Void)?
    var onErrorOccurred: ((SalaryCalculatorError) -> Void)?
    var onCalculationStateChanged: ((CalculationState) -> Void)?
    
    // MARK: - Constants and Settings
    private let defaultCalculationInterval: TimeInterval = 10.0
    private var currentCalculationInterval: TimeInterval = 10.0
    private let thresholds: [Double] = [0.25, 0.50, 0.75, 1.0]
    private var reachedThresholds: Set<Double> = []
    
    // 电池优化级别对应的更新间隔
    private let batteryOptimizedIntervals: [BatteryLevel: TimeInterval] = [
        .high: 10.0,
        .medium: 60.0,
        .low: 300.0,
        .critical: 600.0
    ]
    
    // MARK: - Initialization
    init(settings: UserSettings = .default) {
        self.settings = settings
        self.calculationQueue = DispatchQueue(label: "salary.calculation", qos: .userInteractive)
        self.lastCalculationDate = Date()
        
        // 验证设置
        validateSettings()
        
        // 检查是否需要重置（新的一天）
        resetIfNewDay()
        
        // 设置性能监控
        setupPerformanceMonitoring()
    }
    
    deinit {
        cleanup()
    }
}

// MARK: - Public Methods
extension SalaryCalculator {
    /// 开始工资计算
    func startCalculation() throws {
        guard calculationState != .calculating else { return }
        
        // 验证设置
        try validateSettingsThrows()
        
        calculationState = .calculating
        workStartTime = Date()
        retryCount = 0
        lastError = nil
        
        // 立即计算一次
        try performCalculation()
        
        // 设置定时器
        try setupCalculationTimer()
        
        onCalculationStateChanged?(calculationState)
    }
    
    /// 停止工资计算
    func stopCalculation() {
        calculationState = .stopped
        invalidateTimers()
        workStartTime = nil
        isCalculationInProgress = false
        
        onCalculationStateChanged?(calculationState)
    }
    
    /// 暂停工资计算
    func pauseCalculation() {
        guard calculationState == .calculating else { return }
        
        calculationState = .paused
        invalidateTimers()
        
        onCalculationStateChanged?(calculationState)
    }
    
    /// 恢复工资计算
    func resumeCalculation() throws {
        guard calculationState == .paused else { return }
        
        calculationState = .calculating
        try setupCalculationTimer()
        
        onCalculationStateChanged?(calculationState)
    }
    
    /// 更新用户设置
    func updateSettings(_ newSettings: UserSettings) throws {
        guard newSettings.isValid else {
            throw SalaryCalculatorError.invalidSettings("设置验证失败")
        }
        
        settings = newSettings
        
        // 重新计算当前收入
        if calculationState == .calculating {
            try performCalculation()
        }
    }
    
    /// 手动触发计算
    func forceCalculation() throws {
        try performCalculation()
    }
    
    /// 获取当前工作场景
    func getCurrentScenario() -> WorkScenario {
        // 使用智能检测器的时间基础检测
        let now = Date()
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: now)
        let weekday = calendar.component(.weekday, from: now)
        
        // 周末判断
        let isWeekend = weekday == 1 || weekday == 7 // Sunday = 1, Saturday = 7
        
        if isWeekend {
            return settings.isWeekendWorkEnabled ? .weekendWithPay : .resting
        }
        
        // 工作日判断
        if hour >= 12 && hour <= 13 {
            return .lunchBreak
        } else if hour >= settings.workStartHour && hour < settings.workEndHour {
            return .normalWork
        } else if (hour > settings.workEndHour && hour < 22) || hour < settings.workStartHour {
            return .nightOvertime
        } else {
            return .resting
        }
    }
    
    /// 获取今日目标工资
    func getDailyTargetSalary() -> Double {
        return settings.dailyBaseSalary
    }
    
    /// 获取当前进度百分比
    func getCurrentProgress() -> Double {
        let target = getDailyTargetSalary()
        return target > 0 ? min(dailyEarnings / target, 1.0) : 0.0
    }
    
    /// 重置每日数据
    func resetDailyData() {
        dailyEarnings = 0.0
        dailyWorkMinutes = 0
        totalWorkSeconds = 0
        reachedThresholds.removeAll()
        lastCalculationDate = Date()
        workStartTime = Date()
        
        performanceThrottler.reset()
    }
    
    /// 获取详细统计信息
    func getDetailedStats() -> SalaryStatistics {
        let currentScenario = getCurrentScenario()
        let progress = getCurrentProgress()
        let efficiency = calculateWorkEfficiency()
        
        return SalaryStatistics(
            dailyEarnings: dailyEarnings,
            targetEarnings: getDailyTargetSalary(),
            progress: progress,
            totalWorkMinutes: dailyWorkMinutes,
            currentScenario: currentScenario,
            workEfficiency: efficiency,
            calculationAccuracy: calculationAccuracy,
            averageHourlyRate: calculateAverageHourlyRate(),
            estimatedEndTime: estimateWorkEndTime(),
            overtimeMinutes: calculateOvertimeMinutes()
        )
    }
}

// MARK: - Private Core Calculation Methods
private extension SalaryCalculator {
    /// 核心计算方法
    func performCalculation() throws {
        guard !isCalculationInProgress else { return }
        
        isCalculationInProgress = true
        defer { isCalculationInProgress = false }
        
        let now = Date()
        
        // 检查是否需要重置（新的一天）
        if resetIfNewDay() {
            return
        }
        
        // 检测当前工作场景
        let newScenario = getCurrentScenario()
        if newScenario != currentWorkScenario {
            currentWorkScenario = newScenario
            onScenarioChanged?(newScenario)
        }
        
        // 如果不需要计算工资，直接返回
        guard currentWorkScenario.shouldCalculateSalary else {
            lastCalculationDate = now
            return
        }
        
        // 计算自上次计算以来的时间差
        let timeDifference = calculateTimeDifference(from: lastCalculationDate ?? now, to: now)
        
        // 验证时间差的合理性
        guard timeDifference >= 0 && timeDifference <= 3600 else { // 最大1小时
            throw SalaryCalculatorError.invalidTimeInterval
        }
        
        // 计算这段时间的收入
        let earnings = try calculateEarningsForDuration(timeDifference, scenario: currentWorkScenario)
        
        // 更新总收入和工作时间
        dailyEarnings += earnings
        totalWorkSeconds += timeDifference
        dailyWorkMinutes = Int(totalWorkSeconds / 60)
        
        // 检查阈值
        checkThresholds()
        
        // 更新最后计算时间
        lastCalculationDate = now
        
        // 更新计算准确度
        updateCalculationAccuracy(timeDifference: timeDifference)
        
        // 触发回调
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.onEarningsUpdated?(self.dailyEarnings)
        }
        
        // 重置重试计数
        retryCount = 0
    }
    
    /// 计算时间差（秒）
    func calculateTimeDifference(from startDate: Date, to endDate: Date) -> TimeInterval {
        return max(0, endDate.timeIntervalSince(startDate))
    }
    
    /// 计算指定时长的收入
    func calculateEarningsForDuration(_ duration: TimeInterval, scenario: WorkScenario) throws -> Double {
        guard duration >= 0 else {
            throw SalaryCalculatorError.calculationFailed("时长不能为负数")
        }
        
        let seconds = duration
        let baseSecondSalary = settings.secondBaseSalary
        let multiplier = scenario.overtimeRate == 0.0 ? 1.0 : scenario.overtimeRate
        
        guard baseSecondSalary.isFinite && multiplier.isFinite else {
            throw SalaryCalculatorError.calculationFailed("计算参数无效")
        }
        
        return seconds * baseSecondSalary * multiplier
    }
    
    /// 检查并触发阈值通知
    func checkThresholds() {
        let progress = getCurrentProgress()
        
        for threshold in thresholds {
            if progress >= threshold && !reachedThresholds.contains(threshold) {
                reachedThresholds.insert(threshold)
                
                DispatchQueue.main.async { [weak self] in
                    self?.onThresholdReached?(self?.dailyEarnings ?? 0, threshold)
                }
            }
        }
    }
    
    /// 检查是否需要重置（新的一天）
    @discardableResult
    func resetIfNewDay() -> Bool {
        let calendar = Calendar.current
        let now = Date()
        
        guard let lastDate = lastCalculationDate else {
            lastCalculationDate = now
            return false
        }
        
        // 检查是否是新的一天
        if !calendar.isDate(lastDate, inSameDayAs: now) {
            resetDailyData()
            return true
        }
        
        return false
    }
    
    /// 更新计算准确度
    func updateCalculationAccuracy(timeDifference: TimeInterval) {
        // 基于时间间隔和计算频率评估准确度
        let idealInterval = currentCalculationInterval
        let accuracy = min(1.0, idealInterval / max(timeDifference, 1.0))
        
        // 使用移动平均来平滑准确度值
        calculationAccuracy = (calculationAccuracy * 0.8) + (accuracy * 0.2)
    }
}

// MARK: - Timer Management
private extension SalaryCalculator {
    /// 设置计算定时器
    func setupCalculationTimer() throws {
        invalidateTimers()
        
        calculationTimer = Timer.scheduledTimer(withTimeInterval: currentCalculationInterval, repeats: true) { [weak self] _ in
            self?.performCalculationSafely()
        }
        
        guard calculationTimer != nil else {
            throw SalaryCalculatorError.timerCreationFailed
        }
    }
    
    /// 安全地执行计算（带错误处理）
    func performCalculationSafely() {
        do {
            try performCalculation()
        } catch {
            handleCalculationError(error)
        }
    }
    
    /// 销毁定时器
    func invalidateTimers() {
        calculationTimer?.invalidate()
        calculationTimer = nil
        retryTimer?.invalidate()
        retryTimer = nil
    }
}

// MARK: - Error Handling
private extension SalaryCalculator {
    /// 处理计算错误
    func handleCalculationError(_ error: Error) {
        let salaryError: SalaryCalculatorError
        
        if let err = error as? SalaryCalculatorError {
            salaryError = err
        } else {
            salaryError = .calculationFailed(error.localizedDescription)
        }
        
        lastError = salaryError
        
        // 如果重试次数未达到上限，尝试重试
        if retryCount < maxRetryCount {
            retryCount += 1
            scheduleRetry()
        } else {
            // 超过重试上限，停止计算
            calculationState = .error
            stopCalculation()
            
            DispatchQueue.main.async { [weak self] in
                self?.onErrorOccurred?(salaryError)
                self?.onCalculationStateChanged?(self?.calculationState ?? .error)
            }
        }
    }
    
    /// 安排重试
    func scheduleRetry() {
        let retryInterval = min(pow(2.0, Double(retryCount)), 60.0) // 指数退避，最大60秒
        
        retryTimer = Timer.scheduledTimer(withTimeInterval: retryInterval, repeats: false) { [weak self] _ in
            self?.performCalculationSafely()
        }
    }
    
    /// 验证设置
    func validateSettings() {
        if !settings.isValid {
            lastError = .invalidSettings("用户设置验证失败")
        }
    }
    
    /// 验证设置（抛出异常）
    func validateSettingsThrows() throws {
        guard settings.isValid else {
            throw SalaryCalculatorError.invalidSettings("用户设置验证失败: \(settings.validationErrors.joined(separator: ", "))")
        }
    }
}

// MARK: - Battery & Performance Optimization
extension SalaryCalculator {
    /// 根据电池电量调整计算频率
    func adjustCalculationFrequency(batteryLevel: Float) {
        let batteryLevelEnum = BatteryLevel.from(batteryLevel)
        let newInterval = batteryOptimizedIntervals[batteryLevelEnum] ?? defaultCalculationInterval
        
        // 如果间隔发生变化，重新设置定时器
        if newInterval != currentCalculationInterval {
            currentCalculationInterval = newInterval
            
            if calculationState == .calculating {
                try? setupCalculationTimer()
            }
        }
    }
    
    /// 根据时间段调整计算频率
    func adjustForTimeOfDay() {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: Date())
        
        let timeBasedInterval: TimeInterval
        
        switch hour {
        case 22...23, 0...6:
            // 深夜时段：5分钟更新一次
            timeBasedInterval = 300.0
        case 7...8, 19...21:
            // 扩展工作时段：1分钟更新一次
            timeBasedInterval = 60.0
        default:
            // 正常工作时段：默认间隔
            timeBasedInterval = defaultCalculationInterval
        }
        
        if timeBasedInterval != currentCalculationInterval {
            currentCalculationInterval = timeBasedInterval
            
            if calculationState == .calculating {
                try? setupCalculationTimer()
            }
        }
    }
    
    /// 根据电池优化管理器调整计算频率（新增方法）
    func adjustFrequencyForBatteryOptimization(interval: TimeInterval) {
        if interval != currentCalculationInterval {
            currentCalculationInterval = interval
            
            if calculationState == .calculating {
                try? setupCalculationTimer()
            }
        }
    }
    
    /// 智能调节计算频率
    func intelligentFrequencyAdjustment() {
        // 根据工作场景调整频率
        switch currentWorkScenario {
        case .normalWork:
            currentCalculationInterval = defaultCalculationInterval
        case .nightOvertime, .weekendVoluntary:
            currentCalculationInterval = defaultCalculationInterval * 0.5 // 加班时更频繁
        case .weekendWithPay:
            currentCalculationInterval = defaultCalculationInterval * 1.5
        case .lunchBreak, .resting:
            currentCalculationInterval = defaultCalculationInterval * 6 // 休息时降低频率
        case .deepWork:
            currentCalculationInterval = defaultCalculationInterval * 0.8 // 专注时稍频繁
        case .meeting:
            currentCalculationInterval = defaultCalculationInterval * 1.2 // 会议时稍降低
        case .commuting:
            currentCalculationInterval = defaultCalculationInterval * 4 // 通勤时大幅降低
        }
        
        // 应用性能节流
        if performanceThrottler.shouldThrottle() {
            currentCalculationInterval *= 2
        }
        
        // 重新设置定时器
        if calculationState == .calculating {
            try? setupCalculationTimer()
        }
    }
}

// MARK: - Statistics and Analytics
private extension SalaryCalculator {
    /// 计算平均时薪
    func calculateAverageHourlyRate() -> Double {
        guard totalWorkSeconds > 0 else { return 0.0 }
        let hours = totalWorkSeconds / 3600.0
        return dailyEarnings / hours
    }
    
    /// 计算工作效率
    func calculateWorkEfficiency() -> Double {
        let expectedWorkSeconds = Double(settings.workHoursPerDay) * 3600.0
        guard expectedWorkSeconds > 0 else { return 0.0 }
        return min(totalWorkSeconds / expectedWorkSeconds, 1.0)
    }
    
    /// 计算加班时长
    func calculateOvertimeMinutes() -> Int {
        // 简化的加班时长计算
        return currentWorkScenario == .nightOvertime ? 60 : 0
    }
    
    /// 预估工作结束时间
    func estimateWorkEndTime() -> Date? {
        let remaining = getDailyTargetSalary() - dailyEarnings
        guard remaining > 0 else { return nil }
        
        let currentRate = calculateAverageHourlyRate()
        guard currentRate > 0 else { return nil }
        
        let hoursNeeded = remaining / currentRate
        return Calendar.current.date(byAdding: .hour, value: Int(ceil(hoursNeeded)), to: Date())
    }
}

// MARK: - Setup and Cleanup
private extension SalaryCalculator {
    /// 设置性能监控
    func setupPerformanceMonitoring() {
        // WatchOS 暂不支持内存警告通知
        // 可以考虑其他性能监控方式
    }
    
    /// 处理内存警告
    func handleMemoryWarning() {
        // 降低计算频率
        currentCalculationInterval *= 2
        
        // 重置性能节流器
        performanceThrottler.reset()
        
        // 如果正在运行，重新设置定时器
        if calculationState == .calculating {
            try? setupCalculationTimer()
        }
    }
    
    /// 清理资源
    func cleanup() {
        invalidateTimers()
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - Supporting Types
enum BatteryLevel: CaseIterable {
    case high, medium, low, critical
    
    static func from(_ level: Float) -> BatteryLevel {
        switch level {
        case 0.5...1.0:
            return .high
        case 0.2..<0.5:
            return .medium
        case 0.1..<0.2:
            return .low
        default:
            return .critical
        }
    }
}

/// 性能节流器
class PerformanceThrottler {
    private var operationCount: Int = 0
    private var startTime: Date = Date()
    private let maxOperationsPerMinute: Int = 60
    
    func shouldThrottle() -> Bool {
        let elapsed = Date().timeIntervalSince(startTime)
        
        // 每分钟重置计数器
        if elapsed >= 60 {
            reset()
            return false
        }
        
        operationCount += 1
        return operationCount > maxOperationsPerMinute
    }
    
    func reset() {
        operationCount = 0
        startTime = Date()
    }
}

/// 工资统计信息
struct SalaryStatistics {
    let dailyEarnings: Double
    let targetEarnings: Double
    let progress: Double
    let totalWorkMinutes: Int
    let currentScenario: WorkScenario
    let workEfficiency: Double
    let calculationAccuracy: Double
    let averageHourlyRate: Double
    let estimatedEndTime: Date?
    let overtimeMinutes: Int
    
    var formattedWorkTime: String {
        let hours = totalWorkMinutes / 60
        let minutes = totalWorkMinutes % 60
        return String(format: "%d小时%d分钟", hours, minutes)
    }
    
    var formattedProgress: String {
        return String(format: "%.1f%%", progress * 100)
    }
    
    var formattedEfficiency: String {
        return String(format: "%.1f%%", workEfficiency * 100)
    }
    
    var formattedAccuracy: String {
        return String(format: "%.1f%%", calculationAccuracy * 100)
    }
    
    var formattedAverageHourlyRate: String {
        return String(format: "¥%.2f", averageHourlyRate)
    }
    
    var formattedEstimatedEndTime: String? {
        guard let endTime = estimatedEndTime else { return nil }
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: endTime)
    }
    
    /// 获取每日工作进度 (0.0 - 1.0)
    func getDailyProgress() -> Double {
        // TODO: 暂时返回固定值，避免编译错误
        return 0.0
    }
}