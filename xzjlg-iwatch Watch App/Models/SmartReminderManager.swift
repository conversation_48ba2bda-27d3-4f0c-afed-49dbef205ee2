//
//  SmartReminderManager.swift
//  xzjlg-iwatch Watch App
//
//  Created by Assistant on 2025/1/20.
//

import Foundation
import SwiftUI
import Combine

/// 智能提醒管理器
/// 负责管理各种工作相关的智能提醒
class SmartReminderManager: ObservableObject {
    
    // MARK: - Types
    
    /// 提醒类型
    enum ReminderType: String, CaseIterable {
        case workDuration = "work_duration"     // 工作时长提醒
        case restBreak = "rest_break"          // 休息提醒
        case posture = "posture"               // 姿势提醒
        case hydration = "hydration"           // 喝水提醒
        case eyeRest = "eye_rest"             // 眼睛休息提醒
        case healthCheck = "health_check"      // 健康检查提醒
    }
    
    // MARK: - Published Properties
    
    @Published var isEnabled: Bool = true
    @Published var currentWorkScenario: WorkScenario = .resting
    @Published var workDurationMinutes: Int = 0
    @Published var lastRestTime: Date = Date()
    
    // MARK: - Properties
    
    /// 提醒触发回调
    var onReminderTriggered: ((String) -> Void)?
    
    // MARK: - Private Properties
    
    private var workDurationTimer: Timer?
    private var restTimer: Timer?
    private var postureTimer: Timer?
    private var workStartTime: Date?
    private var lastPostureReminder: Date = Date()
    
    // MARK: - Timing Constants
    
    private let workDurationCheckInterval: TimeInterval = 60 // 每分钟检查一次工作时长
    private let restReminderInterval: TimeInterval = 50 * 60 // 50分钟提醒休息
    private let postureReminderInterval: TimeInterval = 30 * 60 // 30分钟提醒调整姿势
    private let maxContinuousWorkTime: TimeInterval = 2 * 60 * 60 // 最大连续工作时间2小时
    
    // MARK: - Initialization
    
    init() {
        // 初始化
    }
    
    deinit {
        stopAllReminders()
    }
    
    // MARK: - Public Methods
    
    /// 启动提醒系统
    func startReminders() {
        guard isEnabled else { return }
        
        startWorkDurationMonitoring()
        startRestReminders()
        startPostureReminders()
    }
    
    /// 停止所有提醒
    func stopAllReminders() {
        workDurationTimer?.invalidate()
        restTimer?.invalidate()
        postureTimer?.invalidate()
        
        workDurationTimer = nil
        restTimer = nil
        postureTimer = nil
    }
    
    /// 更新工作场景
    func updateWorkScenario(_ scenario: WorkScenario) {
        let previousScenario = currentWorkScenario
        currentWorkScenario = scenario
        
        // 场景变化时重置相关计时器
        handleScenarioChange(from: previousScenario, to: scenario)
    }
    
    /// 手动触发休息
    func triggerRest() {
        lastRestTime = Date()
        workDurationMinutes = 0
        workStartTime = nil
        
        triggerReminder(
            type: .restBreak,
            message: "开始休息，放松一下身心 😌"
        )
    }
    
    /// 获取工作时长统计
    func getWorkDurationStats() -> (total: TimeInterval, continuous: TimeInterval) {
        let total = TimeInterval(workDurationMinutes * 60)
        let continuous = workStartTime != nil ? Date().timeIntervalSince(workStartTime!) : 0
        return (total: total, continuous: continuous)
    }
    
    // MARK: - Private Methods
    
    /// 处理场景变化
    private func handleScenarioChange(from: WorkScenario, to: WorkScenario) {
        // 从休息状态进入工作状态
        if !from.isWorkingState && to.isWorkingState {
            workStartTime = Date()
            lastRestTime = Date()
        }
        
        // 从工作状态进入休息状态
        if from.isWorkingState && !to.isWorkingState {
            workStartTime = nil
        }
    }
    
    // MARK: - Timer Management
    
    /// 开始工作时长监控
    private func startWorkDurationMonitoring() {
        workDurationTimer = Timer.scheduledTimer(withTimeInterval: workDurationCheckInterval, repeats: true) { [weak self] _ in
            self?.checkWorkDuration()
        }
    }
    
    /// 开始休息提醒
    private func startRestReminders() {
        restTimer = Timer.scheduledTimer(withTimeInterval: restReminderInterval, repeats: true) { [weak self] _ in
            self?.checkRestReminder()
        }
    }
    
    /// 开始姿势提醒
    private func startPostureReminders() {
        postureTimer = Timer.scheduledTimer(withTimeInterval: postureReminderInterval, repeats: true) { [weak self] _ in
            self?.checkPostureReminder()
        }
    }
    
    // MARK: - Reminder Checks
    
    /// 检查工作时长
    private func checkWorkDuration() {
        guard currentWorkScenario.isWorkingState,
              let startTime = workStartTime else { return }
        
        let workDuration = Date().timeIntervalSince(startTime)
        workDurationMinutes = Int(workDuration / 60)
        
        // 检查是否需要强制休息提醒
        if workDuration >= maxContinuousWorkTime {
            triggerReminder(
                type: .workDuration,
                message: "已连续工作\(Int(workDuration / 3600))小时，必须休息一下！⚠️"
            )
            
            // 重置计时器，避免重复提醒
            workStartTime = Date()
        } else if workDuration >= 90 * 60 { // 90分钟提醒
            triggerReminder(
                type: .workDuration,
                message: "已工作90分钟，建议短暂休息 ⏰"
            )
        }
    }
    
    /// 检查休息提醒
    private func checkRestReminder() {
        guard currentWorkScenario.isWorkingState else { return }
        
        let timeSinceLastRest = Date().timeIntervalSince(lastRestTime)
        
        if timeSinceLastRest >= restReminderInterval {
            let message = getContextualRestMessage()
            triggerReminder(type: .restBreak, message: message)
        }
    }
    
    /// 检查姿势提醒
    private func checkPostureReminder() {
        guard currentWorkScenario.isWorkingState else { return }
        
        let timeSinceLastPostureReminder = Date().timeIntervalSince(lastPostureReminder)
        
        if timeSinceLastPostureReminder >= postureReminderInterval {
            let message = "调整一下坐姿，活动活动肩膀 🧘‍♀️"
            triggerReminder(type: .posture, message: message)
            lastPostureReminder = Date()
        }
    }
    
    // MARK: - Helper Methods
    
    /// 获取上下文相关的休息消息
    private func getContextualRestMessage() -> String {
        let hour = Calendar.current.component(.hour, from: Date())
        
        switch currentWorkScenario {
        case .deepWork:
            return "深度专注已久，让大脑休息一下 🧠"
        case .nightOvertime:
            return "夜间加班辛苦了，更要注意休息 🌙"
        case .meeting:
            return "长时间会议后，活动一下身体 📋"
        default:
            if hour < 12 {
                return "上午工作辛苦了，休息充电一下 ☕"
            } else if hour < 18 {
                return "下午继续加油前，先休息一下 ⚡"
            } else {
                return "工作一天辛苦了，放松一下吧 😌"
            }
        }
    }
    
    /// 触发提醒
    private func triggerReminder(type: ReminderType, message: String) {
        print("📢 \(type.rawValue): \(message)")
        onReminderTriggered?(message)
    }
}
