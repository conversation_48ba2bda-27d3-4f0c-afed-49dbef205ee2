//
//  TestingManager.swift
//  xzjlg-iwatch Watch App
//
//  Created by Assistant on 2025/1/20.
//

import Foundation
import SwiftUI
import WatchKit

/// 屏幕尺寸类型
enum ScreenSizeType: String, CaseIterable {
    case size38mm = "38mm"
    case size40mm = "40mm"
    case size42mm = "42mm"
    case size44mm = "44mm"
    case size45mm = "45mm"
    case size49mm = "49mm"
    
    var displayName: String {
        switch self {
        case .size38mm: return "Apple Watch 38mm"
        case .size40mm: return "Apple Watch 40mm"
        case .size42mm: return "Apple Watch 42mm"
        case .size44mm: return "Apple Watch 44mm"
        case .size45mm: return "Apple Watch 45mm"
        case .size49mm: return "Apple Watch Ultra 49mm"
        }
    }
    
    var screenSize: CGSize {
        switch self {
        case .size38mm: return CGSize(width: 136, height: 170)
        case .size40mm: return CGSize(width: 162, height: 197)
        case .size42mm: return CGSize(width: 156, height: 195)
        case .size44mm: return CGSize(width: 184, height: 224)
        case .size45mm: return CGSize(width: 184, height: 224)
        case .size49mm: return CGSize(width: 205, height: 251)
        }
    }
    
    var crownLocation: CrownLocation {
        return .right // 所有现代Apple Watch都在右侧
    }
}

/// 数字王冠位置
enum CrownLocation {
    case left
    case right
}

/// 测试报告
struct TestReport: Codable, Identifiable {
    let id = UUID()
    let testType: String
    let startTime: Date
    let endTime: Date
    let duration: TimeInterval
    let result: TestingResult
    let details: [String: String]
    let issues: [TestIssue]
    
    // 自定义编码键，排除id属性
    enum CodingKeys: String, CodingKey {
        case testType, startTime, endTime, duration, result, details, issues
    }
    
    var durationString: String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute, .second]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: duration) ?? "\(Int(duration))秒"
    }
}

/// 测试结果
enum TestingResult: String, Codable {
    case passed = "passed"
    case failed = "failed"
    case warning = "warning"
    case running = "running"
    
    var displayName: String {
        switch self {
        case .passed: return "通过"
        case .failed: return "失败"
        case .warning: return "警告"
        case .running: return "运行中"
        }
    }
    
    var color: Color {
        switch self {
        case .passed: return .green
        case .failed: return .red
        case .warning: return .orange
        case .running: return .blue
        }
    }
}

/// 测试问题
struct TestIssue: Codable, Identifiable {
    let id = UUID()
    let severity: IssueSeverity
    let category: String
    let description: String
    let suggestion: String
    
    // 自定义编码键，排除id属性
    enum CodingKeys: String, CodingKey {
        case severity, category, description, suggestion
    }
    
    enum IssueSeverity: String, Codable {
        case critical = "critical"
        case major = "major"
        case minor = "minor"
        case info = "info"
        
        var displayName: String {
            switch self {
            case .critical: return "严重"
            case .major: return "主要"
            case .minor: return "次要"
            case .info: return "信息"
            }
        }
        
        var color: Color {
            switch self {
            case .critical: return .red
            case .major: return .orange
            case .minor: return .yellow
            case .info: return .blue
            }
        }
    }
}

/// 稳定性测试指标
struct StabilityMetrics: Codable {
    var startTime: Date
    var uptime: TimeInterval
    var crashCount: Int
    var memoryLeakCount: Int
    var errorCount: Int
    var averageMemoryUsage: Double
    var peakMemoryUsage: Double
    var averageCPUUsage: Double
    var peakCPUUsage: Double
    var batteryDrainRate: Double
    var frameDropCount: Int
    var averageFPS: Double
    
    static let empty = StabilityMetrics(
        startTime: Date(),
        uptime: 0,
        crashCount: 0,
        memoryLeakCount: 0,
        errorCount: 0,
        averageMemoryUsage: 0,
        peakMemoryUsage: 0,
        averageCPUUsage: 0,
        peakCPUUsage: 0,
        batteryDrainRate: 0,
        frameDropCount: 0,
        averageFPS: 0
    )
}

/// 测试管理器
class TestingManager: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = TestingManager()
    
    // MARK: - Published Properties
    
    @Published var isRunningStabilityTest: Bool = false
    @Published var stabilityMetrics: StabilityMetrics = .empty
    @Published var testReports: [TestReport] = []
    @Published var currentScreenSize: ScreenSizeType = .size44mm
    @Published var screenAdaptationResults: [ScreenSizeType: TestingResult] = [:]
    
    // MARK: - Private Properties
    
    private var stabilityTimer: Timer?
    private var metricsUpdateTimer: Timer?
    private let metricsUpdateInterval: TimeInterval = 5.0
    private var memoryReadings: [Double] = []
    private var cpuReadings: [Double] = []
    private var fpsReadings: [Double] = []
    
    // MARK: - Initialization
    
    private init() {
        loadTestReports()
        detectCurrentScreenSize()
    }
    
    // MARK: - Screen Adaptation Testing
    
    /// 开始屏幕适配测试
    func startScreenAdaptationTest() {
        let startTime = Date()
        var issues: [TestIssue] = []
        var details: [String: String] = [:]
        
        UserExperienceManager.shared.triggerHapticFeedback(.notification)
        
        // 测试当前屏幕尺寸
        let currentSize = getCurrentScreenSize()
        details["检测到的屏幕尺寸"] = currentSize.displayName
        details["屏幕分辨率"] = "\(Int(currentSize.screenSize.width))×\(Int(currentSize.screenSize.height))"
        
        // 测试UI元素适配
        let uiTestResult = testUIAdaptation(for: currentSize)
        screenAdaptationResults[currentSize] = uiTestResult
        
        // 检查字体大小适配
        if !isTextSizeAppropriate(for: currentSize) {
            issues.append(TestIssue(
                severity: .minor,
                category: "字体适配",
                description: "在\(currentSize.displayName)上字体可能过小",
                suggestion: "建议调整字体大小以提高可读性"
            ))
        }
        
        // 检查触摸目标大小
        if !isTouchTargetSizeAppropriate(for: currentSize) {
            issues.append(TestIssue(
                severity: .major,
                category: "触摸目标",
                description: "按钮或触摸目标可能过小",
                suggestion: "确保触摸目标至少44点大小"
            ))
        }
        
        // 检查内容布局
        if !isLayoutAppropriate(for: currentSize) {
            issues.append(TestIssue(
                severity: .minor,
                category: "布局适配",
                description: "某些内容在小屏幕上可能显示不全",
                suggestion: "优化布局以适配小屏幕设备"
            ))
        }
        
        let result: TestingResult = issues.isEmpty ? .passed : (issues.contains { $0.severity == .critical || $0.severity == .major } ? .failed : .warning)
        
        let report = TestReport(
            testType: "屏幕适配测试",
            startTime: startTime,
            endTime: Date(),
            duration: Date().timeIntervalSince(startTime),
            result: result,
            details: details,
            issues: issues
        )
        
        testReports.append(report)
        saveTestReports()
        
        UserExperienceManager.shared.triggerHapticFeedback(result == .passed ? .success : .warning)
    }
    
    /// 开始长时间稳定性测试
    func startStabilityTest(duration: TimeInterval = 3600) { // 默认1小时
        guard !isRunningStabilityTest else { return }
        
        isRunningStabilityTest = true
        stabilityMetrics = StabilityMetrics(
            startTime: Date(),
            uptime: 0,
            crashCount: 0,
            memoryLeakCount: 0,
            errorCount: 0,
            averageMemoryUsage: 0,
            peakMemoryUsage: 0,
            averageCPUUsage: 0,
            peakCPUUsage: 0,
            batteryDrainRate: 0,
            frameDropCount: 0,
            averageFPS: 0
        )
        
        // 清空历史读数
        memoryReadings.removeAll()
        cpuReadings.removeAll()
        fpsReadings.removeAll()
        
        UserExperienceManager.shared.triggerHapticFeedback(.notification)
        
        // 开始定期更新指标
        metricsUpdateTimer = Timer.scheduledTimer(withTimeInterval: metricsUpdateInterval, repeats: true) { [weak self] _ in
            self?.updateStabilityMetrics()
        }
        
        // 设置测试结束定时器
        stabilityTimer = Timer.scheduledTimer(withTimeInterval: duration, repeats: false) { [weak self] _ in
            self?.stopStabilityTest()
        }
        
        // 注册错误通知监听
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleErrorOccurred),
            name: .errorOccurred,
            object: nil
        )
    }
    
    /// 停止稳定性测试
    func stopStabilityTest() {
        guard isRunningStabilityTest else { return }
        
        isRunningStabilityTest = false
        stabilityTimer?.invalidate()
        metricsUpdateTimer?.invalidate()
        
        NotificationCenter.default.removeObserver(self, name: .errorOccurred, object: nil)
        
        // 生成测试报告
        generateStabilityReport()
        
        UserExperienceManager.shared.triggerHapticFeedback(.success)
    }
    
    // MARK: - Private Testing Methods
    
    private func detectCurrentScreenSize() {
        let screenBounds = WKInterfaceDevice.current().screenBounds
        let screenScale = WKInterfaceDevice.current().screenScale
        
        // 计算物理像素
        let physicalWidth = screenBounds.width * screenScale
        let physicalHeight = screenBounds.height * screenScale
        
        // 根据分辨率判断设备类型
        switch (Int(physicalWidth), Int(physicalHeight)) {
        case (272, 340): currentScreenSize = .size38mm
        case (324, 394): currentScreenSize = .size40mm
        case (312, 390): currentScreenSize = .size42mm
        case (368, 448): currentScreenSize = .size44mm
        case (368, 448): currentScreenSize = .size45mm // 与44mm相同分辨率
        case (410, 502): currentScreenSize = .size49mm
        default: currentScreenSize = .size44mm // 默认值
        }
    }
    
    private func getCurrentScreenSize() -> ScreenSizeType {
        return currentScreenSize
    }
    
    private func testUIAdaptation(for screenSize: ScreenSizeType) -> TestingResult {
        // 模拟UI适配测试
        let screenWidth = screenSize.screenSize.width
        
        // 检查是否为小屏幕
        if screenWidth < 160 {
            // 38mm和42mm为小屏幕，需要特殊适配
            return .warning
        } else if screenWidth > 200 {
            // Ultra为大屏幕，应该充分利用空间
            return .passed
        } else {
            // 标准尺寸
            return .passed
        }
    }
    
    private func isTextSizeAppropriate(for screenSize: ScreenSizeType) -> Bool {
        // 小屏幕需要确保文字可读性
        return screenSize.screenSize.width >= 150
    }
    
    private func isTouchTargetSizeAppropriate(for screenSize: ScreenSizeType) -> Bool {
        // 所有屏幕都应该有合适的触摸目标大小
        return true // 简化实现
    }
    
    private func isLayoutAppropriate(for screenSize: ScreenSizeType) -> Bool {
        // 检查布局是否适合当前屏幕
        return screenSize.screenSize.width >= 140
    }
    
    private func updateStabilityMetrics() {
        stabilityMetrics.uptime = Date().timeIntervalSince(stabilityMetrics.startTime)
        
        // 更新内存使用情况（模拟）
        let currentMemory = getCurrentMemoryUsage()
        memoryReadings.append(currentMemory)
        stabilityMetrics.averageMemoryUsage = memoryReadings.reduce(0, +) / Double(memoryReadings.count)
        stabilityMetrics.peakMemoryUsage = memoryReadings.max() ?? 0
        
        // 更新CPU使用情况（模拟）
        let currentCPU = getCurrentCPUUsage()
        cpuReadings.append(currentCPU)
        stabilityMetrics.averageCPUUsage = cpuReadings.reduce(0, +) / Double(cpuReadings.count)
        stabilityMetrics.peakCPUUsage = cpuReadings.max() ?? 0
        
        // 更新FPS（模拟）
        let currentFPS = getCurrentFPS()
        fpsReadings.append(currentFPS)
        stabilityMetrics.averageFPS = fpsReadings.reduce(0, +) / Double(fpsReadings.count)
        
        // 检测内存泄漏
        if currentMemory > stabilityMetrics.peakMemoryUsage * 1.5 {
            stabilityMetrics.memoryLeakCount += 1
        }
        
        // 检测帧率下降
        if currentFPS < 30 {
            stabilityMetrics.frameDropCount += 1
        }
        
        // 更新电池消耗率（模拟）
        stabilityMetrics.batteryDrainRate = calculateBatteryDrainRate()
    }
    
    private func getCurrentMemoryUsage() -> Double {
        // 获取真实内存使用情况
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                          task_flavor_t(MACH_TASK_BASIC_INFO),
                          $0,
                          &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / 1024 / 1024 // MB
        } else {
            return Double.random(in: 50...100) // 模拟值
        }
    }
    
    private func getCurrentCPUUsage() -> Double {
        // 简化的CPU使用率（模拟）
        return Double.random(in: 10...40)
    }
    
    private func getCurrentFPS() -> Double {
        // 简化的FPS计算（模拟）
        return Double.random(in: 55...60)
    }
    
    private func calculateBatteryDrainRate() -> Double {
        // 简化的电池消耗率计算（模拟）
        return Double.random(in: 0.5...2.0) // %/小时
    }
    
    @objc private func handleErrorOccurred() {
        stabilityMetrics.errorCount += 1
    }
    
    private func generateStabilityReport() {
        var issues: [TestIssue] = []
        var details: [String: String] = [:]
        
        details["运行时间"] = stabilityMetrics.uptime.formatted()
        details["平均内存使用"] = String(format: "%.1f MB", stabilityMetrics.averageMemoryUsage)
        details["峰值内存使用"] = String(format: "%.1f MB", stabilityMetrics.peakMemoryUsage)
        details["平均CPU使用率"] = String(format: "%.1f%%", stabilityMetrics.averageCPUUsage)
        details["平均FPS"] = String(format: "%.1f", stabilityMetrics.averageFPS)
        details["错误数量"] = "\(stabilityMetrics.errorCount)"
        details["崩溃数量"] = "\(stabilityMetrics.crashCount)"
        
        // 分析问题
        if stabilityMetrics.crashCount > 0 {
            issues.append(TestIssue(
                severity: .critical,
                category: "稳定性",
                description: "检测到\(stabilityMetrics.crashCount)次崩溃",
                suggestion: "检查崩溃日志并修复相关问题"
            ))
        }
        
        if stabilityMetrics.memoryLeakCount > 5 {
            issues.append(TestIssue(
                severity: .major,
                category: "内存",
                description: "检测到可能的内存泄漏",
                suggestion: "使用内存分析工具检查内存泄漏"
            ))
        }
        
        if stabilityMetrics.averageFPS < 50 {
            issues.append(TestIssue(
                severity: .minor,
                category: "性能",
                description: "平均帧率偏低",
                suggestion: "优化动画和渲染性能"
            ))
        }
        
        if stabilityMetrics.errorCount > 10 {
            issues.append(TestIssue(
                severity: .major,
                category: "错误处理",
                description: "错误数量过多",
                suggestion: "改进错误处理机制"
            ))
        }
        
        let result: TestingResult = {
            if issues.contains(where: { $0.severity == .critical }) {
                return .failed
            } else if issues.contains(where: { $0.severity == .major }) {
                return .warning
            } else {
                return .passed
            }
        }()
        
        let report = TestReport(
            testType: "长时间稳定性测试",
            startTime: stabilityMetrics.startTime,
            endTime: Date(),
            duration: stabilityMetrics.uptime,
            result: result,
            details: details,
            issues: issues
        )
        
        testReports.append(report)
        saveTestReports()
    }
    
    // MARK: - Data Persistence
    
    private func loadTestReports() {
        if let data = UserDefaults.standard.data(forKey: "TestReports"),
           let decoded = try? JSONDecoder().decode([TestReport].self, from: data) {
            testReports = decoded
        }
    }
    
    private func saveTestReports() {
        if let encoded = try? JSONEncoder().encode(testReports) {
            UserDefaults.standard.set(encoded, forKey: "TestReports")
        }
    }
    
    /// 清除测试报告
    func clearTestReports() {
        testReports.removeAll()
        saveTestReports()
    }
    
    /// 导出测试报告
    func exportTestReports() -> String {
        var report = "测试报告导出\n"
        report += "导出时间: \(Date())\n\n"
        
        for testReport in testReports {
            report += "===== \(testReport.testType) =====\n"
            report += "开始时间: \(testReport.startTime)\n"
            report += "结束时间: \(testReport.endTime)\n"
            report += "持续时间: \(testReport.durationString)\n"
            report += "测试结果: \(testReport.result.displayName)\n\n"
            
            if !testReport.details.isEmpty {
                report += "详细信息:\n"
                for (key, value) in testReport.details {
                    report += "  \(key): \(value)\n"
                }
                report += "\n"
            }
            
            if !testReport.issues.isEmpty {
                report += "发现的问题:\n"
                for issue in testReport.issues {
                    report += "  [\(issue.severity.displayName)] \(issue.category): \(issue.description)\n"
                    report += "    建议: \(issue.suggestion)\n"
                }
                report += "\n"
            }
            
            report += "\n"
        }
        
        return report
    }
    
    deinit {
        stabilityTimer?.invalidate()
        metricsUpdateTimer?.invalidate()
        NotificationCenter.default.removeObserver(self)
    }
} 