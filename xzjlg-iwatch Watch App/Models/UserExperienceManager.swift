//
//  UserExperienceManager.swift
//  xzjlg-iwatch Watch App
//
//  Created by Assistant on 2025/1/20.
//

import Foundation
import SwiftUI
import WatchKit
import UIKit

/// 触觉反馈类型
enum HapticFeedbackType: String, CaseIterable {
    case success = "success"
    case warning = "warning"
    case error = "error"
    case selection = "selection"
    case impact = "impact"
    case notification = "notification"
    case milestone = "milestone"
    case goalComplete = "goal_complete"
    
    var displayName: String {
        switch self {
        case .success: return "成功"
        case .warning: return "警告"
        case .error: return "错误"
        case .selection: return "选择"
        case .impact: return "冲击"
        case .notification: return "通知"
        case .milestone: return "里程碑"
        case .goalComplete: return "目标完成"
        }
    }
}

/// UI性能优化级别
enum UIPerformanceLevel: String, CaseIterable, Codable {
    case high = "high"
    case medium = "medium"
    case low = "low"
    case minimal = "minimal"
    
    var displayName: String {
        switch self {
        case .high: return "高性能"
        case .medium: return "平衡"
        case .low: return "节能"
        case .minimal: return "最小化"
        }
    }
    
    var animationDuration: Double {
        switch self {
        case .high: return 0.3
        case .medium: return 0.25
        case .low: return 0.2
        case .minimal: return 0.1
        }
    }
    
    var refreshRate: Double {
        switch self {
        case .high: return 1.0
        case .medium: return 2.0
        case .low: return 5.0
        case .minimal: return 10.0
        }
    }
}

/// 用户体验设置
struct UserExperienceSettings: Codable {
    var hapticFeedbackEnabled: Bool
    var performanceLevel: UIPerformanceLevel
    var reduceMotion: Bool
    var highContrast: Bool
    var largerText: Bool
    var soundFeedbackEnabled: Bool
    
    static let `default` = UserExperienceSettings(
        hapticFeedbackEnabled: true,
        performanceLevel: .medium,
        reduceMotion: false,
        highContrast: false,
        largerText: false,
        soundFeedbackEnabled: true
    )
}

/// 用户体验管理器
class UserExperienceManager: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = UserExperienceManager()
    
    // MARK: - Published Properties
    
    @Published var settings: UserExperienceSettings = .default
    @Published var currentPerformanceLevel: UIPerformanceLevel = .medium
    @Published var isOptimizingForBattery: Bool = false
    
    // MARK: - Private Properties
    
    private let hapticEngine = WKHapticType.self
    private var performanceTimer: Timer?
    private var lastFrameTime: CFTimeInterval = 0
    private var frameCount: Int = 0
    private var averageFPS: Double = 60.0
    
    // MARK: - Initialization
    
    private init() {
        loadSettings()
        setupPerformanceMonitoring()
        applyAccessibilitySettings()
    }
    
    // MARK: - Public Interface
    
    /// 触发触觉反馈
    func triggerHapticFeedback(_ type: HapticFeedbackType) {
        guard settings.hapticFeedbackEnabled else { return }
        
        DispatchQueue.main.async {
            switch type {
            case .success:
                WKInterfaceDevice.current().play(.success)
            case .warning, .error:
                WKInterfaceDevice.current().play(.failure)
            case .selection, .impact:
                WKInterfaceDevice.current().play(.click)
            case .notification:
                WKInterfaceDevice.current().play(.notification)
            case .milestone:
                // 自定义组合反馈
                self.playCustomMilestoneHaptic()
            case .goalComplete:
                // 自定义目标完成反馈
                self.playCustomGoalCompleteHaptic()
            }
        }
    }
    
    /// 更新性能级别
    func updatePerformanceLevel(_ level: UIPerformanceLevel) {
        settings.performanceLevel = level
        currentPerformanceLevel = level
        saveSettings()
        
        // 通知其他组件性能级别变化
        NotificationCenter.default.post(
            name: .performanceLevelChanged,
            object: level
        )
    }
    
    /// 优化电池使用
    func optimizeForBattery(_ optimize: Bool) {
        isOptimizingForBattery = optimize
        
        if optimize {
            currentPerformanceLevel = .minimal
        } else {
            currentPerformanceLevel = settings.performanceLevel
        }
        
        NotificationCenter.default.post(
            name: .batteryOptimizationChanged,
            object: optimize
        )
    }
    
    /// 应用无障碍设置
    func applyAccessibilitySettings() {
        // watchOS 可访问性设置检查
        // 注意：部分UIAccessibility API在watchOS中不可用，使用替代方案
        #if os(iOS)
        if UIAccessibility.isReduceMotionEnabled {
            settings.reduceMotion = true
        }
        
        if UIAccessibility.isDarkerSystemColorsEnabled {
            settings.highContrast = true
        }
        
        if UIAccessibility.isBoldTextEnabled {
            settings.largerText = true
        }
        #else
        // watchOS 替代方案 - 使用保守的默认设置
        settings.reduceMotion = false
        settings.highContrast = false
        settings.largerText = false
        #endif
    }
    
    /// 获取当前动画持续时间
    func getAnimationDuration() -> Double {
        if settings.reduceMotion {
            return 0.1
        }
        return currentPerformanceLevel.animationDuration
    }
    
    /// 获取当前刷新间隔
    func getRefreshInterval() -> Double {
        return currentPerformanceLevel.refreshRate
    }
    
    // MARK: - Performance Monitoring
    
    private func setupPerformanceMonitoring() {
        performanceTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updatePerformanceMetrics()
        }
    }
    
    private func updatePerformanceMetrics() {
        let currentTime = Date().timeIntervalSince1970
        
        if lastFrameTime > 0 {
            let deltaTime = currentTime - lastFrameTime
            let fps = 1.0 / deltaTime
            
            // 计算平均FPS
            averageFPS = (averageFPS * 0.9) + (fps * 0.1)
            
            // 根据性能自动调整
            autoAdjustPerformance()
        }
        
        lastFrameTime = currentTime
        frameCount += 1
    }
    
    private func autoAdjustPerformance() {
        guard !isOptimizingForBattery else { return }
        
        // 如果FPS过低，自动降低性能级别
        if averageFPS < 30 && currentPerformanceLevel != .minimal {
            let newLevel: UIPerformanceLevel
            switch currentPerformanceLevel {
            case .high: newLevel = .medium
            case .medium: newLevel = .low
            case .low: newLevel = .minimal
            case .minimal: newLevel = .minimal
            }
            updatePerformanceLevel(newLevel)
        }
        // 如果FPS稳定在高水平，可以提升性能级别
        else if averageFPS > 55 && currentPerformanceLevel != settings.performanceLevel {
            let targetLevel = settings.performanceLevel
            if currentPerformanceLevel.rawValue < targetLevel.rawValue {
                let newLevel: UIPerformanceLevel
                switch currentPerformanceLevel {
                case .minimal: newLevel = .low
                case .low: newLevel = .medium
                case .medium: newLevel = .high
                case .high: newLevel = .high
                }
                updatePerformanceLevel(newLevel)
            }
        }
    }
    
    // MARK: - Custom Haptic Patterns
    
    private func playCustomMilestoneHaptic() {
        DispatchQueue.main.async {
            WKInterfaceDevice.current().play(.success)
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                WKInterfaceDevice.current().play(.click)
            }
        }
    }
    
    private func playCustomGoalCompleteHaptic() {
        DispatchQueue.main.async {
            WKInterfaceDevice.current().play(.success)
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                WKInterfaceDevice.current().play(.success)
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
                WKInterfaceDevice.current().play(.notification)
            }
        }
    }
    
    // MARK: - Settings Management
    
    private func loadSettings() {
        if let data = UserDefaults.standard.data(forKey: "UserExperienceSettings"),
           let decoded = try? JSONDecoder().decode(UserExperienceSettings.self, from: data) {
            settings = decoded
            currentPerformanceLevel = decoded.performanceLevel
        }
    }
    
    private func saveSettings() {
        if let encoded = try? JSONEncoder().encode(settings) {
            UserDefaults.standard.set(encoded, forKey: "UserExperienceSettings")
        }
    }
    
    // MARK: - Memory Management
    
    deinit {
        performanceTimer?.invalidate()
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let performanceLevelChanged = Notification.Name("performanceLevelChanged")
    static let batteryOptimizationChanged = Notification.Name("batteryOptimizationChanged")
    static let hapticFeedbackTriggered = Notification.Name("hapticFeedbackTriggered")
} 