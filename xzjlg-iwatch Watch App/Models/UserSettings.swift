//
//  UserSettings.swift
//  xzjlg-iwatch Watch App
//
//  Created by <PERSON><PERSON> on 2025/7/25.
//

import Foundation

// 为了使用NotificationSettings，我们需要在这里重新声明
// 或者可以将NotificationSettings移到单独的文件中

/// 通知设置（临时在此声明，避免循环依赖）
struct NotificationSettings: Codable, Equatable {
    var isEnabled: Bool = true
    var salaryThresholdEnabled: Bool = true
    var goalCompletionEnabled: Bool = true
    var milestoneEnabled: Bool = true
    var workReminderEnabled: Bool = true
    var batteryOptimizationEnabled: Bool = true
    var quietHoursStart: Int = 22 // 22:00
    var quietHoursEnd: Int = 8    // 08:00
    var maxNotificationsPerHour: Int = 5
    
    static let `default` = NotificationSettings()
}

/// 用户设置结构体，包含工资计算相关的配置参数
struct UserSettings: Codable, Equatable {
    // MARK: - Core Settings
    
    /// 月薪金额 (最小1000，最大1000000)
    private var _monthlySalary: Double = 9500.0
    var monthlySalary: Double {
        get { _monthlySalary }
        set { _monthlySalary = max(1000, min(1000000, newValue)) }
    }
    
    /// 每月工作天数 (最小15天，最大31天)
    private var _workDaysPerMonth: Int = 22
    var workDaysPerMonth: Int {
        get { _workDaysPerMonth }
        set { _workDaysPerMonth = max(15, min(31, newValue)) }
    }
    
    /// 每日工作小时数 (最小4小时，最大16小时)
    private var _workHoursPerDay: Int = 8
    var workHoursPerDay: Int {
        get { _workHoursPerDay }
        set { _workHoursPerDay = max(4, min(16, newValue)) }
    }
    
    /// 是否启用周末工作
    var isWeekendWorkEnabled: Bool = true
    
    /// 加班费率倍数 (1.0x - 3.0x)
    private var _overtimeRate: Double = 1.5
    var overtimeRate: Double {
        get { _overtimeRate }
        set { _overtimeRate = max(1.0, min(3.0, newValue)) }
    }
    
    /// 通知设置
    var notificationSettings: NotificationSettings = .default
    
    // MARK: - Advanced Settings
    
    /// 工作开始时间 (默认9点)
    private var _workStartHour: Int = 9
    var workStartHour: Int {
        get { _workStartHour }
        set { _workStartHour = max(0, min(23, newValue)) }
    }
    
    /// 工作结束时间 (默认18点)
    private var _workEndHour: Int = 18
    var workEndHour: Int {
        get { _workEndHour }
        set { _workEndHour = max(workStartHour + 1, min(23, newValue)) }
    }
    
    /// 最早加班开始时间 (默认7点)
    var earliestOvertimeHour: Int = 7
    
    /// 最晚加班结束时间 (默认22点)
    var latestOvertimeHour: Int = 22
    
    /// 午休时长（分钟）
    var lunchBreakMinutes: Int = 60
    
    /// 税率 (用于净收入计算)
    private var _taxRate: Double = 0.0
    var taxRate: Double {
        get { _taxRate }
        set { _taxRate = max(0.0, min(0.5, newValue)) }
    }
    
    // MARK: - Computed Properties
    
    /// 计算每日基础工资
    var dailyBaseSalary: Double {
        return monthlySalary / Double(workDaysPerMonth)
    }
    
    /// 计算每小时基础工资
    var hourlyBaseSalary: Double {
        return dailyBaseSalary / Double(workHoursPerDay)
    }
    
    /// 计算每分钟基础工资
    var minuteBaseSalary: Double {
        return hourlyBaseSalary / 60.0
    }
    
    /// 计算加班时的每分钟工资
    var overtimeMinuteSalary: Double {
        return minuteBaseSalary * overtimeRate
    }
    
    /// 计算每秒基础工资
    var secondBaseSalary: Double {
        return minuteBaseSalary / 60.0
    }
    
    /// 计算税后每日基础工资
    var dailyBaseSalaryAfterTax: Double {
        return dailyBaseSalary * (1.0 - taxRate)
    }
    
    /// 计算实际工作小时数（扣除午休）
    var effectiveWorkHoursPerDay: Double {
        return Double(workHoursPerDay) - (Double(lunchBreakMinutes) / 60.0)
    }
    
    /// 计算基于有效工作时间的时薪
    var effectiveHourlySalary: Double {
        return dailyBaseSalary / effectiveWorkHoursPerDay
    }
    
    /// 获取工作时间范围描述
    var workTimeDescription: String {
        return "\(workStartHour):00 - \(workEndHour):00"
    }
    
    /// 获取加班时间范围描述
    var overtimeDescription: String {
        return "\(earliestOvertimeHour):00-\(workStartHour):00, \(workEndHour):00-\(latestOvertimeHour):00"
    }
    
    // MARK: - Initialization
    
    init(
        monthlySalary: Double = 9500.0,
        workDaysPerMonth: Int = 22,
        workHoursPerDay: Int = 8,
        isWeekendWorkEnabled: Bool = true,
        overtimeRate: Double = 1.5,
        notificationSettings: NotificationSettings = .default,
        workStartHour: Int = 9,
        workEndHour: Int = 18
    ) {
        self.monthlySalary = monthlySalary
        self.workDaysPerMonth = workDaysPerMonth
        self.workHoursPerDay = workHoursPerDay
        self.isWeekendWorkEnabled = isWeekendWorkEnabled
        self.overtimeRate = overtimeRate
        self.notificationSettings = notificationSettings
        self.workStartHour = workStartHour
        self.workEndHour = workEndHour
    }
}

// MARK: - Default Settings & Presets
extension UserSettings {
    /// 默认设置
    static let `default` = UserSettings()
    
    /// 高薪预设
    static let highSalary = UserSettings(
        monthlySalary: 25000,
        workDaysPerMonth: 22,
        workHoursPerDay: 8,
        overtimeRate: 2.0
    )
    
    /// 程序员预设
    static let programmer = UserSettings(
        monthlySalary: 18000,
        workDaysPerMonth: 22,
        workHoursPerDay: 9,
        isWeekendWorkEnabled: true,
        overtimeRate: 1.5,
        workStartHour: 10,
        workEndHour: 19
    )
    
    /// 销售预设
    static let sales = UserSettings(
        monthlySalary: 12000,
        workDaysPerMonth: 26,
        workHoursPerDay: 8,
        isWeekendWorkEnabled: true,
        overtimeRate: 1.2
    )
    
    /// 验证设置是否有效
    var isValid: Bool {
        return monthlySalary > 0 &&
               workDaysPerMonth > 0 &&
               workHoursPerDay > 0 &&
               overtimeRate >= 1.0 &&
               overtimeRate <= 3.0 &&
               workStartHour < workEndHour &&
               earliestOvertimeHour <= workStartHour &&
               workEndHour <= latestOvertimeHour &&
               taxRate >= 0.0 &&
               taxRate <= 0.5
    }
    
    /// 获取验证错误信息
    var validationErrors: [String] {
        var errors: [String] = []
        
        if monthlySalary <= 0 {
            errors.append("月薪必须大于0")
        }
        if workDaysPerMonth <= 0 || workDaysPerMonth > 31 {
            errors.append("每月工作天数必须在1-31天之间")
        }
        if workHoursPerDay <= 0 || workHoursPerDay > 16 {
            errors.append("每日工作小时数必须在1-16小时之间")
        }
        if overtimeRate < 1.0 || overtimeRate > 3.0 {
            errors.append("加班费率必须在1.0-3.0倍之间")
        }
        if workStartHour >= workEndHour {
            errors.append("工作开始时间必须早于结束时间")
        }
        if taxRate < 0.0 || taxRate > 0.5 {
            errors.append("税率必须在0-50%之间")
        }
        
        return errors
    }
    
    /// 重置为默认值
    mutating func resetToDefault() {
        self = .default
    }
    
    /// 应用预设配置
    mutating func applyPreset(_ preset: UserSettings) {
        self = preset
    }
}

// MARK: - Calculation Helpers
extension UserSettings {
    /// 计算指定时间段的收入
    func calculateEarnings(for duration: TimeInterval, isOvertime: Bool = false) -> Double {
        let minutes = duration / 60.0
        let rate = isOvertime ? overtimeMinuteSalary : minuteBaseSalary
        return minutes * rate
    }
    
    /// 计算到达目标金额需要的工作时间
    func timeToReach(amount: Double, isOvertime: Bool = false) -> TimeInterval {
        let rate = isOvertime ? overtimeMinuteSalary : minuteBaseSalary
        guard rate > 0 else { return 0 }
        return (amount / rate) * 60.0 // 转换为秒
    }
    
    /// 获取工作时间段信息
    func getWorkPeriods() -> WorkPeriods {
        return WorkPeriods(
            normalWork: (start: workStartHour, end: workEndHour),
            morningOvertime: (start: earliestOvertimeHour, end: workStartHour),
            eveningOvertime: (start: workEndHour, end: latestOvertimeHour)
        )
    }
}

// MARK: - Supporting Types
struct WorkPeriods {
    let normalWork: (start: Int, end: Int)
    let morningOvertime: (start: Int, end: Int)
    let eveningOvertime: (start: Int, end: Int)
}