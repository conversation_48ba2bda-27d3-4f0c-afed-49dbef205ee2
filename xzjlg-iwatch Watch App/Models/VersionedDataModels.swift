//
//  VersionedDataModels.swift
//  xzjlg-iwatch Watch App
//
//  Created by Assistant on 2025/1/20.
//

import Foundation

// MARK: - Versioned UserSettings

/// 版本化用户设置
struct VersionedUserSettings: VersionedData, DataIntegrityValidator {
    static let currentVersion: Int = 2
    
    var version: Int = currentVersion
    var monthlySalary: Double
    var workDaysPerMonth: Int
    var workHoursPerDay: Int
    var isWeekendWorkEnabled: Bool
    var overtimeRate: Double
    var workStartHour: Int
    var workEndHour: Int
    var notificationSettings: VersionedNotificationSettings
    
    // V2 新增字段
    var earliestOvertimeHour: Int?
    var latestOvertimeHour: Int?
    var lunchBreakMinutes: Int?
    var taxRate: Double?
    
    init(from userSettings: UserSettings) {
        self.monthlySalary = userSettings.monthlySalary
        self.workDaysPerMonth = userSettings.workDaysPerMonth
        self.workHoursPerDay = userSettings.workHoursPerDay
        self.isWeekendWorkEnabled = userSettings.isWeekendWorkEnabled
        self.overtimeRate = userSettings.overtimeRate
        self.workStartHour = userSettings.workStartHour
        self.workEndHour = userSettings.workEndHour
        self.notificationSettings = VersionedNotificationSettings(from: userSettings.notificationSettings)
        
        // V2 字段
        self.earliestOvertimeHour = userSettings.earliestOvertimeHour
        self.latestOvertimeHour = userSettings.latestOvertimeHour
        self.lunchBreakMinutes = userSettings.lunchBreakMinutes
        self.taxRate = userSettings.taxRate
    }
    
    func migrateToCurrentVersion() -> VersionedUserSettings? {
        var migrated = self
        
        switch version {
        case 1:
            // V1 -> V2 迁移
            migrated.earliestOvertimeHour = migrated.earliestOvertimeHour ?? 7
            migrated.latestOvertimeHour = migrated.latestOvertimeHour ?? 22
            migrated.lunchBreakMinutes = migrated.lunchBreakMinutes ?? 60
            migrated.taxRate = migrated.taxRate ?? 0.0
            migrated.version = 2
            fallthrough
        case Self.currentVersion:
            return migrated
        default:
            return nil
        }
    }
    
    func validate() -> Bool {
        guard monthlySalary >= 1000 && monthlySalary <= 1000000 else { return false }
        guard workDaysPerMonth >= 15 && workDaysPerMonth <= 31 else { return false }
        guard workHoursPerDay >= 4 && workHoursPerDay <= 16 else { return false }
        guard overtimeRate >= 1.0 && overtimeRate <= 3.0 else { return false }
        guard workStartHour >= 0 && workStartHour <= 23 else { return false }
        guard workEndHour >= 0 && workEndHour <= 23 else { return false }
        guard workEndHour > workStartHour else { return false }
        
        if let taxRate = taxRate {
            guard taxRate >= 0.0 && taxRate <= 0.5 else { return false }
        }
        
        return notificationSettings.validate()
    }
    
    mutating func repair() -> Bool {
        var repaired = false
        
        if monthlySalary < 1000 || monthlySalary > 1000000 {
            monthlySalary = 9500
            repaired = true
        }
        
        if workDaysPerMonth < 15 || workDaysPerMonth > 31 {
            workDaysPerMonth = 22
            repaired = true
        }
        
        if workHoursPerDay < 4 || workHoursPerDay > 16 {
            workHoursPerDay = 8
            repaired = true
        }
        
        if overtimeRate < 1.0 || overtimeRate > 3.0 {
            overtimeRate = 1.5
            repaired = true
        }
        
        if workStartHour < 0 || workStartHour > 23 {
            workStartHour = 9
            repaired = true
        }
        
        if workEndHour < 0 || workEndHour > 23 || workEndHour <= workStartHour {
            workEndHour = max(18, workStartHour + 8)
            repaired = true
        }
        
        if let taxRate = taxRate, (taxRate < 0.0 || taxRate > 0.5) {
            self.taxRate = 0.0
            repaired = true
        }
        
        if !notificationSettings.validate() {
            notificationSettings = VersionedNotificationSettings()
            repaired = true
        }
        
        return repaired
    }
    
    func toUserSettings() -> UserSettings {
        var settings = UserSettings()
        settings.monthlySalary = monthlySalary
        settings.workDaysPerMonth = workDaysPerMonth
        settings.workHoursPerDay = workHoursPerDay
        settings.isWeekendWorkEnabled = isWeekendWorkEnabled
        settings.overtimeRate = overtimeRate
        settings.workStartHour = workStartHour
        settings.workEndHour = workEndHour
        settings.notificationSettings = notificationSettings.toNotificationSettings()
        
        if let earliestOvertimeHour = earliestOvertimeHour {
            settings.earliestOvertimeHour = earliestOvertimeHour
        }
        if let latestOvertimeHour = latestOvertimeHour {
            settings.latestOvertimeHour = latestOvertimeHour
        }
        if let lunchBreakMinutes = lunchBreakMinutes {
            settings.lunchBreakMinutes = lunchBreakMinutes
        }
        if let taxRate = taxRate {
            settings.taxRate = taxRate
        }
        
        return settings
    }
}

// MARK: - Versioned NotificationSettings

/// 版本化通知设置
struct VersionedNotificationSettings: VersionedData, DataIntegrityValidator {
    static let currentVersion: Int = 1
    
    var version: Int = currentVersion
    var isEnabled: Bool
    var salaryThresholdEnabled: Bool
    var goalCompletionEnabled: Bool
    var milestoneEnabled: Bool
    var workReminderEnabled: Bool
    var batteryOptimizationEnabled: Bool
    var quietHoursStart: Int
    var quietHoursEnd: Int
    var maxNotificationsPerHour: Int
    
    init() {
        self.isEnabled = true
        self.salaryThresholdEnabled = true
        self.goalCompletionEnabled = true
        self.milestoneEnabled = true
        self.workReminderEnabled = true
        self.batteryOptimizationEnabled = true
        self.quietHoursStart = 22
        self.quietHoursEnd = 8
        self.maxNotificationsPerHour = 5
    }
    
    init(from notificationSettings: NotificationSettings) {
        self.isEnabled = notificationSettings.isEnabled
        self.salaryThresholdEnabled = notificationSettings.salaryThresholdEnabled
        self.goalCompletionEnabled = notificationSettings.goalCompletionEnabled
        self.milestoneEnabled = notificationSettings.milestoneEnabled
        self.workReminderEnabled = notificationSettings.workReminderEnabled
        self.batteryOptimizationEnabled = notificationSettings.batteryOptimizationEnabled
        self.quietHoursStart = notificationSettings.quietHoursStart
        self.quietHoursEnd = notificationSettings.quietHoursEnd
        self.maxNotificationsPerHour = notificationSettings.maxNotificationsPerHour
    }
    
    func migrateToCurrentVersion() -> VersionedNotificationSettings? {
        let migrated = self
        
        switch version {
        case Self.currentVersion:
            return migrated
        default:
            return nil
        }
    }
    
    func validate() -> Bool {
        guard quietHoursStart >= 0 && quietHoursStart <= 23 else { return false }
        guard quietHoursEnd >= 0 && quietHoursEnd <= 23 else { return false }
        guard maxNotificationsPerHour >= 1 && maxNotificationsPerHour <= 20 else { return false }
        return true
    }
    
    mutating func repair() -> Bool {
        var repaired = false
        
        if quietHoursStart < 0 || quietHoursStart > 23 {
            quietHoursStart = 22
            repaired = true
        }
        
        if quietHoursEnd < 0 || quietHoursEnd > 23 {
            quietHoursEnd = 8
            repaired = true
        }
        
        if maxNotificationsPerHour < 1 || maxNotificationsPerHour > 20 {
            maxNotificationsPerHour = 5
            repaired = true
        }
        
        return repaired
    }
    
    func toNotificationSettings() -> NotificationSettings {
        var settings = NotificationSettings()
        settings.isEnabled = isEnabled
        settings.salaryThresholdEnabled = salaryThresholdEnabled
        settings.goalCompletionEnabled = goalCompletionEnabled
        settings.milestoneEnabled = milestoneEnabled
        settings.workReminderEnabled = workReminderEnabled
        settings.batteryOptimizationEnabled = batteryOptimizationEnabled
        settings.quietHoursStart = quietHoursStart
        settings.quietHoursEnd = quietHoursEnd
        settings.maxNotificationsPerHour = maxNotificationsPerHour
        return settings
    }
}

// MARK: - Versioned Goals

/// 版本化目标列表
struct VersionedGoals: VersionedData, DataIntegrityValidator {
    static let currentVersion: Int = 1
    
    var version: Int = currentVersion
    var goals: [Goal]
    
    init(goals: [Goal], version: Int = currentVersion) {
        self.goals = goals
        self.version = version
    }
    
    func migrateToCurrentVersion() -> VersionedGoals? {
        let migrated = self
        
        switch version {
        case Self.currentVersion:
            return migrated
        default:
            return nil
        }
    }
    
    func validate() -> Bool {
        for goal in goals {
            // 验证目标的基本字段
            if goal.name.isEmpty { return false }
            if goal.targetAmount <= 0 || goal.targetAmount > 1000000 { return false }
            if goal.currentAmount < 0 || goal.currentAmount > goal.targetAmount * 1.1 { return false }
            
            // 验证分配权重
            if goal.allocationWeight <= 0 || goal.allocationWeight > 10 { return false }
        }
        
        // 检查重复ID
        let uniqueIds = Set(goals.map { $0.id })
        if uniqueIds.count != goals.count { return false }
        
        return true
    }
    
    mutating func repair() -> Bool {
        var repaired = false
        var repairedGoals: [Goal] = []
        var usedIds: Set<UUID> = []
        
        for goal in goals {
            var currentGoal = goal
            var goalNeedsRepair = false
            
            // 检查是否需要修复
            if !goal.validate() {
                currentGoal = goal.repaired()
                goalNeedsRepair = true
            }
            
            // 修复重复ID
            if usedIds.contains(currentGoal.id) {
                currentGoal = Goal(
                    id: UUID(),
                    name: currentGoal.name,
                    targetAmount: currentGoal.targetAmount,
                    currentAmount: currentGoal.currentAmount,
                    lastUpdated: currentGoal.lastUpdated,
                    createdAt: currentGoal.createdAt,
                    priority: currentGoal.priority,
                    category: currentGoal.category,
                    description: currentGoal.description,
                    targetDate: currentGoal.targetDate,
                    isAutoAllocationEnabled: currentGoal.isAutoAllocationEnabled,
                    allocationWeight: currentGoal.allocationWeight
                )
                goalNeedsRepair = true
            }
            
            usedIds.insert(currentGoal.id)
            repairedGoals.append(currentGoal)
            
            if goalNeedsRepair {
                repaired = true
            }
        }
        
        if repaired {
            self.goals = repairedGoals
        }
        
        return repaired
    }
}

// MARK: - Versioned NotificationHistory

/// 版本化通知历史
struct VersionedNotificationHistory: VersionedData, DataIntegrityValidator {
    static let currentVersion: Int = 1
    
    var version: Int = currentVersion
    var history: [NotificationHistory]
    
    init(history: [NotificationHistory]) {
        self.history = history
    }
    
    func migrateToCurrentVersion() -> VersionedNotificationHistory? {
        let migrated = self
        
        switch version {
        case Self.currentVersion:
            return migrated
        default:
            return nil
        }
    }
    
    func validate() -> Bool {
        // 检查历史记录数量限制
        if history.count > 200 { return false }
        
        // 检查时间戳的合理性
        let now = Date()
        let oneYearAgo = Calendar.current.date(byAdding: .year, value: -1, to: now) ?? now
        
        for record in history {
            if record.timestamp > now || record.timestamp < oneYearAgo {
                return false
            }
            if record.content.isEmpty || record.content.count > 500 {
                return false
            }
        }
        
        return true
    }
    
    mutating func repair() -> Bool {
        var repaired = false
        var repairedHistory: [NotificationHistory] = []
        
        let now = Date()
        let oneYearAgo = Calendar.current.date(byAdding: .year, value: -1, to: now) ?? now
        
        for record in history {
            // 修复时间戳
            var fixedTimestamp = record.timestamp
            if record.timestamp > now {
                fixedTimestamp = now
                repaired = true
            } else if record.timestamp < oneYearAgo {
                continue // 删除过老的记录
            }
            
            // 修复内容
            var fixedContent = record.content
            if record.content.isEmpty {
                fixedContent = "已修复的通知记录"
                repaired = true
            } else if record.content.count > 500 {
                fixedContent = String(record.content.prefix(500))
                repaired = true
            }
            
            // 创建修复后的记录
            let repairedRecord = NotificationHistory(
                type: record.type,
                title: record.title,
                content: fixedContent,
                timestamp: fixedTimestamp,
                isRead: record.isRead,
                associatedAmount: record.associatedAmount,
                associatedGoalId: record.associatedGoalId,
                priority: record.priority,
                metadata: record.metadata
            )
            
            repairedHistory.append(repairedRecord)
        }
        
        // 限制记录数量
        if repairedHistory.count > 100 {
            repairedHistory = Array(repairedHistory.suffix(100))
            repaired = true
        }
        
        if repaired {
            self.history = repairedHistory
        }
        
        return repaired
    }
}

// MARK: - Goal Extensions for Data Integrity

extension Goal {
    /// 验证目标数据的完整性
    func validate() -> Bool {
        if name.isEmpty { return false }
        if targetAmount <= 0 || targetAmount > 1000000 { return false }
        if currentAmount < 0 || currentAmount > targetAmount * 1.1 { return false }
        if allocationWeight <= 0 || allocationWeight > 10 { return false }
        return true
    }
    
    /// 创建修复后的目标数据
    func repaired() -> Goal {
        let repairedName = name.isEmpty ? "未命名目标" : name
        let repairedTargetAmount = (targetAmount <= 0 || targetAmount > 1000000) ? 5000 : targetAmount
        let repairedCurrentAmount = max(0, min(currentAmount, repairedTargetAmount))
        let repairedAllocationWeight = (allocationWeight <= 0 || allocationWeight > 10) ? 1.0 : allocationWeight
        
        return Goal(
            id: id,
            name: repairedName,
            targetAmount: repairedTargetAmount,
            currentAmount: repairedCurrentAmount,
            lastUpdated: lastUpdated,
            createdAt: createdAt,
            priority: priority,
            category: category,
            description: description,
            targetDate: targetDate,
            isAutoAllocationEnabled: isAutoAllocationEnabled,
            allocationWeight: repairedAllocationWeight
        )
    }
} 