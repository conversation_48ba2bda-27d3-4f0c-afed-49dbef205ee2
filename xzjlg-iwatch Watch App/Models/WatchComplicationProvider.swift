//
//  WatchComplicationProvider.swift
//  xzjlg-iwatch Watch App
//
//  Created by Assistant on 2024-12-28.
//

import Foundation
import SwiftUI
import ClockKit

/// Watch表盘复杂功能数据提供者
/// 为Apple Watch表盘提供工资和目标进度信息
class WatchComplicationProvider: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = WatchComplicationProvider()
    
    // MARK: - Properties
    
    @Published var currentEarnings: Double = 0.0
    @Published var dailyProgress: Double = 0.0
    @Published var goalProgress: Double = 0.0
    @Published var lastUpdateTime: Date = Date()
    
    private let persistenceManager = DataPersistenceManager.shared
    
    // MARK: - Initialization
    
    private init() {
        updateComplicationData()
    }
    
    // MARK: - Public Methods
    
    /// 更新复杂功能数据
    func updateComplicationData() {
        DispatchQueue.main.async { [weak self] in
            self?.currentEarnings = self?.persistenceManager.loadDailyEarnings() ?? 0.0
            self?.dailyProgress = self?.calculateDailyProgress() ?? 0.0
            self?.goalProgress = self?.calculateGoalProgress() ?? 0.0
            self?.lastUpdateTime = Date()
            
            // 请求更新表盘复杂功能
            self?.requestComplicationUpdate()
        }
    }
    
    /// 请求更新表盘复杂功能
    private func requestComplicationUpdate() {
        // 直接调用 CLKComplicationServer 更新复杂功能
        let server = CLKComplicationServer.sharedInstance()
        for complication in server.activeComplications ?? [] {
            server.reloadTimeline(for: complication)
        }
        
        // 通知更新管理器
        ComplicationUpdateManager.shared.lastUpdateTime = Date()
    }
    
    /// 强制更新复杂功能数据
    func forceUpdateComplicationData() {
        updateComplicationData()
        
        // 立即请求系统更新
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            let server = CLKComplicationServer.sharedInstance()
            for complication in server.activeComplications ?? [] {
                server.reloadTimeline(for: complication)
            }
        }
    }
    
    /// 获取格式化的收入显示文本
    func getFormattedEarnings() -> String {
        return String(format: "¥%.0f", currentEarnings)
    }
    
    /// 获取进度显示文本
    func getProgressText() -> String {
        let percentage = Int(dailyProgress * 100)
        return "\(percentage)%"
    }
    
    /// 获取目标进度显示文本
    func getGoalProgressText() -> String {
        let percentage = Int(goalProgress * 100)
        return "目标\(percentage)%"
    }
    
    /// 获取简短状态文本
    func getShortStatusText() -> String {
        if dailyProgress >= 1.0 {
            return "已完成"
        } else if dailyProgress >= 0.5 {
            return "进行中"
        } else {
            return "开始"
        }
    }
    
    // MARK: - Private Methods
    
    /// 计算每日工作进度
    private func calculateDailyProgress() -> Double {
        let settings = persistenceManager.loadUserSettings()
        let dailyTarget = settings.dailyBaseSalary
        guard dailyTarget > 0 else { return 0.0 }
        return min(currentEarnings / dailyTarget, 1.0)
    }
    
    /// 计算目标进度
    private func calculateGoalProgress() -> Double {
        let goals = persistenceManager.loadGoals()
        guard !goals.isEmpty else { return 0.0 }
        
        let totalTarget = goals.reduce(0.0) { $0 + $1.targetAmount }
        let totalCurrent = goals.reduce(0.0) { $0 + $1.currentAmount }
        
        guard totalTarget > 0 else { return 0.0 }
        return min(totalCurrent / totalTarget, 1.0)
    }
}

// MARK: - Complication Data Structure

/// 复杂功能显示数据
struct ComplicationDisplayData {
    let earnings: String
    let progress: String
    let goalProgress: String
    let shortStatus: String
    let progressValue: Double
    let goalProgressValue: Double
    let lastUpdate: Date
    
    static func current() -> ComplicationDisplayData {
        let provider = WatchComplicationProvider.shared
        return ComplicationDisplayData(
            earnings: provider.getFormattedEarnings(),
            progress: provider.getProgressText(),
            goalProgress: provider.getGoalProgressText(),
            shortStatus: provider.getShortStatusText(),
            progressValue: provider.dailyProgress,
            goalProgressValue: provider.goalProgress,
            lastUpdate: provider.lastUpdateTime
        )
    }
}

// MARK: - AppState Extension

extension AppState {
    
    /// 更新复杂功能数据
    func updateComplicationData() {
        WatchComplicationProvider.shared.updateComplicationData()
    }
} 