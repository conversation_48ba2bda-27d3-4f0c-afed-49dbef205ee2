//
//  WorkScenario.swift
//  xzjlg-iwatch Watch App
//
//  Created by Kiro on 2025/7/25.
//

import Foundation
import SwiftUI
import Combine
import CoreMotion
import HealthKit

/// 工作场景枚举，定义不同的工作状态
enum WorkScenario: String, CaseIterable, Codable {
    /// 正常工作时间
    case normalWork = "normal_work"
    
    /// 夜间加班
    case nightOvertime = "night_overtime"
    
    /// 周末有薪工作
    case weekendWithPay = "weekend_with_pay"
    
    /// 周末自愿工作
    case weekendVoluntary = "weekend_voluntary"
    
    /// 休息状态
    case resting = "resting"
    
    /// 午休时间
    case lunchBreak = "lunch_break"
    
    /// 深度工作状态（专注模式）
    case deepWork = "deep_work"
    
    /// 会议状态
    case meeting = "meeting"
    
    /// 通勤状态
    case commuting = "commuting"
    
    /// 状态显示名称
    var displayName: String {
        switch self {
        case .normalWork:
            return "正常工作"
        case .nightOvertime:
            return "夜间加班"
        case .weekendWithPay:
            return "周末工作"
        case .weekendVoluntary:
            return "周末加班"
        case .resting:
            return "休息状态"
        case .lunchBreak:
            return "午休时间"
        case .deepWork:
            return "专注工作"
        case .meeting:
            return "开会中"
        case .commuting:
            return "通勤中"
        }
    }
    
    /// 状态描述
    var description: String {
        switch self {
        case .normalWork:
            return "正常的工作时间，保持专注"
        case .nightOvertime:
            return "夜间加班，注意休息"
        case .weekendWithPay:
            return "周末工作，获得加班费"
        case .weekendVoluntary:
            return "周末自愿工作"
        case .resting:
            return "休息时间，放松身心"
        case .lunchBreak:
            return "午休时间，补充能量"
        case .deepWork:
            return "深度专注，高效工作"
        case .meeting:
            return "会议进行中"
        case .commuting:
            return "通勤路上"
        }
    }
    
    /// 是否为工作状态
    var isWorkingState: Bool {
        switch self {
        case .normalWork, .nightOvertime, .weekendWithPay, .weekendVoluntary, .deepWork, .meeting:
            return true
        case .resting, .lunchBreak, .commuting:
            return false
        }
    }
    
    /// 是否需要计算工资
    var shouldCalculateSalary: Bool {
        switch self {
        case .normalWork, .nightOvertime, .weekendWithPay, .deepWork, .meeting:
            return true
        case .weekendVoluntary, .resting, .lunchBreak, .commuting:
            return false
        }
    }
    
    /// 加班费率倍数
    var overtimeRate: Double {
        switch self {
        case .normalWork, .deepWork, .meeting:
            return 1.0
        case .nightOvertime:
            return 1.5
        case .weekendWithPay:
            return 2.0
        case .weekendVoluntary, .resting, .lunchBreak, .commuting:
            return 0.0
        }
    }
    
    /// 建议的状态持续时间（分钟）
    var suggestedDuration: TimeInterval {
        switch self {
        case .normalWork:
            return 50 * 60 // 50分钟
        case .nightOvertime:
            return 120 * 60 // 2小时
        case .weekendWithPay, .weekendVoluntary:
            return 240 * 60 // 4小时
        case .resting:
            return 15 * 60 // 15分钟
        case .lunchBreak:
            return 60 * 60 // 1小时
        case .deepWork:
            return 90 * 60 // 90分钟
        case .meeting:
            return 60 * 60 // 1小时
        case .commuting:
            return 45 * 60 // 45分钟
        }
    }
    
    /// 优先级（用于自动切换时的判断）
    var priority: Int {
        switch self {
        case .meeting:
            return 10 // 最高优先级
        case .deepWork:
            return 9
        case .lunchBreak:
            return 8
        case .normalWork:
            return 7
        case .nightOvertime:
            return 6
        case .weekendWithPay:
            return 5
        case .commuting:
            return 4
        case .weekendVoluntary:
            return 3
        case .resting:
            return 1 // 最低优先级
        }
    }
}

// MARK: - 智能场景检测器
/// 智能工作场景检测器
class SmartScenarioDetector: ObservableObject {
    // MARK: - Published Properties
    @Published var currentScenario: WorkScenario = .resting
    @Published var detectionConfidence: Double = 0.0 // 0.0 - 1.0
    @Published var isAutoDetectionEnabled: Bool = true
    @Published var lastScenarioChange: Date = Date()
    @Published var scenarioHistory: [ScenarioHistoryEntry] = []
    
    // MARK: - Detection Settings
    @Published var workHours: ClosedRange<Int> = 9...18 // 9AM - 6PM
    @Published var lunchHours: ClosedRange<Int> = 12...13 // 12PM - 1PM
    @Published var enableLocationBasedDetection: Bool = false
    @Published var enableActivityBasedDetection: Bool = true
    @Published var enableCalendarIntegration: Bool = false
    
    // MARK: - Private Properties
    private var detectionTimer: Timer?
    private var motionManager: CMMotionManager?
    private var healthStore: HKHealthStore?
    private var lastActivityLevel: Double = 0.0
    private var consecutiveWorkMinutes: Int = 0
    private var lastRestReminder: Date = Date()
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Detection Parameters
    private let detectionInterval: TimeInterval = 60.0 // 每分钟检测一次
    private let confidenceThreshold: Double = 0.7 // 切换场景的置信度阈值
    private let maxWorkDuration: TimeInterval = 120 * 60 // 最大连续工作时间（2小时）
    private let restReminderInterval: TimeInterval = 50 * 60 // 休息提醒间隔（50分钟）
    
    // MARK: - Initialization
    init() {
        setupMotionDetection()
        setupHealthKit()
        startAutoDetection()
    }
    
    deinit {
        stopAutoDetection()
    }
    
    // MARK: - Auto Detection
    /// 开始自动检测
    func startAutoDetection() {
        guard isAutoDetectionEnabled else { return }
        
        detectionTimer = Timer.scheduledTimer(withTimeInterval: detectionInterval, repeats: true) { [weak self] _ in
            self?.performDetection()
        }
    }
    
    /// 停止自动检测
    func stopAutoDetection() {
        detectionTimer?.invalidate()
        detectionTimer = nil
    }
    
    /// 执行场景检测
    private func performDetection() {
        let detectedScenario = detectCurrentScenario()
        let confidence = calculateDetectionConfidence(for: detectedScenario)
        
        DispatchQueue.main.async { [weak self] in
            self?.updateDetectionResults(scenario: detectedScenario, confidence: confidence)
        }
    }
    
    /// 更新检测结果
    private func updateDetectionResults(scenario: WorkScenario, confidence: Double) {
        detectionConfidence = confidence
        
        // 只有在置信度足够高且场景不同时才切换
        if confidence >= confidenceThreshold && scenario != currentScenario {
            let previousScenario = currentScenario
            currentScenario = scenario
            lastScenarioChange = Date()
            
            // 记录场景变化历史
            addScenarioHistoryEntry(from: previousScenario, to: scenario, confidence: confidence)
            
            // 发送通知
            notifyScenarioChange(from: previousScenario, to: scenario)
            
            // 检查是否需要发送提醒
            checkForReminders()
        }
    }
    
    // MARK: - Core Detection Logic
    /// 检测当前工作场景
    private func detectCurrentScenario() -> WorkScenario {
        let timeBasedScenario = detectTimeBasedScenario()
        let activityBasedScenario = detectActivityBasedScenario()
        let contextBasedScenario = detectContextBasedScenario()
        
        // 综合多个因素进行判断
        return combineDetectionResults(
            timeBasedScenario: timeBasedScenario,
            activityBasedScenario: activityBasedScenario,
            contextBasedScenario: contextBasedScenario
        )
    }
    
    /// 基于时间的场景检测
    private func detectTimeBasedScenario() -> WorkScenario {
        let now = Date()
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: now)
        let weekday = calendar.component(.weekday, from: now)
        
        // 周末判断
        let isWeekend = weekday == 1 || weekday == 7 // Sunday = 1, Saturday = 7
        
        if isWeekend {
            return workHours.contains(hour) ? .weekendWithPay : .resting
        }
        
        // 工作日判断
        if lunchHours.contains(hour) {
            return .lunchBreak
        } else if workHours.contains(hour) {
            return .normalWork
        } else if hour > workHours.upperBound && hour < 22 {
            return .nightOvertime
        } else {
            return .resting
        }
    }
    
    /// 基于活动的场景检测
    private func detectActivityBasedScenario() -> WorkScenario {
        guard enableActivityBasedDetection else { return .resting }
        
        let currentActivityLevel = getCurrentActivityLevel()
        
        // 基于活动水平判断
        if currentActivityLevel > 0.8 {
            return .deepWork // 高活跃度，可能在专注工作
        } else if currentActivityLevel > 0.5 {
            return .normalWork // 中等活跃度，正常工作
        } else if currentActivityLevel > 0.2 {
            return .meeting // 低活跃度但有活动，可能在开会
        } else {
            return .resting // 很低活跃度，可能在休息
        }
    }
    
    /// 基于上下文的场景检测
    private func detectContextBasedScenario() -> WorkScenario {
        // 这里可以集成日历、位置等信息
        // 目前返回基础检测结果
        return .normalWork
    }
    
    /// 综合检测结果
    private func combineDetectionResults(
        timeBasedScenario: WorkScenario,
        activityBasedScenario: WorkScenario,
        contextBasedScenario: WorkScenario
    ) -> WorkScenario {
        // 时间因素权重最高
        var candidateScenarios: [WorkScenario: Double] = [:]
        
        candidateScenarios[timeBasedScenario] = (candidateScenarios[timeBasedScenario] ?? 0) + 0.6
        candidateScenarios[activityBasedScenario] = (candidateScenarios[activityBasedScenario] ?? 0) + 0.3
        candidateScenarios[contextBasedScenario] = (candidateScenarios[contextBasedScenario] ?? 0) + 0.1
        
        // 选择得分最高的场景
        return candidateScenarios.max(by: { $0.value < $1.value })?.key ?? timeBasedScenario
    }
    
    /// 计算检测置信度
    private func calculateDetectionConfidence(for scenario: WorkScenario) -> Double {
        let timeMatch = detectTimeBasedScenario() == scenario ? 0.6 : 0.0
        let activityMatch = detectActivityBasedScenario() == scenario ? 0.3 : 0.0
        let contextMatch = detectContextBasedScenario() == scenario ? 0.1 : 0.0
        
        return min(timeMatch + activityMatch + contextMatch, 1.0)
    }
    
    // MARK: - Activity Detection
    /// 获取当前活动水平
    private func getCurrentActivityLevel() -> Double {
        // 这里应该集成运动传感器数据
        // 目前返回模拟数据
        return Double.random(in: 0.3...0.9)
    }
    
    /// 设置运动检测
    private func setupMotionDetection() {
        guard enableActivityBasedDetection else { return }
        
        motionManager = CMMotionManager()
        // 配置运动管理器...
    }
    
    /// 设置HealthKit
    private func setupHealthKit() {
        if HKHealthStore.isHealthDataAvailable() {
            healthStore = HKHealthStore()
            // 请求健康数据权限...
        }
    }
    
    // MARK: - Reminders and Notifications
    /// 检查提醒
    private func checkForReminders() {
        checkWorkDurationReminder()
        checkRestReminder()
        checkPostureReminder()
    }
    
    /// 检查工作时长提醒
    private func checkWorkDurationReminder() {
        if currentScenario.isWorkingState {
            consecutiveWorkMinutes += Int(detectionInterval / 60)
            
            if consecutiveWorkMinutes >= Int(maxWorkDuration / 60) {
                sendWorkDurationReminder()
                consecutiveWorkMinutes = 0
            }
        } else {
            consecutiveWorkMinutes = 0
        }
    }
    
    /// 检查休息提醒
    private func checkRestReminder() {
        let timeSinceLastRest = Date().timeIntervalSince(lastRestReminder)
        
        if currentScenario.isWorkingState && timeSinceLastRest >= restReminderInterval {
            sendRestReminder()
            lastRestReminder = Date()
        }
    }
    
    /// 检查姿势提醒
    private func checkPostureReminder() {
        // 基于运动数据检测是否需要姿势提醒
        if currentScenario == .deepWork && getCurrentActivityLevel() < 0.1 {
            sendPostureReminder()
        }
    }
    
    /// 发送工作时长提醒
    private func sendWorkDurationReminder() {
        NotificationCenter.default.post(
            name: .workDurationReminder,
            object: nil,
            userInfo: ["duration": consecutiveWorkMinutes]
        )
    }
    
    /// 发送休息提醒
    private func sendRestReminder() {
        NotificationCenter.default.post(
            name: .restReminder,
            object: nil,
            userInfo: ["scenario": currentScenario.rawValue]
        )
    }
    
    /// 发送姿势提醒
    private func sendPostureReminder() {
        NotificationCenter.default.post(
            name: .postureReminder,
            object: nil
        )
    }
    
    /// 通知场景变化
    private func notifyScenarioChange(from: WorkScenario, to: WorkScenario) {
        NotificationCenter.default.post(
            name: .scenarioChanged,
            object: nil,
            userInfo: [
                "from": from.rawValue,
                "to": to.rawValue,
                "confidence": detectionConfidence
            ]
        )
    }
    
    // MARK: - History Management
    /// 添加场景历史记录
    private func addScenarioHistoryEntry(from: WorkScenario, to: WorkScenario, confidence: Double) {
        let entry = ScenarioHistoryEntry(
            from: from,
            to: to,
            timestamp: Date(),
            confidence: confidence,
            duration: Date().timeIntervalSince(lastScenarioChange)
        )
        
        scenarioHistory.append(entry)
        
        // 保持历史记录数量在合理范围内
        if scenarioHistory.count > 100 {
            scenarioHistory.removeFirst(scenarioHistory.count - 100)
        }
    }
    
    // MARK: - Manual Override
    /// 手动设置场景
    func setScenario(_ scenario: WorkScenario, confidence: Double = 1.0) {
        let previousScenario = currentScenario
        currentScenario = scenario
        detectionConfidence = confidence
        lastScenarioChange = Date()
        
        addScenarioHistoryEntry(from: previousScenario, to: scenario, confidence: confidence)
        notifyScenarioChange(from: previousScenario, to: scenario)
    }
    
    /// 获取场景统计信息
    func getScenarioStatistics(for timeRange: TimeInterval = 24 * 60 * 60) -> [WorkScenario: TimeInterval] {
        let cutoffDate = Date().addingTimeInterval(-timeRange)
        let recentHistory = scenarioHistory.filter { $0.timestamp >= cutoffDate }
        
        var statistics: [WorkScenario: TimeInterval] = [:]
        
        for entry in recentHistory {
            statistics[entry.to] = (statistics[entry.to] ?? 0) + entry.duration
        }
        
        return statistics
    }
}

// MARK: - Supporting Models
/// 场景历史记录条目
struct ScenarioHistoryEntry {
    let from: WorkScenario
    let to: WorkScenario
    let timestamp: Date
    let confidence: Double
    let duration: TimeInterval
}

// MARK: - Notification Names
extension Notification.Name {
    static let scenarioChanged = Notification.Name("scenarioChanged")
    static let workDurationReminder = Notification.Name("workDurationReminder") 
    static let restReminder = Notification.Name("restReminder")
    static let postureReminder = Notification.Name("postureReminder")
}

// MARK: - 工作模式建议器
/// 智能工作模式建议器
class WorkModeAdvisor {
    
    /// 根据当前状态建议最佳工作模式
    static func suggestOptimalWorkMode(
        currentScenario: WorkScenario,
        workHistory: [ScenarioHistoryEntry],
        timeOfDay: Date
    ) -> WorkScenario {
        
        let hour = Calendar.current.component(.hour, from: timeOfDay)
        
        // 基于时间的建议
        switch hour {
        case 9...11:
            return .deepWork // 上午精力充沛，适合深度工作
        case 12...13:
            return .lunchBreak // 午餐时间
        case 14...16:
            return .normalWork // 下午正常工作
        case 17...18:
            return .meeting // 下午晚些时候适合开会
        default:
            return .resting
        }
    }
    
    /// 分析工作模式趋势
    static func analyzeWorkPatterns(history: [ScenarioHistoryEntry]) -> WorkPatternAnalysis {
        var totalWorkTime: TimeInterval = 0
        var deepWorkTime: TimeInterval = 0
        var overtimeCount = 0
        
        for entry in history {
            if entry.to.isWorkingState {
                totalWorkTime += entry.duration
                
                if entry.to == .deepWork {
                    deepWorkTime += entry.duration
                }
                
                if entry.to == .nightOvertime {
                    overtimeCount += 1
                }
            }
        }
        
        return WorkPatternAnalysis(
            totalWorkTime: totalWorkTime,
            deepWorkTime: deepWorkTime,
            deepWorkRatio: totalWorkTime > 0 ? deepWorkTime / totalWorkTime : 0,
            overtimeFrequency: overtimeCount,
            efficiency: calculateEfficiency(history: history)
        )
    }
    
    /// 计算工作效率
    private static func calculateEfficiency(history: [ScenarioHistoryEntry]) -> Double {
        // 基于深度工作时间占比、场景切换频率等计算效率
        let deepWorkRatio = analyzeWorkPatterns(history: history).deepWorkRatio
        let scenarioChanges = history.count
        
        // 深度工作占比高，场景切换少 = 效率高
        let stabilityScore = max(0, 1.0 - Double(scenarioChanges) / 50.0)
        return (deepWorkRatio * 0.7) + (stabilityScore * 0.3)
    }
}

/// 工作模式分析结果
struct WorkPatternAnalysis {
    let totalWorkTime: TimeInterval
    let deepWorkTime: TimeInterval  
    let deepWorkRatio: Double
    let overtimeFrequency: Int
    let efficiency: Double
}