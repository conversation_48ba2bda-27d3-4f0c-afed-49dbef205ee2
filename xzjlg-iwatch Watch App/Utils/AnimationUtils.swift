//
//  AnimationUtils.swift
//  xzjlg-iwatch Watch App
//
//  Created by AI Assistant on 2025/7/26.
//

import SwiftUI
import Foundation

/// 动画工具类 - 提供优化的动画效果和缓动函数
struct AnimationUtils {
    
    // MARK: - 预定义动画
    
    /// 弹性入场动画
    static func bounceIn(duration: TimeInterval = 0.8, delay: TimeInterval = 0) -> Animation {
        return .spring(response: duration, dampingFraction: 0.6, blendDuration: 0.1)
            .delay(delay)
    }
    
    /// 平滑淡入动画
    static func smoothFadeIn(duration: TimeInterval = 0.5, delay: TimeInterval = 0) -> Animation {
        return .easeOut(duration: duration).delay(delay)
    }
    
    /// 脉冲动画
    static func pulse(duration: TimeInterval = 1.0) -> Animation {
        return .easeInOut(duration: duration).repeatForever(autoreverses: true)
    }
    
    /// 旋转动画
    static func continuousRotation(duration: TimeInterval = 2.0) -> Animation {
        return .linear(duration: duration).repeatForever(autoreverses: false)
    }
    
    /// 浮动动画
    static func floating(duration: TimeInterval = 2.0, amplitude: Double = 0.1) -> Animation {
        return .easeInOut(duration: duration).repeatForever(autoreverses: true)
    }
    
    /// 闪烁动画
    static func sparkle(duration: TimeInterval = 1.5) -> Animation {
        return .easeInOut(duration: duration).repeatForever(autoreverses: true)
    }
    
    /// 弹跳动画
    static func bounce(duration: TimeInterval = 0.6) -> Animation {
        return .interpolatingSpring(stiffness: 300, damping: 10)
    }
    
    /// 摆动动画
    static func wiggle(duration: TimeInterval = 0.5) -> Animation {
        return .easeInOut(duration: duration).repeatCount(3, autoreverses: true)
    }
    
    // MARK: - 电量优化动画
    
    /// 根据电量级别获取优化的动画
    static func optimizedAnimation(
        base: Animation,
        batteryLevel: BatteryOptimizationLevel
    ) -> Animation {
        switch batteryLevel {
        case .normal:
            return base
        case .reduced:
            return base.speed(0.8)
        case .minimal:
            return base.speed(0.5)
        }
    }
    
    /// 获取电量优化的持续时间
    static func optimizedDuration(
        base: TimeInterval,
        batteryLevel: BatteryOptimizationLevel
    ) -> TimeInterval {
        switch batteryLevel {
        case .normal:
            return base
        case .reduced:
            return base * 1.2
        case .minimal:
            return base * 1.5
        }
    }
    
    // MARK: - 缓动函数
    
    /// 缓入缓出函数
    static func easeInOut(_ t: Double) -> Double {
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t
    }
    
    /// 弹性缓动函数
    static func elasticOut(_ t: Double) -> Double {
        let p: Double = 0.3
        return pow(2, -10 * t) * sin((t - p / 4) * (2 * .pi) / p) + 1
    }
    
    /// 反弹缓动函数
    static func bounceOut(_ t: Double) -> Double {
        if t < 1 / 2.75 {
            return 7.5625 * t * t
        } else if t < 2 / 2.75 {
            let t2 = t - 1.5 / 2.75
            return 7.5625 * t2 * t2 + 0.75
        } else if t < 2.5 / 2.75 {
            let t2 = t - 2.25 / 2.75
            return 7.5625 * t2 * t2 + 0.9375
        } else {
            let t2 = t - 2.625 / 2.75
            return 7.5625 * t2 * t2 + 0.984375
        }
    }
    
    /// 三次贝塞尔缓动
    static func cubicBezier(_ t: Double, _ p1: Double, _ p2: Double, _ p3: Double, _ p4: Double) -> Double {
        let u = 1 - t
        return u * u * u * p1 + 3 * u * u * t * p2 + 3 * u * t * t * p3 + t * t * t * p4
    }
}

// MARK: - 动画修饰符扩展

extension View {
    
    /// 应用弹性入场动画
    func bounceInAnimation(
        trigger: Bool,
        duration: TimeInterval = 0.8,
        delay: TimeInterval = 0
    ) -> some View {
        self
            .scaleEffect(trigger ? 1.0 : 0.1)
            .opacity(trigger ? 1.0 : 0.0)
            .animation(AnimationUtils.bounceIn(duration: duration, delay: delay), value: trigger)
    }
    
    /// 应用脉冲动画
    func pulseAnimation(
        isActive: Bool,
        scale: Double = 1.1,
        duration: TimeInterval = 1.0
    ) -> some View {
        self
            .scaleEffect(isActive ? scale : 1.0)
            .animation(
                isActive ? AnimationUtils.pulse(duration: duration) : .default,
                value: isActive
            )
    }
    
    /// 应用浮动动画
    func floatingAnimation(
        isActive: Bool,
        offset: Double = 3.0,
        duration: TimeInterval = 2.0
    ) -> some View {
        self
            .offset(y: isActive ? offset : 0)
            .animation(
                isActive ? AnimationUtils.floating(duration: duration) : .default,
                value: isActive
            )
    }
    
    /// 应用闪烁动画
    func sparkleAnimation(
        isActive: Bool,
        duration: TimeInterval = 1.5
    ) -> some View {
        self
            .opacity(isActive ? 0.3 : 1.0)
            .animation(
                isActive ? AnimationUtils.sparkle(duration: duration) : .default,
                value: isActive
            )
    }
    
    /// 应用摆动动画
    func wiggleAnimation(
        trigger: Bool,
        angle: Double = 5.0,
        duration: TimeInterval = 0.5
    ) -> some View {
        self
            .rotationEffect(.degrees(trigger ? angle : 0))
            .animation(
                trigger ? AnimationUtils.wiggle(duration: duration) : .default,
                value: trigger
            )
    }
    
    /// 应用电量优化的动画
    func batteryOptimizedAnimation<V: Equatable>(
        _ animation: Animation,
        value: V,
        batteryLevel: BatteryOptimizationLevel
    ) -> some View {
        self.animation(
            AnimationUtils.optimizedAnimation(base: animation, batteryLevel: batteryLevel),
            value: value
        )
    }
}

// MARK: - 粒子动画工具

struct ParticleAnimationUtils {
    
    /// 生成粒子初始位置（爆炸效果）
    static func explosionPosition(
        index: Int,
        totalCount: Int,
        radius: Double = 30.0,
        center: CGPoint = .zero
    ) -> CGPoint {
        let angle = Double(index) * (360.0 / Double(totalCount)) * .pi / 180
        let randomRadius = radius * Double.random(in: 0.5...1.0)
        return CGPoint(
            x: center.x + randomRadius * cos(angle),
            y: center.y + randomRadius * sin(angle)
        )
    }
    
    /// 生成粒子初始速度（爆炸效果）
    static func explosionVelocity(
        from position: CGPoint,
        center: CGPoint = .zero,
        speed: Double = 100.0
    ) -> CGVector {
        let dx = position.x - center.x
        let dy = position.y - center.y
        let distance = sqrt(dx * dx + dy * dy)
        
        if distance == 0 { return CGVector.zero }
        
        let normalizedDx = dx / distance
        let normalizedDy = dy / distance
        
        return CGVector(
            dx: normalizedDx * speed * Double.random(in: 0.8...1.2),
            dy: normalizedDy * speed * Double.random(in: 0.8...1.2)
        )
    }
    
    /// 生成螺旋运动路径
    static func spiralPosition(
        time: Double,
        center: CGPoint = .zero,
        radius: Double = 20.0,
        speed: Double = 1.0
    ) -> CGPoint {
        let angle = time * speed
        let currentRadius = radius * (1.0 - time * 0.1) // 逐渐缩小半径
        return CGPoint(
            x: center.x + currentRadius * cos(angle),
            y: center.y + currentRadius * sin(angle)
        )
    }
    
    /// 生成波浪运动路径
    static func wavePosition(
        time: Double,
        basePosition: CGPoint,
        amplitude: Double = 10.0,
        frequency: Double = 2.0
    ) -> CGPoint {
        return CGPoint(
            x: basePosition.x,
            y: basePosition.y + amplitude * sin(time * frequency)
        )
    }
}

// MARK: - 性能优化工具

struct AnimationPerformanceUtils {
    
    /// 根据设备性能调整动画参数
    static func adjustForDevicePerformance(
        value: Double,
        performanceLevel: AnimationQuality
    ) -> Double {
        switch performanceLevel {
        case .high:
            return value
        case .medium:
            return value * 0.8
        case .low:
            return value * 0.6
        case .minimal:
            return value * 0.4
        }
    }
    
    /// 计算最优更新间隔
    static func optimalUpdateInterval(
        targetFPS: Double,
        performanceLevel: AnimationQuality
    ) -> TimeInterval {
        let adjustedFPS = adjustForDevicePerformance(
            value: targetFPS,
            performanceLevel: performanceLevel
        )
        return 1.0 / max(adjustedFPS, 10.0) // 最低10fps
    }
    
    /// 检查是否应该跳过动画帧
    static func shouldSkipFrame(
        frameIndex: Int,
        performanceLevel: AnimationQuality
    ) -> Bool {
        switch performanceLevel {
        case .high:
            return false
        case .medium:
            return frameIndex % 2 == 0 // 跳过一半帧
        case .low:
            return frameIndex % 3 != 0 // 只保留1/3帧
        case .minimal:
            return frameIndex % 4 != 0 // 只保留1/4帧
        }
    }
}

// MARK: - 动画状态管理

class AnimationStateManager: ObservableObject {
    @Published var isAnimating: Bool = false
    @Published var animationProgress: Double = 0.0
    @Published var currentPhase: AnimationPhase = .idle
    
    private var animationTimer: Timer?
    private var startTime: Date?
    
    enum AnimationPhase {
        case idle
        case starting
        case running
        case ending
        case completed
    }
    
    func startAnimation(duration: TimeInterval) {
        guard !isAnimating else { return }
        
        isAnimating = true
        currentPhase = .starting
        startTime = Date()
        animationProgress = 0.0
        
        animationTimer = Timer.scheduledTimer(withTimeInterval: 1.0/60.0, repeats: true) { [weak self] _ in
            self?.updateAnimationProgress(duration: duration)
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.currentPhase = .running
        }
    }
    
    func stopAnimation() {
        animationTimer?.invalidate()
        animationTimer = nil
        
        currentPhase = .ending
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.isAnimating = false
            self.currentPhase = .completed
            self.animationProgress = 1.0
        }
    }
    
    private func updateAnimationProgress(duration: TimeInterval) {
        guard let startTime = startTime else { return }
        
        let elapsed = Date().timeIntervalSince(startTime)
        animationProgress = min(elapsed / duration, 1.0)
        
        if animationProgress >= 1.0 {
            stopAnimation()
        }
    }
}