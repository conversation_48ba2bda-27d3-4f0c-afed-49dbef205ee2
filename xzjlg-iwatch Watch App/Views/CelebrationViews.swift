//
//  CelebrationViews.swift
//  xzjlg-iwatch Watch App
//
//  Created by AI Assistant on 2025/1/25.
//

import SwiftUI

/// 目标完成庆祝视图
struct GoalCompletionCelebrationView: View {
    let completedGoal: Goal
    @Environment(\.dismiss) private var dismiss
    @State private var showParticles = false
    @State private var showContent = false
    @State private var confettiOffset: CGFloat = -200
    @State private var celebrationScale: Double = 0.1
    @State private var celebrationOpacity: Double = 0.0
    
    var body: some View {
        ZStack {
            // 背景渐变
            RadialGradient(
                colors: [
                    Color.yellow.opacity(0.3),
                    Color.orange.opacity(0.2),
                    Color.clear
                ],
                center: .center,
                startRadius: 50,
                endRadius: 200
            )
            .ignoresSafeArea()
            
            // 彩色粒子背景
            if showParticles {
                ParticleEffectView()
                    .ignoresSafeArea()
            }
            
            VStack(spacing: 24) {
                // 庆祝图标
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [Color.yellow, Color.orange],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)
                        .shadow(color: .orange.opacity(0.5), radius: 8, x: 0, y: 4)
                    
                    Image(systemName: "checkmark")
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(.white)
                }
                .scaleEffect(celebrationScale)
                .opacity(celebrationOpacity)
                
                // 庆祝文字
                VStack(spacing: 8) {
                    Text("🎉 目标达成！")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    // 简洁的达成内容
                    VStack(spacing: 4) {
                        Text(completedGoal.name)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.center)
                            .lineLimit(1)
                        
                        Text(completedGoal.formattedTargetAmount)
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.orange)
                    }
                    .padding(.vertical, 4)
                    .padding(.horizontal, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(.background.opacity(0.8))
                    )
                    
                    // 简洁的完成提示
                    Text(getCompletionMessage())
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .opacity(showContent ? 1.0 : 0.0)
                .offset(y: showContent ? 0 : 20)
                
                // 庆祝小福狸
                CelebrationFoxView()
                    .frame(height: 60)
                    .opacity(showContent ? 1.0 : 0.0)
                    .scaleEffect(showContent ? 1.0 : 0.8)
                
                // 确认按钮
                Button {
                    dismiss()
                } label: {
                    HStack(spacing: 8) {
                        Image(systemName: "hand.thumbsup.fill")
                        Text("太棒了！")
                    }
                    .font(.body)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        LinearGradient(
                            colors: [Color.orange, Color.red],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(20)
                }
                .opacity(showContent ? 1.0 : 0.0)
                .scaleEffect(showContent ? 1.0 : 0.9)
            }
            .padding()
        }
        .onAppear {
            startCelebrationAnimation()
        }
    }
    
    private func startCelebrationAnimation() {
        // 第一阶段：粒子效果
        withAnimation(.easeOut(duration: 0.3)) {
            showParticles = true
        }
        
        // 第二阶段：庆祝图标
        withAnimation(.spring(response: 0.6, dampingFraction: 0.6).delay(0.2)) {
            celebrationScale = 1.2
            celebrationOpacity = 1.0
        }
        
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8).delay(0.5)) {
            celebrationScale = 1.0
        }
        
        // 第三阶段：内容显示
        withAnimation(.easeOut(duration: 0.5).delay(0.8)) {
            showContent = true
        }
    }
    
    private func getCompletionMessage() -> String {
        let timeTaken = completedGoal.lastUpdated.timeIntervalSince(completedGoal.createdAt)
        let daysTaken = Int(timeTaken / (24 * 3600))
        
        // 根据目标类型和完成时间给出不同的简洁消息
        if daysTaken <= 7 {
            return "快速达成！仅用\(daysTaken)天"
        } else if daysTaken <= 30 {
            return "坚持\(daysTaken)天，成功达成"
        } else if daysTaken <= 90 {
            return "历时\(daysTaken)天，终于完成"
        } else {
            return "长期坚持，值得庆祝"
        }
    }
}

/// 庆祝状态的小福狸
struct CelebrationFoxView: View {
    @State private var bounce: Double = 0
    @State private var sparkle: Double = 0
    
    var body: some View {
        ZStack {
            // 小福狸头部
            Ellipse()
                .fill(
                    RadialGradient(
                        colors: [
                            Color(red: 1.0, green: 0.8, blue: 0.6),
                            Color(red: 1.0, green: 0.7, blue: 0.4)
                        ],
                        center: .topLeading,
                        startRadius: 5,
                        endRadius: 25
                    )
                )
                .frame(width: 40, height: 35)
                .overlay(
                    // 高光
                    Ellipse()
                        .fill(Color.white.opacity(0.3))
                        .frame(width: 15, height: 12)
                        .offset(x: -8, y: -8)
                )
            
            // 庆祝的眼睛（星星眼）
            HStack(spacing: 8) {
                StarEye()
                StarEye()
            }
            .offset(y: -3)
            
            // 大大的笑脸
            Path { path in
                path.addArc(
                    center: CGPoint(x: 0, y: 8),
                    radius: 8,
                    startAngle: .degrees(20),
                    endAngle: .degrees(160),
                    clockwise: false
                )
            }
            .stroke(Color.black, lineWidth: 2)
            .offset(y: 2)
            
            // 耳朵
            HStack(spacing: 30) {
                CelebrationEar(isLeft: true, sparkle: sparkle)
                CelebrationEar(isLeft: false, sparkle: sparkle)
            }
            .offset(y: -15)
            
            // 闪闪发光效果
            ForEach(0..<6, id: \.self) { index in
                Image(systemName: "sparkle")
                    .font(.system(size: 8))
                    .foregroundColor(.yellow)
                    .offset(
                        x: cos(Double(index) * .pi / 3 + sparkle) * 30,
                        y: sin(Double(index) * .pi / 3 + sparkle) * 30
                    )
                    .opacity(0.8)
            }
        }
        .offset(y: bounce)
        .onAppear {
            // 欢快的弹跳动画
            withAnimation(.easeInOut(duration: 0.5).repeatForever(autoreverses: true)) {
                bounce = -3
            }
            
            // 闪闪发光动画
            withAnimation(.linear(duration: 2.0).repeatForever(autoreverses: false)) {
                sparkle = .pi * 2
            }
        }
    }
}

/// 星星眼睛
struct StarEye: View {
    @State private var twinkle: Double = 1.0
    
    var body: some View {
        Image(systemName: "star.fill")
            .font(.system(size: 8))
            .foregroundColor(.yellow)
            .scaleEffect(twinkle)
            .onAppear {
                withAnimation(.easeInOut(duration: 0.8).repeatForever(autoreverses: true)) {
                    twinkle = 1.3
                }
            }
    }
}

/// 庆祝状态的耳朵
struct CelebrationEar: View {
    let isLeft: Bool
    let sparkle: Double
    
    var body: some View {
        ZStack {
            // 外耳
            Ellipse()
                .fill(
                    LinearGradient(
                        colors: [
                            Color(red: 1.0, green: 0.7, blue: 0.4),
                            Color(red: 0.9, green: 0.5, blue: 0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 12, height: 16)
            
            // 内耳
            Ellipse()
                .fill(Color.pink.opacity(0.8))
                .frame(width: 5, height: 7)
                .offset(y: 1)
            
            // 小星星装饰
            Image(systemName: "star.fill")
                .font(.system(size: 4))
                .foregroundColor(.yellow)
                .offset(x: isLeft ? -8 : 8, y: -8)
                .opacity(sin(sparkle) * 0.5 + 0.5)
        }
        .rotationEffect(.degrees(
            isLeft ? (-15 + sin(sparkle) * 10) : (15 + sin(sparkle) * -10)
        ))
        .shadow(color: .black.opacity(0.1), radius: 1, x: 0.5, y: 0.5)
    }
}

/// 优化版粒子特效视图 - 支持电量优化和性能调节
struct ParticleEffectView: View {
    @EnvironmentObject var appState: AppState
    @State private var particles: [ParticleData] = []
    @State private var animationTimer: Timer? = nil
    @State private var effectIntensity: EffectIntensity = .high
    
    var body: some View {
        ZStack {
            ForEach(particles.indices, id: \.self) { index in
                if index < particles.count {
                    ParticleView(particle: particles[index])
                        .opacity(getParticleOpacity(for: particles[index]))
                }
            }
        }
        .onAppear {
            updateEffectIntensity()
            if appState.shouldShowAnimation() {
                generateParticles()
                startParticleAnimation()
            }
        }
        .onDisappear {
            stopParticleAnimation()
        }
        .onChange(of: appState.showCharacterAnimation) { oldValue, newValue in
            if newValue && appState.showCharacterAnimation {
                if animationTimer == nil {
                    generateParticles()
                    startParticleAnimation()
                }
            } else {
                stopParticleAnimation()
            }
        }
        .onChange(of: appState.batteryOptimizationLevel) { oldValue, newValue in
            updateEffectIntensity()
            adjustParticleComplexity()
        }
    }
    
    private func generateParticles() {
        let particleCount = getOptimalParticleCount()
        particles = (0..<particleCount).map { _ in
            ParticleData(
                id: UUID(),
                startPosition: CGPoint(
                    x: Double.random(in: -50...250),
                    y: Double.random(in: -50...50)
                ),
                color: getOptimizedColor(),
                size: getOptimizedSize(),
                speed: getOptimizedSpeed(),
                direction: Double.random(in: 0...(2 * .pi))
            )
        }
    }
    
    private func getOptimalParticleCount() -> Int {
        switch effectIntensity {
        case .high:
            return 30
        case .medium:
            return 20
        case .minimal:
            return 10
        }
    }
    
    private func getOptimizedColor() -> Color {
        let colors: [Color]
        switch effectIntensity {
        case .high:
            colors = [.red, .orange, .yellow, .green, .blue, .purple, .pink, .cyan, .mint]
        case .medium:
            colors = [.red, .orange, .yellow, .blue, .purple, .pink]
        case .minimal:
            colors = [.yellow, .orange]
        }
        return colors.randomElement() ?? .yellow
    }
    
    private func getOptimizedSize() -> Double {
        switch effectIntensity {
        case .high:
            return Double.random(in: 4...12)
        case .medium:
            return Double.random(in: 5...10)
        case .minimal:
            return Double.random(in: 6...8)
        }
    }
    
    private func getOptimizedSpeed() -> Double {
        switch effectIntensity {
        case .high:
            return Double.random(in: 50...150)
        case .medium:
            return Double.random(in: 60...120)
        case .minimal:
            return Double.random(in: 80...100)
        }
    }
    
    private func startParticleAnimation() {
        let updateInterval = getOptimalUpdateInterval()
        animationTimer = Timer.scheduledTimer(withTimeInterval: updateInterval, repeats: true) { _ in
            updateParticles()
        }
        
        // 根据电量优化调整持续时间
        let duration = getOptimalDuration()
        DispatchQueue.main.asyncAfter(deadline: .now() + duration) {
            stopParticleAnimation()
        }
    }
    
    private func stopParticleAnimation() {
        animationTimer?.invalidate()
        animationTimer = nil
        particles.removeAll()
    }
    
    private func getOptimalUpdateInterval() -> TimeInterval {
        switch effectIntensity {
        case .high:
            return 1.0 / 30.0  // 30 FPS
        case .medium:
            return 1.0 / 24.0  // 24 FPS
        case .minimal:
            return 1.0 / 15.0  // 15 FPS
        }
    }
    
    private func getOptimalDuration() -> TimeInterval {
        switch effectIntensity {
        case .high:
            return 3.0
        case .medium:
            return 2.5
        case .minimal:
            return 1.5
        }
    }
    
    private func updateParticles() {
        for index in particles.indices {
            particles[index].update()
        }
    }
    
    private func updateEffectIntensity() {
        switch appState.batteryOptimizationLevel {
        case .normal:
            effectIntensity = .high
        case .reduced:
            effectIntensity = .medium
        case .minimal:
            effectIntensity = .minimal
        }
    }
    
    private func adjustParticleComplexity() {
        // 重新生成适应当前电量级别的粒子
        stopParticleAnimation()
        if appState.shouldShowAnimation() {
            generateParticles()
            startParticleAnimation()
        }
    }
    
    private func getParticleOpacity(for particle: ParticleData) -> Double {
        switch effectIntensity {
        case .high:
            return particle.opacity
        case .medium:
            return particle.opacity * 0.9
        case .minimal:
            return particle.opacity * 0.7
        }
    }
}

/// 特效强度级别
enum EffectIntensity {
    case high      // 高强度：全特效
    case medium    // 中强度：减少粒子数量和颜色
    case minimal   // 最小强度：最基础的特效
}

/// 单个粒子视图
struct ParticleView: View {
    let particle: ParticleData
    
    var body: some View {
        Circle()
            .fill(particle.color)
            .frame(width: particle.size, height: particle.size)
            .position(particle.currentPosition)
            .opacity(particle.opacity)
            .scaleEffect(particle.scale)
    }
}

/// 粒子数据结构
class ParticleData: ObservableObject {
    let id: UUID
    let startPosition: CGPoint
    let color: Color
    let size: Double
    let speed: Double
    let direction: Double
    
    @Published var currentPosition: CGPoint
    @Published var opacity: Double = 1.0
    @Published var scale: Double = 1.0
    
    private var time: Double = 0.0
    private let gravity: Double = 30.0
    private let lifespan: Double = 3.0
    
    init(id: UUID, startPosition: CGPoint, color: Color, size: Double, speed: Double, direction: Double) {
        self.id = id
        self.startPosition = startPosition
        self.color = color
        self.size = size
        self.speed = speed
        self.direction = direction
        self.currentPosition = startPosition
    }
    
    func update() {
        time += 0.05
        
        // 物理运动计算
        let velocityX = cos(direction) * speed
        let velocityY = sin(direction) * speed - gravity * time
        
        currentPosition = CGPoint(
            x: startPosition.x + velocityX * time,
            y: startPosition.y + velocityY * time
        )
        
        // 生命周期效果
        let lifeRatio = time / lifespan
        opacity = max(0, 1.0 - lifeRatio)
        scale = max(0.1, 1.0 - lifeRatio * 0.5)
    }
}

// ProgressMilestoneDetector and ProgressMilestone are now defined in separate files

/// 里程碑通知视图
struct MilestoneNotificationView: View {
    let milestone: ProgressMilestone
    @Environment(\.dismiss) private var dismiss
    @State private var scale: Double = 0.1
    @State private var opacity: Double = 0.0
    @State private var showParticles: Bool = false
    
    var body: some View {
        ZStack {
            // 背景粒子效果
            if showParticles {
                MilestoneParticleEffectView(percentage: milestone.percentage)
                    .ignoresSafeArea()
            }
            
            VStack(spacing: 16) {
            // 进度图标
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: milestoneColors,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 60, height: 60)
                
                Text("\(milestone.percentage)%")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            .scaleEffect(scale)
            
            VStack(spacing: 6) {
                // 简洁的里程碑标题
                Text(getMilestoneTitle(milestone.percentage))
                    .font(.title3)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                
                // 目标名称（简短显示）
                Text(milestone.goalName)
                    .font(.body)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .lineLimit(1)
                
                // 关键信息卡片
                VStack(spacing: 3) {
                    HStack {
                        Text("已攒")
                        Spacer()
                        Text(milestone.formattedCurrentAmount)
                            .fontWeight(.bold)
                            .foregroundColor(.orange)
                    }
                    .font(.caption)
                    
                    HStack {
                        Text("还需")
                        Spacer()
                        Text(milestone.formattedRemainingAmount)
                            .fontWeight(.medium)
                    }
                    .font(.caption2)
                    .foregroundColor(.secondary)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(.background.opacity(0.7))
                )
            }
            .opacity(opacity)
            
            Button("继续加油！") {
                dismiss()
            }
            .buttonStyle(.borderedProminent)
            .opacity(opacity)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.regularMaterial)
                .shadow(radius: 8)
        )
        }
        .onAppear {
            // 启动粒子效果
            withAnimation(.easeOut(duration: 0.3)) {
                showParticles = true
            }
            
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                scale = 1.0
            }
            
            withAnimation(.easeOut(duration: 0.5).delay(0.2)) {
                opacity = 1.0
            }
            
            // 3秒后自动消失
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                dismiss()
            }
        }
    }
    
    private var milestoneColors: [Color] {
        switch milestone.percentage {
        case 25:
            return [.green, .mint]
        case 50:
            return [.blue, .cyan]
        case 75:
            return [.orange, .yellow]
        default:
            return [.purple, .pink]
        }
    }
    
    private func getMilestoneTitle(_ percentage: Int) -> String {
        switch percentage {
        case 25:
            return "🌱 起步不错！"
        case 50:
            return "🎯 过半啦！"
        case 75:
            return "🚀 快到了！"
        case 90:
            return "⭐️ 即将达成！"
        default:
            return "💪 继续努力！"
        }
    }
}

/// 里程碑专用粒子特效 - 优化版
struct MilestoneParticleEffectView: View {
    let percentage: Int
    @EnvironmentObject var appState: AppState
    @State private var particles: [MilestoneParticleData] = []
    @State private var animationTimer: Timer? = nil
    @State private var effectIntensity: EffectIntensity = .high
    
    var body: some View {
        ZStack {
            ForEach(particles.indices, id: \.self) { index in
                if index < particles.count {
                    MilestoneParticleView(particle: particles[index])
                        .opacity(getMilestoneParticleOpacity(for: particles[index]))
                }
            }
        }
        .onAppear {
            updateMilestoneEffectIntensity()
            if appState.shouldShowAnimation() {
                generateParticles()
                startParticleAnimation()
            }
        }
        .onDisappear {
            stopMilestoneParticleAnimation()
        }
    }
    
    private func generateParticles() {
        let baseCount = percentage >= 75 ? 25 : (percentage >= 50 ? 20 : 15)
        let particleCount = getOptimizedMilestoneParticleCount(baseCount: baseCount)
        
        particles = (0..<particleCount).map { _ in
            MilestoneParticleData(
                id: UUID(),
                startPosition: CGPoint(
                    x: Double.random(in: -50...250),
                    y: Double.random(in: -50...50)
                ),
                color: getParticleColor(),
                size: getOptimizedMilestoneSize(),
                speed: getOptimizedMilestoneSpeed(),
                direction: Double.random(in: 0...(2 * .pi)),
                symbol: getParticleSymbol()
            )
        }
    }
    
    private func getOptimizedMilestoneParticleCount(baseCount: Int) -> Int {
        switch effectIntensity {
        case .high:
            return baseCount
        case .medium:
            return Int(Double(baseCount) * 0.7)
        case .minimal:
            return Int(Double(baseCount) * 0.4)
        }
    }
    
    private func getOptimizedMilestoneSize() -> Double {
        switch effectIntensity {
        case .high:
            return Double.random(in: 3...8)
        case .medium:
            return Double.random(in: 4...7)
        case .minimal:
            return Double.random(in: 4...5)
        }
    }
    
    private func getOptimizedMilestoneSpeed() -> Double {
        switch effectIntensity {
        case .high:
            return Double.random(in: 30...80)
        case .medium:
            return Double.random(in: 35...70)
        case .minimal:
            return Double.random(in: 45...55)
        }
    }
    
    private func getParticleColor() -> Color {
        switch percentage {
        case 25:
            return [.green, .mint, .yellow].randomElement() ?? .green
        case 50:
            return [.blue, .cyan, .purple].randomElement() ?? .blue
        case 75:
            return [.orange, .yellow, .red].randomElement() ?? .orange
        default:
            return [.purple, .pink, .blue].randomElement() ?? .purple
        }
    }
    
    private func getParticleSymbol() -> String {
        switch percentage {
        case 25:
            return ["star.fill", "circle.fill"].randomElement() ?? "star.fill"
        case 50:
            return ["star.fill", "diamond.fill", "heart.fill"].randomElement() ?? "star.fill"
        case 75:
            return ["star.fill", "sparkle", "flame.fill"].randomElement() ?? "star.fill"
        default:
            return "star.fill"
        }
    }
    
    private func startParticleAnimation() {
        let updateInterval = getMilestoneOptimalUpdateInterval()
        animationTimer = Timer.scheduledTimer(withTimeInterval: updateInterval, repeats: true) { _ in
            updateParticles()
        }
        
        // 根据电量优化调整持续时间
        let duration = getMilestoneOptimalDuration()
        DispatchQueue.main.asyncAfter(deadline: .now() + duration) {
            stopMilestoneParticleAnimation()
        }
    }
    
    private func stopMilestoneParticleAnimation() {
        animationTimer?.invalidate()
        animationTimer = nil
        particles.removeAll()
    }
    
    private func updateParticles() {
        for index in particles.indices {
            particles[index].update()
        }
    }
    
    private func getMilestoneOptimalUpdateInterval() -> TimeInterval {
        switch effectIntensity {
        case .high:
            return 1.0 / 30.0  // 30 FPS
        case .medium:
            return 1.0 / 24.0  // 24 FPS
        case .minimal:
            return 1.0 / 15.0  // 15 FPS
        }
    }
    
    private func getMilestoneOptimalDuration() -> TimeInterval {
        switch effectIntensity {
        case .high:
            return 2.5
        case .medium:
            return 2.0
        case .minimal:
            return 1.0
        }
    }
    
    private func updateMilestoneEffectIntensity() {
        switch appState.batteryOptimizationLevel {
        case .normal:
            effectIntensity = .high
        case .reduced:
            effectIntensity = .medium
        case .minimal:
            effectIntensity = .minimal
        }
    }
    
    private func getMilestoneParticleOpacity(for particle: MilestoneParticleData) -> Double {
        switch effectIntensity {
        case .high:
            return particle.opacity
        case .medium:
            return particle.opacity * 0.9
        case .minimal:
            return particle.opacity * 0.7
        }
    }
}

/// 里程碑粒子视图
struct MilestoneParticleView: View {
    let particle: MilestoneParticleData
    
    var body: some View {
        Image(systemName: particle.symbol)
            .font(.system(size: particle.size))
            .foregroundColor(particle.color)
            .position(particle.currentPosition)
            .opacity(particle.opacity)
            .scaleEffect(particle.scale)
            .rotationEffect(.degrees(particle.rotation))
    }
}

/// 里程碑粒子数据结构
class MilestoneParticleData: ObservableObject {
    let id: UUID
    let startPosition: CGPoint
    let color: Color
    let size: Double
    let speed: Double
    let direction: Double
    let symbol: String
    
    @Published var currentPosition: CGPoint
    @Published var opacity: Double = 1.0
    @Published var scale: Double = 1.0
    @Published var rotation: Double = 0.0
    
    private var time: Double = 0.0
    private let gravity: Double = 20.0
    private let lifespan: Double = 2.5
    
    init(id: UUID, startPosition: CGPoint, color: Color, size: Double, speed: Double, direction: Double, symbol: String) {
        self.id = id
        self.startPosition = startPosition
        self.color = color
        self.size = size
        self.speed = speed
        self.direction = direction
        self.symbol = symbol
        self.currentPosition = startPosition
    }
    
    func update() {
        time += 0.05
        
        // 物理运动计算
        let velocityX = cos(direction) * speed
        let velocityY = sin(direction) * speed - gravity * time
        
        currentPosition = CGPoint(
            x: startPosition.x + velocityX * time,
            y: startPosition.y + velocityY * time
        )
        
        // 旋转效果
        rotation += 5.0
        
        // 生命周期效果
        let lifeRatio = time / lifespan
        opacity = max(0, 1.0 - lifeRatio)
        scale = max(0.1, 1.0 - lifeRatio * 0.3)
    }
} 