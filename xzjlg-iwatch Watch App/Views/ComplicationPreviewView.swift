//
//  ComplicationPreviewView.swift
//  xzjlg-iwatch Watch App
//
//  Created by Assistant on 2024-12-28.
//

import SwiftUI

/// 表盘复杂功能预览视图
/// 展示不同复杂功能样式的预览效果
struct ComplicationPreviewView: View {
    @StateObject private var complicationProvider = WatchComplicationProvider.shared
    
    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                // 标题
                Text("表盘复杂功能预览")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .padding(.top)
                
                // 小型文本复杂功能
                VStack(spacing: 8) {
                    Text("小型文本")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    SmallTextComplicationView()
                        .frame(width: 60, height: 20)
                        .background(Color.black)
                        .cornerRadius(4)
                }
                
                // 环形进度复杂功能
                VStack(spacing: 8) {
                    Text("环形进度")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    CircularProgressComplicationView()
                        .frame(width: 40, height: 40)
                        .background(Color.black)
                        .cornerRadius(20)
                }
                
                // 矩形进度复杂功能
                VStack(spacing: 8) {
                    Text("矩形进度")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    RectangularComplicationView()
                        .frame(width: 120, height: 32)
                        .background(Color.black)
                        .cornerRadius(8)
                }
                
                // 更新信息
                VStack(spacing: 4) {
                    Text("最后更新")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Text(DateFormatter.timeFormatter.string(from: complicationProvider.lastUpdateTime))
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                // 手动更新按钮
                Button("手动更新") {
                    complicationProvider.updateComplicationData()
                }
                .buttonStyle(.bordered)
                .padding(.bottom)
            }
            .padding(.horizontal)
        }
        .navigationTitle("复杂功能")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            complicationProvider.updateComplicationData()
        }
    }
}

// MARK: - Small Text Complication

struct SmallTextComplicationView: View {
    @StateObject private var provider = WatchComplicationProvider.shared
    
    var body: some View {
        Text(provider.getFormattedEarnings())
            .font(.caption2)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .lineLimit(1)
            .minimumScaleFactor(0.6)
    }
}

// MARK: - Circular Progress Complication

struct CircularProgressComplicationView: View {
    @StateObject private var provider = WatchComplicationProvider.shared
    
    var body: some View {
        ZStack {
            // 背景圆环
            Circle()
                .stroke(Color.gray.opacity(0.3), lineWidth: 3)
            
            // 进度圆环
            Circle()
                .trim(from: 0, to: provider.dailyProgress)
                .stroke(
                    LinearGradient(
                        colors: [.blue, .green],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    style: StrokeStyle(lineWidth: 3, lineCap: .round)
                )
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 0.5), value: provider.dailyProgress)
            
            // 中心文本
            Text(provider.getProgressText())
                .font(.caption2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .lineLimit(1)
                .minimumScaleFactor(0.5)
        }
    }
}

// MARK: - Rectangular Complication

struct RectangularComplicationView: View {
    @StateObject private var provider = WatchComplicationProvider.shared
    
    var body: some View {
        HStack(spacing: 8) {
            // 收入显示
            VStack(alignment: .leading, spacing: 1) {
                Text(provider.getFormattedEarnings())
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Text(provider.getShortStatusText())
                    .font(.caption2)
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            // 进度条
            VStack(alignment: .trailing, spacing: 2) {
                // 每日进度
                HStack(spacing: 4) {
                    Text("日")
                        .font(.caption2)
                        .foregroundColor(.gray)
                    
                    ProgressView(value: provider.dailyProgress)
                        .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                        .frame(width: 20, height: 2)
                }
                
                // 目标进度
                HStack(spacing: 4) {
                    Text("目标")
                        .font(.caption2)
                        .foregroundColor(.gray)
                    
                    ProgressView(value: provider.goalProgress)
                        .progressViewStyle(LinearProgressViewStyle(tint: .green))
                        .frame(width: 20, height: 2)
                }
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
    }
}

// MARK: - DateFormatter Extension

extension DateFormatter {
    static let timeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()
}

// MARK: - Preview

struct ComplicationPreviewView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ComplicationPreviewView()
        }
    }
} 