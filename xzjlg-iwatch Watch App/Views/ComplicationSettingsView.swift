//
//  ComplicationSettingsView.swift
//  xzjlg-iwatch Watch App
//
//  Created by Assistant on 2025/7/26.
//

import SwiftUI
import ClockKit

/// 复杂功能设置界面
/// 允许用户配置复杂功能的样式、主题和更新策略
struct ComplicationSettingsView: View {
    
    // MARK: - Properties
    
    @StateObject private var updateManager = ComplicationUpdateManager.shared
    @StateObject private var styleManager = ComplicationStyleManager.shared
    @StateObject private var complicationProvider = WatchComplicationProvider.shared
    
    @State private var selectedTheme: ComplicationTheme = .default
    @State private var selectedStyle: ComplicationStyle = .combined
    @State private var showingPreview = false
    
    // MARK: - Body
    
    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                // 标题
                headerSection
                
                // 启用开关
                enableSection
                
                // 样式选择
                if updateManager.isComplicationEnabled {
                    styleSection
                    
                    // 主题选择
                    themeSection
                    
                    // 更新策略
                    updateStrategySection
                    
                    // 预览按钮
                    previewSection
                    
                    // 状态信息
                    statusSection
                }
            }
            .padding(.horizontal)
        }
        .navigationTitle("复杂功能设置")
        .navigationBarTitleDisplayMode(.inline)
        .sheet(isPresented: $showingPreview) {
            ComplicationPreviewView()
        }
        .onAppear {
            loadCurrentSettings()
        }
    }
}

// MARK: - View Components
private extension ComplicationSettingsView {
    
    /// 标题部分
    var headerSection: some View {
        VStack(spacing: 8) {
            Image(systemName: "applewatch")
                .font(.title2)
                .foregroundColor(.blue)
            
            Text("表盘复杂功能")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text("在表盘上显示工资和进度信息")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.top)
    }
    
    /// 启用开关部分
    var enableSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "power")
                    .foregroundColor(.green)
                Text("启用复杂功能")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
                
                Toggle("", isOn: $updateManager.isComplicationEnabled)
                    .labelsHidden()
            }
            
            if !updateManager.isComplicationEnabled {
                Text("启用后可在表盘上显示实时工资信息")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(red: 0.95, green: 0.95, blue: 0.97))
        .cornerRadius(12)
    }
    
    /// 样式选择部分
    var styleSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "paintbrush")
                    .foregroundColor(.orange)
                Text("显示样式")
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                ForEach(ComplicationStyle.allCases, id: \.self) { style in
                    styleCard(for: style)
                }
            }
        }
        .padding()
        .background(Color(red: 0.95, green: 0.95, blue: 0.97))
        .cornerRadius(12)
    }
    
    /// 样式卡片
    func styleCard(for style: ComplicationStyle) -> some View {
        VStack(spacing: 4) {
            Text(style.displayName)
                .font(.caption)
                .fontWeight(.medium)
            
            Text(style.description)
                .font(.caption2)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .padding(8)
        .background(selectedStyle == style ? Color.blue.opacity(0.2) : Color(red: 0.9, green: 0.9, blue: 0.92))
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(selectedStyle == style ? Color.blue : Color.clear, lineWidth: 1)
        )
        .onTapGesture {
            selectedStyle = style
            applyStyleChange()
        }
    }
    
    /// 主题选择部分
    var themeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "palette")
                    .foregroundColor(.purple)
                Text("颜色主题")
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            
            HStack(spacing: 12) {
                themeButton(theme: .default, color: .blue, name: "蓝色")
                themeButton(theme: .green, color: .green, name: "绿色")
                themeButton(theme: .orange, color: .orange, name: "橙色")
                themeButton(theme: .purple, color: .purple, name: "紫色")
            }
        }
        .padding()
        .background(Color(red: 0.95, green: 0.95, blue: 0.97))
        .cornerRadius(12)
    }
    
    /// 主题按钮
    func themeButton(theme: ComplicationTheme, color: Color, name: String) -> some View {
        VStack(spacing: 4) {
            Circle()
                .fill(color)
                .frame(width: 20, height: 20)
                .overlay(
                    Circle()
                        .stroke(Color.white, lineWidth: selectedTheme.primaryColor == theme.primaryColor ? 2 : 0)
                )
            
            Text(name)
                .font(.caption2)
                .foregroundColor(.primary)
        }
        .onTapGesture {
            selectedTheme = theme
            applyThemeChange()
        }
    }
    
    /// 更新策略部分
    var updateStrategySection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "arrow.clockwise")
                    .foregroundColor(.blue)
                Text("更新策略")
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            
            VStack(spacing: 8) {
                ForEach(UpdateStrategy.allCases, id: \.self) { strategy in
                    updateStrategyRow(for: strategy)
                }
            }
        }
        .padding()
        .background(Color(red: 0.95, green: 0.95, blue: 0.97))
        .cornerRadius(12)
    }
    
    /// 更新策略行
    func updateStrategyRow(for strategy: UpdateStrategy) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(strategy.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                
                Text(strategy.description)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            if updateManager.updateStrategy == strategy {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.blue)
            } else {
                Image(systemName: "circle")
                    .foregroundColor(.gray)
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            updateManager.setUpdateStrategy(strategy)
        }
    }
    
    /// 预览部分
    var previewSection: some View {
        Button(action: {
            showingPreview = true
        }) {
            HStack {
                Image(systemName: "eye")
                Text("预览复杂功能")
                Spacer()
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color.blue.opacity(0.1))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    /// 状态信息部分
    var statusSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.blue)
                Text("状态信息")
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                statusRow(title: "最后更新", value: formatTime(updateManager.lastUpdateTime))
                statusRow(title: "更新间隔", value: formatInterval(updateManager.updateInterval))
                statusRow(title: "当前收入", value: complicationProvider.getFormattedEarnings())
                statusRow(title: "今日进度", value: complicationProvider.getProgressText())
            }
        }
        .padding()
        .background(Color(red: 0.95, green: 0.95, blue: 0.97))
        .cornerRadius(12)
    }
    
    /// 状态行
    func statusRow(title: String, value: String) -> some View {
        HStack {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
        }
    }
}

// MARK: - Helper Methods
private extension ComplicationSettingsView {
    
    /// 加载当前设置
    func loadCurrentSettings() {
        selectedTheme = styleManager.currentTheme
        // 这里可以从持久化存储加载用户选择的样式
    }
    
    /// 应用样式变化
    func applyStyleChange() {
        // 这里可以保存用户选择的样式到持久化存储
        // 并触发复杂功能更新
        updateManager.updateImmediately()
    }
    
    /// 应用主题变化
    func applyThemeChange() {
        styleManager.updateTheme(selectedTheme)
        updateManager.updateImmediately()
    }
    
    /// 格式化时间
    func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
    
    /// 格式化时间间隔
    func formatInterval(_ interval: TimeInterval) -> String {
        let minutes = Int(interval / 60)
        if minutes < 60 {
            return "\(minutes)分钟"
        } else {
            let hours = minutes / 60
            let remainingMinutes = minutes % 60
            if remainingMinutes == 0 {
                return "\(hours)小时"
            } else {
                return "\(hours)小时\(remainingMinutes)分钟"
            }
        }
    }
}

// MARK: - Preview
struct ComplicationSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ComplicationSettingsView()
        }
    }
}