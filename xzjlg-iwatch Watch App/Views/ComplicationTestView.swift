//
//  ComplicationTestView.swift
//  xzjlg-iwatch Watch App
//
//  Created by Assistant on 2025/7/26.
//

import SwiftUI
import ClockKit

/// 复杂功能测试视图
/// 用于测试复杂功能的各种功能和数据更新
struct ComplicationTestView: View {
    
    // MARK: - Properties
    
    @StateObject private var complicationProvider = WatchComplicationProvider.shared
    @StateObject private var updateManager = ComplicationUpdateManager.shared
    @State private var testResults: [TestResult] = []
    @State private var isRunningTests = false
    
    // MARK: - Body
    
    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                // 标题
                Text("复杂功能测试")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .padding(.top)
                
                // 当前数据显示
                currentDataSection
                
                // 测试按钮
                testButtonsSection
                
                // 测试结果
                if !testResults.isEmpty {
                    testResultsSection
                }
                
                // 系统信息
                systemInfoSection
            }
            .padding(.horizontal)
        }
        .navigationTitle("复杂功能测试")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - View Components
private extension ComplicationTestView {
    
    /// 当前数据部分
    var currentDataSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("当前数据")
                .font(.subheadline)
                .fontWeight(.medium)
            
            VStack(alignment: .leading, spacing: 4) {
                dataRow(title: "收入", value: complicationProvider.getFormattedEarnings())
                dataRow(title: "进度", value: complicationProvider.getProgressText())
                dataRow(title: "目标进度", value: complicationProvider.getGoalProgressText())
                dataRow(title: "状态", value: complicationProvider.getShortStatusText())
                dataRow(title: "最后更新", value: formatTime(complicationProvider.lastUpdateTime))
            }
        }
        .padding()
        .background(Color(red: 0.95, green: 0.95, blue: 0.97))
        .cornerRadius(12)
    }
    
    /// 数据行
    func dataRow(title: String, value: String) -> some View {
        HStack {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
        }
    }
    
    /// 测试按钮部分
    var testButtonsSection: some View {
        VStack(spacing: 12) {
            Text("测试功能")
                .font(.subheadline)
                .fontWeight(.medium)
            
            VStack(spacing: 8) {
                testButton(title: "更新数据", action: testDataUpdate)
                testButton(title: "强制更新", action: testForceUpdate)
                testButton(title: "测试所有样式", action: testAllStyles)
                testButton(title: "清除测试结果", action: clearTestResults)
            }
        }
        .padding()
        .background(Color(red: 0.95, green: 0.95, blue: 0.97))
        .cornerRadius(12)
    }
    
    /// 测试按钮
    func testButton(title: String, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack {
                Text(title)
                    .font(.caption)
                Spacer()
                if isRunningTests {
                    ProgressView()
                        .scaleEffect(0.7)
                } else {
                    Image(systemName: "play.circle")
                        .font(.caption)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color.blue.opacity(0.1))
            .cornerRadius(8)
        }
        .disabled(isRunningTests)
        .buttonStyle(PlainButtonStyle())
    }
    
    /// 测试结果部分
    var testResultsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("测试结果")
                .font(.subheadline)
                .fontWeight(.medium)
            
            VStack(spacing: 4) {
                ForEach(testResults.indices, id: \.self) { index in
                    let result = testResults[index]
                    testResultRow(result: result)
                }
            }
        }
        .padding()
        .background(Color(red: 0.95, green: 0.95, blue: 0.97))
        .cornerRadius(12)
    }
    
    /// 测试结果行
    func testResultRow(result: TestResult) -> some View {
        HStack {
            Image(systemName: result.success ? "checkmark.circle.fill" : "xmark.circle.fill")
                .foregroundColor(result.success ? .green : .red)
                .font(.caption)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(result.testName)
                    .font(.caption)
                    .fontWeight(.medium)
                
                Text(result.message)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text(formatTime(result.timestamp))
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
    
    /// 系统信息部分
    var systemInfoSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("系统信息")
                .font(.subheadline)
                .fontWeight(.medium)
            
            VStack(alignment: .leading, spacing: 4) {
                systemInfoRow(title: "复杂功能启用", value: updateManager.isComplicationEnabled ? "是" : "否")
                systemInfoRow(title: "更新策略", value: updateManager.updateStrategy.displayName)
                systemInfoRow(title: "更新间隔", value: formatInterval(updateManager.updateInterval))
                systemInfoRow(title: "活跃复杂功能", value: "\(getActiveComplicationsCount())")
            }
        }
        .padding()
        .background(Color(red: 0.95, green: 0.95, blue: 0.97))
        .cornerRadius(12)
    }
    
    /// 系统信息行
    func systemInfoRow(title: String, value: String) -> some View {
        HStack {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
        }
    }
}

// MARK: - Test Methods
private extension ComplicationTestView {
    
    /// 测试数据更新
    func testDataUpdate() {
        isRunningTests = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            complicationProvider.updateComplicationData()
            
            let result = TestResult(
                testName: "数据更新测试",
                success: true,
                message: "数据更新成功",
                timestamp: Date()
            )
            
            testResults.append(result)
            isRunningTests = false
        }
    }
    
    /// 测试强制更新
    func testForceUpdate() {
        isRunningTests = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            complicationProvider.forceUpdateComplicationData()
            
            let result = TestResult(
                testName: "强制更新测试",
                success: true,
                message: "强制更新完成",
                timestamp: Date()
            )
            
            testResults.append(result)
            isRunningTests = false
        }
    }
    
    /// 测试所有样式
    func testAllStyles() {
        isRunningTests = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let styles = ComplicationStyle.allCases
            
            for style in styles {
                let result = TestResult(
                    testName: "样式测试: \(style.displayName)",
                    success: true,
                    message: "样式 \(style.displayName) 测试通过",
                    timestamp: Date()
                )
                testResults.append(result)
            }
            
            isRunningTests = false
        }
    }
    
    /// 清除测试结果
    func clearTestResults() {
        testResults.removeAll()
    }
}

// MARK: - Helper Methods
private extension ComplicationTestView {
    
    /// 格式化时间
    func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
    
    /// 格式化时间间隔
    func formatInterval(_ interval: TimeInterval) -> String {
        let minutes = Int(interval / 60)
        if minutes < 60 {
            return "\(minutes)分钟"
        } else {
            let hours = minutes / 60
            let remainingMinutes = minutes % 60
            if remainingMinutes == 0 {
                return "\(hours)小时"
            } else {
                return "\(hours)小时\(remainingMinutes)分钟"
            }
        }
    }
    
    /// 获取活跃复杂功能数量
    func getActiveComplicationsCount() -> Int {
        let server = CLKComplicationServer.sharedInstance()
        return server.activeComplications?.count ?? 0
    }
}

// MARK: - Supporting Types

/// 测试结果
struct TestResult {
    let testName: String
    let success: Bool
    let message: String
    let timestamp: Date
}

// MARK: - Preview
struct ComplicationTestView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ComplicationTestView()
        }
    }
}