//
//  ErrorHandlingViews.swift
//  xzjlg-iwatch Watch App
//
//  Created by Assistant on 2025/1/20.
//

import SwiftUI

// MARK: - 错误弹窗组件

/// 错误弹窗视图
struct ErrorAlertView: View {
    @ObservedObject var errorHandler = GlobalErrorHandler.shared
    
    var body: some View {
        EmptyView()
            .alert(
                "发生错误",
                isPresented: $errorHandler.showErrorAlert
            ) {
                if let error = errorHandler.currentError {
                    // 恢复选项按钮
                    ForEach(error.recoveryOptions, id: \.self) { option in
                        Button(option.displayName) {
                            errorHandler.executeRecoveryAction(option, for: error)
                        }
                    }
                    
                    // 取消按钮
                    Button("取消", role: .cancel) {
                        errorHandler.clearCurrentError()
                    }
                }
            } message: {
                if let error = errorHandler.currentError {
                    Text(error.userMessage)
                }
            }
    }
}

// MARK: - 错误历史视图

/// 错误历史列表视图
struct ErrorHistoryView: View {
    @ObservedObject var errorHandler = GlobalErrorHandler.shared
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 8) {
                    if errorHandler.errorHistory.isEmpty {
                        EmptyErrorHistoryView()
                    } else {
                        ForEach(errorHandler.errorHistory) { entry in
                            ErrorHistoryRowView(entry: entry)
                        }
                    }
                }
                .padding(.horizontal, 8)
            }
            .navigationTitle("错误历史")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    Button("关闭") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .topBarTrailing) {
                    Button("清理") {
                        errorHandler.cleanupErrorHistory()
                    }
                    .foregroundColor(.red)
                }
            }
        }
    }
}

/// 空错误历史视图
struct EmptyErrorHistoryView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "checkmark.shield.fill")
                .font(.system(size: 40))
                .foregroundColor(.green)
            
            Text("暂无错误记录")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Text("系统运行良好！")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .multilineTextAlignment(.center)
    }
}

/// 错误历史行视图
struct ErrorHistoryRowView: View {
    let entry: ErrorLogEntry
    @State private var showDetails = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                // 错误级别图标
                Image(systemName: severityIcon)
                    .font(.caption)
                    .foregroundColor(severityColor)
                
                // 错误分类
                Text(entry.category.displayName)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                // 时间
                Text(entry.timestamp, style: .time)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            // 错误消息
            Text(entry.userMessage)
                .font(.caption)
                .foregroundColor(.primary)
                .lineLimit(showDetails ? nil : 2)
            
            // 恢复动作
            if let recoveryAction = entry.recoveryAction {
                HStack {
                    Image(systemName: "arrow.clockwise.circle.fill")
                        .font(.caption2)
                        .foregroundColor(.blue)
                    
                    Text("已执行: \(recoveryAction.displayName)")
                        .font(.caption2)
                        .foregroundColor(.blue)
                }
            }
        }
        .padding(8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.gray.opacity(0.2))
        )
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.2)) {
                showDetails.toggle()
            }
        }
    }
    
    private var severityIcon: String {
        switch entry.severity {
        case .info:
            return "info.circle.fill"
        case .warning:
            return "exclamationmark.triangle.fill"
        case .error:
            return "xmark.circle.fill"
        case .critical:
            return "exclamationmark.octagon.fill"
        }
    }
    
    private var severityColor: Color {
        switch entry.severity {
        case .info:
            return .blue
        case .warning:
            return .orange
        case .error:
            return .red
        case .critical:
            return .purple
        }
    }
}

// MARK: - 错误统计视图

/// 错误统计视图
struct ErrorStatisticsView: View {
    @ObservedObject var errorHandler = GlobalErrorHandler.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("错误统计")
                .font(.headline)
                .foregroundColor(.primary)
            
            if errorHandler.errorStatistics.isEmpty {
                Text("暂无统计数据")
                    .font(.caption)
                    .foregroundColor(.secondary)
            } else {
                ForEach(Array(errorHandler.errorStatistics.keys), id: \.self) { category in
                    HStack {
                        Text(category.displayName)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        Text("\(errorHandler.errorStatistics[category] ?? 0)")
                            .font(.caption)
                            .foregroundColor(.primary)
                            .bold()
                    }
                }
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.gray.opacity(0.2))
        )
    }
}

// MARK: - 错误指示器组件

/// 全局错误指示器
struct ErrorIndicatorView: View {
    @ObservedObject var errorHandler = GlobalErrorHandler.shared
    @State private var showErrorHistory = false
    
    var body: some View {
        VStack {
            if !errorHandler.errorHistory.isEmpty {
                Button(action: {
                    showErrorHistory = true
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.caption2)
                            .foregroundColor(.orange)
                        
                        Text("\(errorHandler.errorHistory.count)")
                            .font(.caption2)
                            .foregroundColor(.orange)
                    }
                }
                .buttonStyle(.borderless)
                .sheet(isPresented: $showErrorHistory) {
                    ErrorHistoryView()
                }
            }
        }
    }
}

// MARK: - 系统健康状态视图

/// 系统健康状态概览
struct SystemHealthView: View {
    @ObservedObject var errorHandler = GlobalErrorHandler.shared
    @State private var showDetails = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("系统状态")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                systemHealthIndicator
            }
            
            if showDetails {
                ErrorStatisticsView()
                
                Button("查看详细历史") {
                    // 可以添加导航到详细页面的逻辑
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black.opacity(0.1))
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.3)) {
                showDetails.toggle()
            }
        }
    }
    
    private var systemHealthIndicator: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(healthColor)
                .frame(width: 8, height: 8)
            
            Text(healthText)
                .font(.caption)
                .foregroundColor(healthColor)
        }
    }
    
    private var healthColor: Color {
        let recentErrors = errorHandler.errorHistory.filter { entry in
            entry.timestamp > Date().addingTimeInterval(-24 * 60 * 60) // 最近24小时
        }
        
        let criticalErrors = recentErrors.filter { $0.severity == .critical }
        let errors = recentErrors.filter { $0.severity == .error }
        
        if !criticalErrors.isEmpty {
            return .red
        } else if errors.count > 5 {
            return .orange
        } else if errors.count > 0 {
            return .yellow
        } else {
            return .green
        }
    }
    
    private var healthText: String {
        switch healthColor {
        case .red:
            return "需要关注"
        case .orange:
            return "一般"
        case .yellow:
            return "良好"
        default:
            return "优秀"
        }
    }
}

// MARK: - 恢复动作按钮组

/// 恢复动作按钮组
struct RecoveryActionButtonsView: View {
    let error: AppError
    let onAction: (ErrorRecoveryOption) -> Void
    
    var body: some View {
        VStack(spacing: 8) {
            ForEach(error.recoveryOptions, id: \.self) { option in
                Button(action: {
                    onAction(option)
                }) {
                    HStack {
                        Image(systemName: optionIcon(for: option))
                            .font(.caption)
                        
                        Text(option.displayName)
                            .font(.caption)
                        
                        Spacer()
                    }
                    .foregroundColor(optionColor(for: option))
                }
                .buttonStyle(.borderless)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(optionColor(for: option).opacity(0.1))
                )
            }
        }
    }
    
    private func optionIcon(for option: ErrorRecoveryOption) -> String {
        switch option {
        case .retry:
            return "arrow.clockwise"
        case .reset:
            return "arrow.counterclockwise"
        case .useDefault:
            return "gear"
        case .ignore:
            return "xmark"
        case .restart:
            return "power"
        case .contact:
            return "questionmark.circle"
        }
    }
    
    private func optionColor(for option: ErrorRecoveryOption) -> Color {
        switch option {
        case .retry:
            return .blue
        case .reset:
            return .orange
        case .useDefault:
            return .green
        case .ignore:
            return .gray
        case .restart:
            return .red
        case .contact:
            return .purple
        }
    }
}

// MARK: - 系统健康详细视图

/// 系统健康详细视图
struct SystemHealthDetailView: View {
    @ObservedObject var errorHandler = GlobalErrorHandler.shared
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 16) {
                    // 总体健康状态
                    SystemHealthView()
                    
                    // 错误统计详情
                    ErrorStatisticsDetailView()
                    
                    // 最近错误列表
                    RecentErrorsView()
                    
                    // 系统信息
                    SystemInfoView()
                }
                .padding()
            }
            .navigationTitle("系统健康")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    Button("关闭") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .topBarTrailing) {
                    Button("刷新") {
                        errorHandler.cleanupErrorHistory()
                    }
                    .foregroundColor(.blue)
                }
            }
        }
    }
}

/// 错误统计详细视图
struct ErrorStatisticsDetailView: View {
    @ObservedObject var errorHandler = GlobalErrorHandler.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("24小时错误统计")
                .font(.headline)
                .foregroundColor(.primary)
            
            let recentStats = getRecentErrorStatistics()
            
            if recentStats.isEmpty {
                Text("最近24小时无错误记录")
                    .font(.caption)
                    .foregroundColor(.secondary)
            } else {
                ForEach(Array(recentStats.keys), id: \.self) { category in
                    HStack {
                        Circle()
                            .fill(categoryColor(for: category))
                            .frame(width: 8, height: 8)
                        
                        Text(category.displayName)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        Text("\(recentStats[category] ?? 0)")
                            .font(.caption)
                            .foregroundColor(.primary)
                            .bold()
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.2))
        )
    }
    
    private func getRecentErrorStatistics() -> [ErrorCategory: Int] {
        let oneDayAgo = Date().addingTimeInterval(-24 * 60 * 60)
        let recentErrors = errorHandler.errorHistory.filter { $0.timestamp > oneDayAgo }
        
        var stats: [ErrorCategory: Int] = [:]
        for error in recentErrors {
            stats[error.category, default: 0] += 1
        }
        return stats
    }
    
    private func categoryColor(for category: ErrorCategory) -> Color {
        switch category {
        case .calculation: return .blue
        case .persistence: return .green
        case .notification: return .orange
        case .configuration: return .purple
        case .system: return .red
        case .network: return .cyan
        case .userInput: return .yellow
        }
    }
}

/// 最近错误视图
struct RecentErrorsView: View {
    @ObservedObject var errorHandler = GlobalErrorHandler.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("最近错误")
                .font(.headline)
                .foregroundColor(.primary)
            
            let recentErrors = Array(errorHandler.errorHistory.prefix(5))
            
            if recentErrors.isEmpty {
                Text("暂无错误记录")
                    .font(.caption)
                    .foregroundColor(.secondary)
            } else {
                ForEach(recentErrors) { entry in
                    ErrorHistoryRowView(entry: entry)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.2))
        )
    }
}

/// 系统信息视图
struct SystemInfoView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("系统信息")
                .font(.headline)
                .foregroundColor(.primary)
            
            VStack(alignment: .leading, spacing: 8) {
                InfoRow(title: "设备型号", value: WKInterfaceDevice.current().model)
                InfoRow(title: "系统版本", value: WKInterfaceDevice.current().systemVersion)
                InfoRow(title: "应用版本", value: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "未知")
                
                // 电池信息
                let batteryLevel = WKInterfaceDevice.current().batteryLevel
                if batteryLevel >= 0 {
                    InfoRow(title: "电池电量", value: "\(Int(batteryLevel * 100))%")
                }
                
                // 内存使用情况
                let memoryInfo = getMemoryInfo()
                InfoRow(title: "内存使用", value: memoryInfo)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.2))
        )
    }
    
    private func getMemoryInfo() -> String {
        let MACH_TASK_BASIC_INFO_COUNT = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info_data_t>.size / MemoryLayout<natural_t>.size)
        let name = mach_task_self_
        var info = mach_task_basic_info_data_t()
        var count = MACH_TASK_BASIC_INFO_COUNT
        
        let kerr = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(name, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let memoryUsage = Double(info.resident_size) / 1024.0 / 1024.0
            return String(format: "%.1f MB", memoryUsage)
        } else {
            return "未知"
        }
    }
}

/// 信息行视图
struct InfoRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.caption)
                .foregroundColor(.primary)
        }
    }
}

// MARK: - 预览

struct ErrorHandlingViews_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            ErrorHistoryView()
                .previewDisplayName("错误历史")
            
            SystemHealthView()
                .previewDisplayName("系统健康状态")
                .padding()
            
            ErrorIndicatorView()
                .previewDisplayName("错误指示器")
                
            SystemHealthDetailView()
                .previewDisplayName("系统健康详细")
        }
    }
} 