import SwiftUI

/// 增强版小福狸角色视图 - 更可爱的设计和丰富的表情动作系统
struct FoxCharacterView: View {
    @EnvironmentObject var appState: AppState
    @State private var animationPhase: Double = 0
    @State private var showSpeechBubble: Bool = false
    @State private var pulseScale: Double = 1.0
    @State private var characterScale: Double = 0.1 // 入场动画用
    @State private var characterOpacity: Double = 0.0 // 入场动画用
    @State private var entranceAnimationCompleted: Bool = false
    
    var body: some View {
        VStack(spacing: 8) {
            // 角色主体
            ZStack {
                // 背景光环效果
                if appState.currentCharacterState.showEffects {
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: effectGradientColors,
                                center: .center,
                                startRadius: 10,
                                endRadius: 50
                            )
                        )
                        .frame(width: 100, height: 100)
                        .scaleEffect(pulseScale)
                        .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: pulseScale)
                }
                
                // 小福狸主体
                ZStack {
                    // 头部 - 更可爱的椭圆形
                    Ellipse()
                        .fill(
                            RadialGradient(
                                colors: [
                                    Color(red: 1.0, green: 0.7, blue: 0.4), // 温暖的橘色
                                    Color(red: 0.9, green: 0.5, blue: 0.2)  // 深一点的橘色
                                ],
                                center: UnitPoint(x: 0.3, y: 0.3), // 高光位置
                                startRadius: 5,
                                endRadius: 40
                            )
                        )
                        .frame(width: 65, height: 55) // 略扁的椭圆形，更可爱
                        .shadow(color: .black.opacity(0.15), radius: 6, x: 2, y: 3)
                        .scaleEffect(appState.showCharacterAnimation ? 
                                   1.0 + sin(animationPhase) * 0.03 : 1.0)
                    
                    // 脸颊腮红
                    HStack(spacing: 45) {
                        Circle()
                            .fill(Color.pink.opacity(0.3))
                            .frame(width: 8, height: 8)
                        Circle()
                            .fill(Color.pink.opacity(0.3))
                            .frame(width: 8, height: 8)
                    }
                    .offset(y: 5)
                    
                    // 耳朵 - 更可爱的圆润形状
                    HStack(spacing: 35) {
                        CuteEar(isLeft: true, animationPhase: animationPhase, showAnimation: appState.showCharacterAnimation)
                        CuteEar(isLeft: false, animationPhase: animationPhase, showAnimation: appState.showCharacterAnimation)
                    }
                    .offset(y: -25)
                    
                    // 眼睛 - 增强版
                    HStack(spacing: 18) {
                        EnhancedEyeView(state: appState.currentCharacterState.eyeState, isLeft: true)
                        EnhancedEyeView(state: appState.currentCharacterState.eyeState, isLeft: false)
                    }
                    .offset(y: -5)
                    
                    // 鼻子
                    Ellipse()
                        .fill(Color.black)
                        .frame(width: 3, height: 2)
                        .offset(y: 3)
                    
                    // 嘴巴 - 增强版
                    EnhancedMouthView(state: appState.currentCharacterState.mouthState)
                        .offset(y: 12)
                    
                    // 手臂 - 从身体边缘开始的自然设计
                    ZStack {
                        // 左胳膊
                        EnhancedArmView(
                            side: .left, 
                            state: appState.currentCharacterState.armState, 
                            animationPhase: animationPhase,
                            showAnimation: appState.showCharacterAnimation
                        )
                        .position(x: 32.5 - 25, y: 25 + 15) // 从身体左侧边缘开始
                        
                        // 右胳膊  
                        EnhancedArmView(
                            side: .right, 
                            state: appState.currentCharacterState.armState, 
                            animationPhase: animationPhase,
                            showAnimation: appState.showCharacterAnimation
                        )
                        .position(x: 32.5 + 25, y: 25 + 15) // 从身体右侧边缘开始
                    }
                    .frame(width: 65, height: 55) // 与头部尺寸匹配
                    .offset(y: 25)
                }
                .scaleEffect(characterScale)
                .opacity(characterOpacity)
                
                // 特效层
                if appState.currentCharacterState.showEffects {
                    EnhancedEffectParticlesView(type: appState.currentCharacterState.effectType ?? .stars)
                        .allowsHitTesting(false)
                        .opacity(entranceAnimationCompleted ? 1.0 : 0.0)
                }
            }
            
            // 状态标识
            HStack(spacing: 4) {
                Circle()
                    .fill(appState.isCalculating ? Color.green : Color.gray)
                    .frame(width: 4, height: 4)
                    .scaleEffect(appState.isCalculating ? 1.2 : 1.0)
                    .animation(.easeInOut(duration: 0.8).repeatForever(autoreverses: true), value: appState.isCalculating)
                
                Text(appState.currentCharacterState.displayName)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .opacity(entranceAnimationCompleted ? 1.0 : 0.0)
            
            // 对话气泡
            if !appState.currentMessage.isEmpty && showSpeechBubble && entranceAnimationCompleted {
                EnhancedSpeechBubbleView(message: appState.currentMessage)
                    .transition(.scale.combined(with: .opacity))
            }
        }
        .onAppear {
            startEntranceAnimation()
            setupNotificationObservers()
        }
        .onDisappear {
            removeNotificationObservers()
        }
        .onChange(of: appState.currentMessage) { oldValue, newValue in
            if !newValue.isEmpty && entranceAnimationCompleted {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    showSpeechBubble = true
                }
            }
        }
        .onChange(of: appState.currentCharacterState) { oldValue, newValue in
            // 状态变化时的平滑过渡动画
            withAnimation(.easeInOut(duration: 0.8)) {
                // 触发重新渲染以应用新状态
            }
        }
        .onChange(of: appState.showCharacterAnimation) { oldValue, newValue in
            // 动画开关变化时的处理
            if newValue && entranceAnimationCompleted {
                resumeAnimations()
            } else {
                pauseAnimations()
            }
        }
        .onChange(of: appState.batteryOptimizationManager.isInIdleMode) { oldValue, newValue in
            // 空闲模式变化时的动画控制
            if newValue {
                pauseAnimations()
            } else if appState.showCharacterAnimation {
                resumeAnimations()
            }
        }
    }
    
    // 入场动画
    private func startEntranceAnimation() {
        // 第一阶段：角色从小到大出现
        withAnimation(.spring(response: 0.8, dampingFraction: 0.6)) {
            characterScale = 1.0
            characterOpacity = 1.0
        }
        
        // 第二阶段：开始循环动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
            entranceAnimationCompleted = true
            startContinuousAnimations()
            pulseScale = 1.1
            
            // 延迟显示对话气泡
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                if !appState.currentMessage.isEmpty {
                    withAnimation(.spring()) {
                        showSpeechBubble = true
                    }
                }
            }
        }
    }
    
    // 持续动画
    private func startContinuousAnimations() {
        guard appState.showCharacterAnimation else { return }
        
        withAnimation(.linear(duration: appState.currentCharacterState.animationDuration).repeatForever(autoreverses: false)) {
            animationPhase = .pi * 2
        }
    }
    
    // 暂停动画
    private func pauseAnimations() {
        withAnimation(.easeOut(duration: 0.3)) {
            animationPhase = 0
        }
    }
    
    // 恢复动画
    private func resumeAnimations() {
        guard appState.showCharacterAnimation else { return }
        
        withAnimation(.linear(duration: appState.currentCharacterState.animationDuration).repeatForever(autoreverses: false)) {
            animationPhase = .pi * 2
        }
    }
    
    // 设置通知观察者
    private func setupNotificationObservers() {
        NotificationCenter.default.addObserver(
            forName: .pauseAnimations,
            object: nil,
            queue: .main
        ) { _ in
            pauseAnimations()
        }
        
        NotificationCenter.default.addObserver(
            forName: .resumeAnimations,
            object: nil,
            queue: .main
        ) { _ in
            resumeAnimations()
        }
    }
    
    // 移除通知观察者
    private func removeNotificationObservers() {
        NotificationCenter.default.removeObserver(self, name: .pauseAnimations, object: nil)
        NotificationCenter.default.removeObserver(self, name: .resumeAnimations, object: nil)
    }
    
    // 特效渐变颜色
    private var effectGradientColors: [Color] {
        switch appState.currentCharacterState.effectType {
        case .stars:
            return [Color.yellow.opacity(0.4), Color.orange.opacity(0.2), Color.clear]
        case .confetti:
            return [Color.blue.opacity(0.3), Color.purple.opacity(0.1), Color.clear]
        case .hearts:
            return [Color.pink.opacity(0.4), Color.red.opacity(0.2), Color.clear]
        default:
            return [Color.blue.opacity(0.3), Color.purple.opacity(0.1), Color.clear]
        }
    }
}

// MARK: - 增强版角色子组件

/// 可爱的耳朵组件
struct CuteEar: View {
    let isLeft: Bool
    let animationPhase: Double
    let showAnimation: Bool
    
    var body: some View {
        ZStack {
            // 外耳
            Ellipse()
                .fill(
                    LinearGradient(
                        colors: [
                            Color(red: 1.0, green: 0.7, blue: 0.4),
                            Color(red: 0.9, green: 0.5, blue: 0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 18, height: 22)
            
            // 内耳
            Ellipse()
                .fill(Color.pink.opacity(0.6))
                .frame(width: 8, height: 10)
                .offset(y: 2)
        }
        .rotationEffect(.degrees(
            isLeft ? (-10 + (showAnimation ? sin(animationPhase * 2) * 3 : 0)) :
                     (10 + (showAnimation ? sin(animationPhase * 2) * -3 : 0))
        ))
        .shadow(color: .black.opacity(0.1), radius: 2, x: 1, y: 1)
    }
}

/// 增强版眼睛视图
struct EnhancedEyeView: View {
    let state: EyeState
    let isLeft: Bool
    @State private var blinkAnimation = false
    @State private var sparkleOpacity = 0.0
    
    var body: some View {
        ZStack {
            // 眼白
            Ellipse()
                .fill(Color.white)
                .frame(width: 14, height: state == .closed ? 2 : eyeHeight)
                .overlay(
                    // 眼睛轮廓
                    Ellipse()
                        .stroke(Color.black.opacity(0.1), lineWidth: 0.5)
                )
            
            // 瞳孔和虹膜
            if state != .closed {
                ZStack {
                    // 虹膜
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [Color.black, Color.gray.opacity(0.8)],
                                center: .center,
                                startRadius: 1,
                                endRadius: pupilSize / 2
                            )
                        )
                        .frame(width: pupilSize, height: pupilSize)
                        .offset(pupilOffset)
                    
                    // 高光
                    Circle()
                        .fill(Color.white)
                        .frame(width: pupilSize * 0.3, height: pupilSize * 0.3)
                        .offset(x: pupilSize * -0.15, y: pupilSize * -0.15)
                        .offset(pupilOffset)
                        .opacity(sparkleOpacity)
                }
            }
            
            // 眼睫毛（开心状态）
            if state == .happy {
                HStack(spacing: 2) {
                    ForEach(0..<3, id: \.self) { _ in
                        Rectangle()
                            .fill(Color.black)
                            .frame(width: 0.5, height: 4)
                    }
                }
                .offset(y: -8)
            }
        }
        .animation(.easeInOut(duration: 0.2), value: blinkAnimation)
        .onAppear {
            startBlinking()
            withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
                sparkleOpacity = 1.0
            }
        }
    }
    
    private var eyeHeight: Double {
        switch state {
        case .open: return 14
        case .happy: return 12 // 稍微眯一点
        case .tired: return 10 // 更眯
        case .closed: return 2
        }
    }
    
    private var pupilSize: Double {
        switch state {
        case .open: return 8
        case .happy: return 6
        case .tired: return 7
        case .closed: return 0
        }
    }
    
    private var pupilOffset: CGSize {
        switch state {
        case .happy: return CGSize(width: isLeft ? -1 : 1, height: -0.5)
        case .tired: return CGSize(width: 0, height: 1)
        default: return CGSize.zero
        }
    }
    
    private func startBlinking() {
        Timer.scheduledTimer(withTimeInterval: Double.random(in: 2...4), repeats: true) { _ in
            withAnimation(.easeInOut(duration: 0.1)) {
                blinkAnimation.toggle()
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    blinkAnimation.toggle()
                }
            }
        }
    }
}

/// 增强版嘴巴视图
struct EnhancedMouthView: View {
    let state: MouthState
    
    var body: some View {
        Group {
            switch state {
            case .neutral:
                // 中性小点
                Circle()
                    .fill(Color.black)
                    .frame(width: 2, height: 2)
                    
            case .smile:
                // 微笑弧线
                Path { path in
                    path.move(to: CGPoint(x: -8, y: 0))
                    path.addQuadCurve(
                        to: CGPoint(x: 8, y: 0),
                        control: CGPoint(x: 0, y: 6)
                    )
                }
                .stroke(Color.black, style: StrokeStyle(lineWidth: 2, lineCap: .round))
                
            case .bigSmile:
                // 大笑
                ZStack {
                    // 嘴巴轮廓
                    Ellipse()
                        .fill(Color.black)
                        .frame(width: 16, height: 10)
                    
                    // 牙齿
                    Rectangle()
                        .fill(Color.white)
                        .frame(width: 12, height: 4)
                        .offset(y: -2)
                }
                
            case .worried:
                // 担忧表情
                Path { path in
                    path.move(to: CGPoint(x: -6, y: 6))
                    path.addQuadCurve(
                        to: CGPoint(x: 6, y: 6),
                        control: CGPoint(x: 0, y: 0)
                    )
                }
                .stroke(Color.black, style: StrokeStyle(lineWidth: 2, lineCap: .round))
            }
        }
    }
}

/// 增强版手臂视图 - 从身体边缘自然延伸
struct EnhancedArmView: View {
    let side: ArmSide
    let state: ArmState
    let animationPhase: Double
    let showAnimation: Bool
    
    var body: some View {
        ZStack {
            // 手臂路径 - 从原点(0,0)开始
            Path { path in
                let shoulderPoint = CGPoint(x: 0, y: 0) // 肩膀位置在身体边缘
                let elbowPoint = getElbowPoint()
                let handPoint = getHandPoint()
                
                // 绘制上臂
                path.move(to: shoulderPoint)
                path.addLine(to: elbowPoint)
                
                // 绘制前臂
                path.move(to: elbowPoint)
                path.addLine(to: handPoint)
            }
            .stroke(
                Color(red: 1.0, green: 0.7, blue: 0.4),
                style: StrokeStyle(lineWidth: 3.5, lineCap: .round, lineJoin: .round)
            )
            
            // 肩膀连接点
            Circle()
                .fill(Color(red: 1.0, green: 0.7, blue: 0.4))
                .frame(width: 4, height: 4)
                .position(CGPoint(x: 0, y: 0))
            
            // 肘部关节
            Circle()
                .fill(Color(red: 0.95, green: 0.65, blue: 0.35))
                .frame(width: 3, height: 3)
                .position(getElbowPoint())
            
            // 手掌
            Circle()
                .fill(Color(red: 1.0, green: 0.7, blue: 0.4))
                .frame(width: 5, height: 5)
                .position(getHandPoint())
                .overlay(
                    // 手掌高光
                    Circle()
                        .fill(Color.white.opacity(0.4))
                        .frame(width: 2, height: 2)
                        .position(CGPoint(
                            x: getHandPoint().x - 0.5,
                            y: getHandPoint().y - 0.5
                        ))
                )
        }
        .frame(width: 25, height: 30) // 限制胳膊显示区域
    }
    
    // 获取肘部位置
    private func getElbowPoint() -> CGPoint {
        let baseAngle: Double = side == .left ? 45 : -45 // 基础角度
        let baseDistance: CGFloat = 12 // 上臂长度
        
        var angle = baseAngle
        var distance = baseDistance
        
        // 根据状态调整角度和距离
        switch state {
        case .relaxed:
            angle += side == .left ? 15 : -15
            
        case .waving:
            let waveOffset = showAnimation ? sin(animationPhase * 2) * 20 : 0
            angle += waveOffset
            distance += showAnimation ? CGFloat(sin(animationPhase * 2)) * 2 : 0
            
        case .working:
            angle += side == .left ? 25 : -25
            
        case .cheering:
            angle -= side == .left ? 30 : -30 // 向上举
            let cheerOffset = showAnimation ? sin(animationPhase * 2.5) * 10 : 0
            angle += cheerOffset
            
        case .celebrating:
            angle -= side == .left ? 45 : -45 // 大幅向上
            let celebrateOffset = showAnimation ? sin(animationPhase * 3) * 25 : 0
            angle += celebrateOffset
            distance += showAnimation ? CGFloat(sin(animationPhase * 3)) * 3 : 0
            
        case .activeWorking:
            let workOffset = showAnimation ? sin(animationPhase * 2.5) * 15 : 0
            angle += workOffset
        }
        
        // 将角度转换为弧度并计算位置
        let radian = angle * .pi / 180
        let x = distance * cos(radian) * (side == .left ? -1 : 1)
        let y = distance * sin(radian)
        
        return CGPoint(x: x, y: y)
    }
    
    // 获取手部位置
    private func getHandPoint() -> CGPoint {
        let elbow = getElbowPoint()
        let forearmLength: CGFloat = 10 // 前臂长度
        
        // 计算前臂的方向角度
        var forearmAngle: Double = 0
        
        switch state {
        case .relaxed:
            forearmAngle = side == .left ? 90 : 90 // 向下
            
        case .waving:
            forearmAngle = side == .left ? 45 : 45 // 斜下
            let waveMotion = showAnimation ? sin(animationPhase * 2) * 30 : 0
            forearmAngle += waveMotion
            
        case .working:
            forearmAngle = side == .left ? 75 : 75 // 轻微向下
            
        case .cheering:
            forearmAngle = side == .left ? 0 : 0 // 向前
            let cheerMotion = showAnimation ? sin(animationPhase * 2.5) * 20 : 0
            forearmAngle += cheerMotion
            
        case .celebrating:
            forearmAngle = side == .left ? -30 : -30 // 向上
            let celebrateMotion = showAnimation ? sin(animationPhase * 3) * 40 : 0
            forearmAngle += celebrateMotion
            
        case .activeWorking:
            forearmAngle = side == .left ? 60 : 60
            let workMotion = showAnimation ? cos(animationPhase * 2.5) * 25 : 0
            forearmAngle += workMotion
        }
        
        // 将角度转换为弧度并计算手的位置
        let radian = forearmAngle * .pi / 180
        let x = elbow.x + forearmLength * cos(radian) * (side == .left ? -1 : 1)
        let y = elbow.y + forearmLength * sin(radian)
        
        return CGPoint(x: x, y: y)
    }
}

/// 增强版特效粒子视图
struct EnhancedEffectParticlesView: View {
    let type: EffectType
    @State private var particles: [EnhancedParticleData] = []
    @State private var animationTimer: Timer?
    @State private var particlePhase: Double = 0
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        ZStack {
            ForEach(particles, id: \.id) { particle in
                FoxParticleView(particle: particle, type: type, phase: particlePhase)
                    .opacity(shouldShowParticle(particle) ? particle.opacity : 0)
            }
        }
        .onAppear {
            generateParticles()
            startParticleAnimation()
        }
        .onDisappear {
            stopParticleAnimation()
        }
        .onChange(of: appState.batteryOptimizationLevel) { oldValue, newValue in
            adjustParticleComplexity(for: newValue)
        }
    }
    
    // MARK: - 粒子生成和管理
    private func generateParticles() {
        let particleCount = getParticleCount()
        particles = (0..<particleCount).map { i in
            EnhancedParticleData(
                id: i,
                symbol: getParticleSymbol(),
                color: getRandomColor(),
                position: getInitialPosition(for: i),
                velocity: getInitialVelocity(),
                size: getParticleSize(),
                opacity: Double.random(in: 0.6...1.0),
                scale: Double.random(in: 0.4...1.3),
                rotation: Double.random(in: 0...360),
                rotationSpeed: Double.random(in: -30...30),
                lifetime: getParticleLifetime(),
                age: 0,
                trail: []
            )
        }
    }
    
    private func getParticleCount() -> Int {
        switch appState.batteryOptimizationLevel {
        case .normal:
            return type == .confetti ? 25 : 20
        case .reduced:
            return type == .confetti ? 15 : 12
        case .minimal:
            return type == .confetti ? 8 : 6
        }
    }
    
    private func getParticleSize() -> Double {
        switch type {
        case .stars:
            return Double.random(in: 8...16)
        case .confetti:
            return Double.random(in: 6...12)
        case .hearts:
            return Double.random(in: 10...18)
        }
    }
    
    private func getParticleLifetime() -> Double {
        switch type {
        case .stars:
            return Double.random(in: 3...6)
        case .confetti:
            return Double.random(in: 2...4)
        case .hearts:
            return Double.random(in: 4...7)
        }
    }
    
    private func getInitialPosition(for index: Int) -> CGPoint {
        switch type {
        case .confetti:
            // 庆祝特效从上方爆炸式散开
            let angle = Double(index) * (360.0 / Double(getParticleCount()))
            let radius = Double.random(in: 10...30)
            return CGPoint(
                x: radius * cos(angle * .pi / 180),
                y: radius * sin(angle * .pi / 180) - 20
            )
        case .stars:
            // 星星在周围闪烁
            return CGPoint(
                x: Double.random(in: -80...80),
                y: Double.random(in: -80...80)
            )
        case .hearts:
            // 心形从中心向外飘散
            return CGPoint(
                x: Double.random(in: -20...20),
                y: Double.random(in: -20...20)
            )
        }
    }
    
    private func getInitialVelocity() -> CGVector {
        switch type {
        case .confetti:
            return CGVector(
                dx: Double.random(in: -50...50),
                dy: Double.random(in: -80...20)
            )
        case .stars:
            return CGVector(
                dx: Double.random(in: -10...10),
                dy: Double.random(in: -10...10)
            )
        case .hearts:
            return CGVector(
                dx: Double.random(in: -30...30),
                dy: Double.random(in: -40...10)
            )
        }
    }
    
    // MARK: - 动画控制
    private func startParticleAnimation() {
        let updateInterval = getUpdateInterval()
        animationTimer = Timer.scheduledTimer(withTimeInterval: updateInterval, repeats: true) { _ in
            updateParticles()
        }
        
        // 主动画相位
        withAnimation(.linear(duration: 10.0).repeatForever(autoreverses: false)) {
            particlePhase = .pi * 4
        }
    }
    
    private func stopParticleAnimation() {
        animationTimer?.invalidate()
        animationTimer = nil
    }
    
    private func getUpdateInterval() -> TimeInterval {
        switch appState.batteryOptimizationLevel {
        case .normal:
            return 1.0 / 30.0 // 30 FPS
        case .reduced:
            return 1.0 / 20.0 // 20 FPS
        case .minimal:
            return 1.0 / 10.0 // 10 FPS
        }
    }
    
    private func updateParticles() {
        let deltaTime = getUpdateInterval()
        
        for i in particles.indices {
            particles[i].age += deltaTime
            
            // 更新位置
            particles[i].position.x += particles[i].velocity.dx * deltaTime
            particles[i].position.y += particles[i].velocity.dy * deltaTime
            
            // 添加重力效果（confetti 类型）
            if type == .confetti {
                particles[i].velocity.dy += 150 * deltaTime // 重力加速度
            }
            
            // 更新透明度（生命周期效果）
            let lifeProgress = particles[i].age / particles[i].lifetime
            particles[i].opacity = max(0, 1.0 - lifeProgress)
            
            // 更新旋转
            particles[i].rotation += particles[i].rotationSpeed * deltaTime
            
            // 添加轨迹点（仅在高性能模式下）
            if appState.batteryOptimizationLevel == .normal && particles[i].trail.count < 5 {
                particles[i].trail.append(particles[i].position)
                if particles[i].trail.count > 5 {
                    particles[i].trail.removeFirst()
                }
            }
            
            // 重新生成过期粒子
            if particles[i].age > particles[i].lifetime {
                particles[i] = regenerateParticle(at: i)
            }
        }
    }
    
    private func regenerateParticle(at index: Int) -> EnhancedParticleData {
        return EnhancedParticleData(
            id: index,
            symbol: getParticleSymbol(),
            color: getRandomColor(),
            position: getInitialPosition(for: index),
            velocity: getInitialVelocity(),
            size: getParticleSize(),
            opacity: Double.random(in: 0.6...1.0),
            scale: Double.random(in: 0.4...1.3),
            rotation: Double.random(in: 0...360),
            rotationSpeed: Double.random(in: -30...30),
            lifetime: getParticleLifetime(),
            age: 0,
            trail: []
        )
    }
    
    private func shouldShowParticle(_ particle: EnhancedParticleData) -> Bool {
        // 边界检查 - 移出屏幕的粒子不显示
        let bounds: CGFloat = 120
        return abs(particle.position.x) < bounds && abs(particle.position.y) < bounds
    }
    
    // MARK: - 性能优化
    private func adjustParticleComplexity(for level: BatteryOptimizationLevel) {
        // 重新生成适应当前电量级别的粒子
        generateParticles()
        
        // 重启动画定时器
        stopParticleAnimation()
        startParticleAnimation()
    }
    
    private func getParticleSymbol() -> String {
        switch type {
        case .stars:
            let symbols = ["star.fill", "sparkle", "star.circle.fill", "asterisk", "star.square.fill"]
            return symbols.randomElement() ?? "star.fill"
        case .confetti:
            let symbols = ["circle.fill", "diamond.fill", "triangle.fill", "square.fill", "pentagon.fill", "hexagon.fill"]
            return symbols.randomElement() ?? "circle.fill"
        case .hearts:
            let symbols = ["heart.fill", "heart", "suit.heart.fill", "heart.circle.fill"]
            return symbols.randomElement() ?? "heart.fill"
        }
    }
    
    private func getRandomColor() -> Color {
        switch type {
        case .stars:
            let colors = [Color.yellow, Color.orange, Color.white, Color.blue, Color.cyan, Color.yellow]
            return colors.randomElement() ?? .yellow
        case .confetti:
            let colors = [Color.red, Color.green, Color.blue, Color.purple, Color.orange, Color.pink, Color.cyan, Color.yellow]
            return colors.randomElement() ?? .red
        case .hearts:
            let colors = [Color.pink, Color.red, Color.purple, Color.pink]
            return colors.randomElement() ?? .pink
        }
    }
}

/// 单个粒子视图组件
struct FoxParticleView: View {
    let particle: EnhancedParticleData
    let type: EffectType
    let phase: Double
    
    var body: some View {
        ZStack {
            // 粒子轨迹（仅高性能模式）
            if !particle.trail.isEmpty {
                Path { path in
                    for (index, point) in particle.trail.enumerated() {
                        if index == 0 {
                            path.move(to: point)
                        } else {
                            path.addLine(to: point)
                        }
                    }
                }
                .stroke(
                    particle.color.opacity(0.3),
                    style: StrokeStyle(lineWidth: 1, lineCap: .round, lineJoin: .round)
                )
            }
            
            // 主粒子
            Group {
                if type == .stars {
                    // 星星特效增强
                    ZStack {
                        Image(systemName: particle.symbol)
                            .foregroundColor(particle.color)
                            .font(.system(size: particle.size))
                            .scaleEffect(particle.scale + sin(phase + Double(particle.id)) * 0.2)
                        
                        // 星星光芒效果
                        Image(systemName: "plus")
                            .foregroundColor(particle.color.opacity(0.6))
                            .font(.system(size: particle.size * 0.6))
                            .scaleEffect(particle.scale + cos(phase + Double(particle.id)) * 0.3)
                            .rotationEffect(.degrees(45))
                    }
                } else if type == .hearts {
                    // 心形特效增强
                    ZStack {
                        Image(systemName: particle.symbol)
                            .foregroundColor(particle.color)
                            .font(.system(size: particle.size))
                            .scaleEffect(particle.scale + sin(phase * 0.5 + Double(particle.id)) * 0.15)
                        
                        // 心形光晕
                        Image(systemName: "heart")
                            .foregroundColor(particle.color.opacity(0.3))
                            .font(.system(size: particle.size * 1.5))
                            .scaleEffect(particle.scale + cos(phase * 0.3 + Double(particle.id)) * 0.2)
                    }
                } else {
                    // 彩色纸屑
                    Image(systemName: particle.symbol)
                        .foregroundColor(particle.color)
                        .font(.system(size: particle.size))
                        .scaleEffect(particle.scale)
                }
            }
        }
        .position(particle.position)
        .rotationEffect(.degrees(particle.rotation))
    }
}

struct EnhancedParticleData {
    let id: Int
    let symbol: String
    let color: Color
    var position: CGPoint
    var velocity: CGVector
    let size: Double
    var opacity: Double
    var scale: Double
    var rotation: Double
    var rotationSpeed: Double
    let lifetime: Double
    var age: Double
    var trail: [CGPoint]
}

/// 增强版对话气泡视图 - 自适应大小和丰富动画
struct EnhancedSpeechBubbleView: View {
    let message: String
    @State private var animationOffset: CGFloat = 0
    @State private var textOpacity: Double = 0
    @State private var bubbleScale: Double = 0.8
    @State private var emojiScale: Double = 1.0
    @State private var isVisible: Bool = false
    
    var body: some View {
        VStack(spacing: 1) {
            // 气泡主体
            Text(message)
                .font(adaptiveFontSize)
                .fontWeight(.medium)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, horizontalPadding)
                .padding(.vertical, verticalPadding)
                .background(
                    RoundedRectangle(cornerRadius: adaptiveCornerRadius)
                        .fill(bubbleBackground)
                        .shadow(color: .black.opacity(0.12), radius: 6, x: 0, y: 3)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: adaptiveCornerRadius)
                        .stroke(bubbleBorder, lineWidth: 1)
                )
                .scaleEffect(bubbleScale)
                .opacity(textOpacity)
            
            // 气泡尾巴
            Triangle()
                .fill(Color.white)
                .frame(width: tailWidth, height: tailHeight)
                .overlay(
                    Triangle()
                        .stroke(Color.gray.opacity(0.15), lineWidth: 0.8)
                )
                .rotationEffect(.degrees(180))
                .offset(y: -1)
                .shadow(color: .black.opacity(0.08), radius: 3, x: 0, y: 2)
                .scaleEffect(bubbleScale)
                .opacity(textOpacity)
        }
        .offset(y: animationOffset)
        .scaleEffect(isVisible ? 1.0 : 0.3)
        .opacity(isVisible ? 1.0 : 0.0)
        .onAppear {
            startEntranceAnimation()
        }
        .onChange(of: message) { oldValue, newValue in
            if !newValue.isEmpty {
                restartAnimation()
            }
        }
    }
    
    // MARK: - 自适应属性
    private var adaptiveFontSize: Font {
        let length = message.count
        if length > 25 {
            return .caption2
        } else if length > 15 {
            return .caption
        } else {
            return .footnote
        }
    }
    
    private var horizontalPadding: CGFloat {
        let length = message.count
        return length > 20 ? 12 : 16
    }
    
    private var verticalPadding: CGFloat {
        let length = message.count
        return length > 20 ? 6 : 8
    }
    
    private var adaptiveCornerRadius: CGFloat {
        let length = message.count
        return length > 25 ? 14 : 18
    }
    
    private var tailWidth: CGFloat {
        let length = message.count
        return length > 20 ? 10 : 14
    }
    
    private var tailHeight: CGFloat {
        let length = message.count
        return length > 20 ? 6 : 8
    }
    
    // MARK: - 样式属性
    private var bubbleBackground: LinearGradient {
        if message.contains("🎉") || message.contains("🏆") || message.contains("👏") {
            // 庆祝主题
            return LinearGradient(
                colors: [
                    Color.yellow.opacity(0.15),
                    Color.orange.opacity(0.08),
                    Color.white
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        } else if message.contains("💪") || message.contains("🔥") || message.contains("⚡") {
            // 鼓励主题
            return LinearGradient(
                colors: [
                    Color.blue.opacity(0.12),
                    Color.cyan.opacity(0.06),
                    Color.white
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        } else if message.contains("😴") || message.contains("🌙") || message.contains("🛋️") {
            // 休息主题
            return LinearGradient(
                colors: [
                    Color.purple.opacity(0.1),
                    Color.pink.opacity(0.05),
                    Color.white
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        } else {
            // 默认主题
            return LinearGradient(
                colors: [Color.white, Color.gray.opacity(0.03)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }
    
    private var bubbleBorder: LinearGradient {
        return LinearGradient(
            colors: [Color.gray.opacity(0.15), Color.gray.opacity(0.08)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    // MARK: - 动画方法
    private func startEntranceAnimation() {
        // 第一阶段：入场动画
        withAnimation(.spring(response: 0.6, dampingFraction: 0.7)) {
            isVisible = true
        }
        
        // 第二阶段：气泡缩放动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                bubbleScale = 1.0
                textOpacity = 1.0
            }
        }
        
        // 第三阶段：开始浮动动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
            startFloatingAnimation()
        }
        
        // 表情符号动画
        if message.contains("🎉") || message.contains("🏆") || message.contains("✨") {
            startEmojiAnimation()
        }
    }
    
    private func startFloatingAnimation() {
        withAnimation(.easeInOut(duration: 4.0).repeatForever(autoreverses: true)) {
            animationOffset = -5
        }
    }
    
    private func startEmojiAnimation() {
        withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
            emojiScale = 1.2
        }
    }
    
    private func restartAnimation() {
        // 重置状态
        animationOffset = 0
        textOpacity = 0
        bubbleScale = 0.8
        isVisible = false
        
        // 重新开始动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            startEntranceAnimation()
        }
    }
}

/// 三角形形状（用于耳朵和气泡尾巴）
struct Triangle: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.move(to: CGPoint(x: rect.midX, y: rect.minY))
        path.addLine(to: CGPoint(x: rect.minX, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY))
        path.closeSubpath()
        return path
    }
}

enum ArmSide {
    case left, right
}

/// 小福狸偷看视图 - 只显示眼睛和耳朵
struct FoxPeekingView: View {
    @EnvironmentObject var appState: AppState
    @State private var animationPhase: Double = 0
    @State private var blinkPhase: Double = 0
    @State private var peekOffset: CGFloat = 0
    @State private var earWiggle: Double = 0
    
    var body: some View {
        ZStack {
            // 背景圆形区域（表示头部但不显示，用于定位）
            Circle()
                .fill(Color.clear)
                .frame(width: 60, height: 60)
            
            // 耳朵 - 从顶部偷偷露出
            HStack(spacing: 25) {
                // 左耳朵
                ZStack {
                    Ellipse()
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color(red: 1.0, green: 0.7, blue: 0.4),
                                    Color(red: 0.9, green: 0.5, blue: 0.2)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 12, height: 16)
                    
                    // 内耳
                    Ellipse()
                        .fill(Color.pink.opacity(0.6))
                        .frame(width: 5, height: 7)
                        .offset(y: 1)
                }
                .rotationEffect(.degrees(-8 + earWiggle))
                .shadow(color: .black.opacity(0.1), radius: 2, x: 1, y: 1)
                
                // 右耳朵
                ZStack {
                    Ellipse()
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color(red: 1.0, green: 0.7, blue: 0.4),
                                    Color(red: 0.9, green: 0.5, blue: 0.2)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 12, height: 16)
                    
                    // 内耳
                    Ellipse()
                        .fill(Color.pink.opacity(0.6))
                        .frame(width: 5, height: 7)
                        .offset(y: 1)
                }
                .rotationEffect(.degrees(8 - earWiggle))
                .shadow(color: .black.opacity(0.1), radius: 2, x: 1, y: 1)
            }
            .offset(y: -15) // 耳朵位置
            
            // 眼睛 - 偷偷看的效果
            HStack(spacing: 14) {
                // 左眼
                PeekingEyeView(isLeft: true, blinkPhase: blinkPhase)
                
                // 右眼
                PeekingEyeView(isLeft: false, blinkPhase: blinkPhase)
            }
            .offset(y: peekOffset)
        }
        .onAppear {
            startPeekingAnimation()
        }
    }
    
    private func startPeekingAnimation() {
        // 耳朵轻微摆动
        withAnimation(.easeInOut(duration: 3.0).repeatForever(autoreverses: true)) {
            earWiggle = 3.0
        }
        
        // 偷看的上下浮动
        withAnimation(.easeInOut(duration: 4.0).repeatForever(autoreverses: true)) {
            peekOffset = -2
        }
        
        // 眨眼动画
        Timer.scheduledTimer(withTimeInterval: Double.random(in: 2...4), repeats: true) { _ in
            withAnimation(.easeInOut(duration: 0.15)) {
                blinkPhase = 1.0
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                withAnimation(.easeInOut(duration: 0.15)) {
                    blinkPhase = 0.0
                }
            }
        }
    }
}

/// 偷看状态的眼睛组件
struct PeekingEyeView: View {
    let isLeft: Bool
    let blinkPhase: Double
    @State private var pupilOffset: CGSize = .zero
    @State private var sparkleOpacity: Double = 0.0
    
    var body: some View {
        ZStack {
            // 眼白
            Ellipse()
                .fill(Color.white)
                .frame(width: 10, height: blinkPhase > 0.5 ? 2 : 10)
                .overlay(
                    Ellipse()
                        .stroke(Color.black.opacity(0.1), lineWidth: 0.3)
                )
            
            // 瞳孔和虹膜
            if blinkPhase < 0.5 {
                ZStack {
                    // 虹膜
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [Color.black, Color.gray.opacity(0.8)],
                                center: .center,
                                startRadius: 0.5,
                                endRadius: 3
                            )
                        )
                        .frame(width: 6, height: 6)
                        .offset(pupilOffset)
                    
                    // 高光
                    Circle()
                        .fill(Color.white)
                        .frame(width: 2, height: 2)
                        .offset(x: -1, y: -1)
                        .offset(pupilOffset)
                        .opacity(sparkleOpacity)
                }
            }
        }
        .onAppear {
            startEyeAnimation()
        }
    }
    
    private func startEyeAnimation() {
        // 眼珠轻微移动（好奇地看来看去）
        Timer.scheduledTimer(withTimeInterval: Double.random(in: 1.5...3.0), repeats: true) { _ in
            withAnimation(.easeInOut(duration: 0.8)) {
                pupilOffset = CGSize(
                    width: Double.random(in: -1.5...1.5),
                    height: Double.random(in: -1.0...1.0)
                )
            }
        }
        
        // 高光闪烁
        withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
            sparkleOpacity = 1.0
        }
    }
}

/// 偷看状态的耳朵
struct PeekingEar: View {
    let isLeft: Bool
    let animationPhase: Double
    
    var body: some View {
        ZStack {
            // 外耳
            Ellipse()
                .fill(
                    LinearGradient(
                        colors: [
                            Color(red: 1.0, green: 0.7, blue: 0.4),
                            Color(red: 0.9, green: 0.5, blue: 0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 12, height: 16)
            
            // 内耳
            Ellipse()
                .fill(Color.pink.opacity(0.6))
                .frame(width: 5, height: 7)
                .offset(y: 1)
        }
        .rotationEffect(.degrees(
            isLeft ? (-8 + sin(animationPhase * 2) * 3) :
                     (8 + sin(animationPhase * 2) * -3)
        ))
        .shadow(color: .black.opacity(0.1), radius: 2, x: 1, y: 1)
    }
}

/// 偷看状态的眼睛
struct PeekingEye: View {
    let isLeft: Bool
    let blinkPhase: Double
    @State private var pupilOffset: CGSize = .zero
    @State private var sparkleOpacity: Double = 0.0
    
    var body: some View {
        ZStack {
            // 眼白
            Ellipse()
                .fill(Color.white)
                .frame(width: 10, height: blinkPhase > 0.5 ? 2 : 10)
                .overlay(
                    Ellipse()
                        .stroke(Color.black.opacity(0.1), lineWidth: 0.3)
                )
            
            // 瞳孔和虹膜
            if blinkPhase < 0.5 {
                ZStack {
                    // 虹膜
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [Color.black, Color.gray.opacity(0.8)],
                                center: .center,
                                startRadius: 0.5,
                                endRadius: 3
                            )
                        )
                        .frame(width: 6, height: 6)
                        .offset(pupilOffset)
                    
                    // 高光
                    Circle()
                        .fill(Color.white)
                        .frame(width: 2, height: 2)
                        .offset(x: -1, y: -1)
                        .offset(pupilOffset)
                        .opacity(sparkleOpacity)
                }
            }
        }
        .onAppear {
            startEyeAnimation()
        }
    }
    
    private func startEyeAnimation() {
        // 眼珠轻微移动（好奇地看来看去）
        Timer.scheduledTimer(withTimeInterval: Double.random(in: 1.5...3.0), repeats: true) { _ in
            withAnimation(.easeInOut(duration: 0.8)) {
                pupilOffset = CGSize(
                    width: Double.random(in: -1.5...1.5),
                    height: Double.random(in: -1.0...1.0)
                )
            }
        }
        
        // 高光闪烁
        withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
            sparkleOpacity = 1.0
        }
    }
}