import SwiftUI
import WatchKit

/// 目标追踪视图 - 增强版
struct GoalTrackingView: View {
    @EnvironmentObject var appState: AppState
    @State private var showingAddGoal = false
    @State private var showingGoalDetail = false
    @State private var selectedGoal: Goal?
    

    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 16) {
                    if appState.goalTracker.goals.isEmpty {
                        EmptyGoalsContentView(showingAddGoal: $showingAddGoal)
                    } else {
                        // 小福狸偷看区域
                        VStack(spacing: 8) {
                            FoxPeekingView()
                                .environmentObject(appState)
                                .frame(height: 60)
                            
                            VStack(spacing: 2) {
                                Text("小福狸在偷偷关注你的目标进度哦～")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                                    .multilineTextAlignment(.center)
                                
                                Text("💡 长按目标可进行删除或重置操作")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                                    .opacity(0.8)
                                    .multilineTextAlignment(.center)
                            }
                        }
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.gray.opacity(0.05))
                        )
                        .padding(.horizontal)
                        
                        // 目标列表 - 直接嵌入，不使用嵌套 ScrollView
                        LazyVStack(spacing: 12) {
                            ForEach(Array(appState.goalTracker.goals.enumerated()), id: \.element.id) { index, goal in
                                EnhancedGoalRowView(goal: goal, index: index)
                                    .environmentObject(appState)
                                    .onTapGesture {
                                        selectedGoal = goal
                                    }
                            }
                            
                            // 添加新目标按钮
                            Button {
                                showingAddGoal = true
                            } label: {
                                HStack(spacing: 8) {
                                    Image(systemName: "plus.circle")
                                        .font(.system(size: 16))
                                    Text("添加新目标")
                                        .font(.caption)
                                }
                                .foregroundColor(.blue)
                                .padding(.vertical, 12)
                                .padding(.horizontal, 16)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.blue.opacity(0.1))
                                        .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                                )
                            }
                            .padding(.top, 8)
                            

                        }
                        .padding(.horizontal)
                    }
                }
                .padding(.vertical, 8)
            }
            .navigationTitle("目标追踪")
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showingAddGoal) {
            EnhancedAddGoalView()
                .environmentObject(appState)
        }
        .sheet(item: $selectedGoal) { goal in
            GoalDetailView(goal: goal)
                .environmentObject(appState)
        }

    }
}

/// 空目标状态内容视图 - 优化版
struct EmptyGoalsContentView: View {
    @Binding var showingAddGoal: Bool
    
    var body: some View {
        VStack(spacing: 20) {
            // 完整的小福狸
            FoxCharacterView()
                .environmentObject(AppState())
                .frame(height: 120)
            
            VStack(spacing: 12) {
                Text("🎯 还没有目标呢！")
                    .font(.headline)
                    .fontWeight(.medium)
                
                Text("设定一个购买目标，让工作更有动力！\n小福狸会陪你一起努力攒钱～")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineSpacing(2)
            }
            
            Button {
                showingAddGoal = true
            } label: {
                HStack(spacing: 8) {
                    Image(systemName: "target")
                    Text("添加第一个目标")
                }
                .fontWeight(.medium)
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.regular)
        }
        .padding(.horizontal, 20)
    }
}

/// 空目标状态视图 - 保持向后兼容
struct EmptyGoalsView: View {
    @Binding var showingAddGoal: Bool
    
    var body: some View {
        EmptyGoalsContentView(showingAddGoal: $showingAddGoal)
    }
}

// 移除 EnhancedGoalListView，功能已集成到 GoalTrackingView 中

/// 增强版目标行视图
struct EnhancedGoalRowView: View {
    let goal: Goal
    let index: Int
    @EnvironmentObject var appState: AppState
    @State private var isPressed = false
    @State private var showDeleteAlert = false {
        didSet {
            print("UI: showDeleteAlert 状态变化: \(oldValue) -> \(showDeleteAlert)")
        }
    }
    @State private var showActionSheet = false {
        didSet {
            print("UI: showActionSheet 状态变化: \(oldValue) -> \(showActionSheet)")
        }
    }
    @State private var alertTrigger = UUID() {
        didSet {
            print("UI: alertTrigger 更新: \(alertTrigger)")
        }
    }
    
    var body: some View {
        VStack(spacing: 12) {
            // 目标头部信息
            HStack(spacing: 12) {
                // 图标和优先级指示器
                ZStack {
                    Circle()
                        .fill(priorityColor.opacity(0.15))
                        .frame(width: 40, height: 40)
                    
                    Image(systemName: goal.displayIcon)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(priorityColor)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(goal.name)
                            .font(.headline)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                            .lineLimit(1)
                        
                        Spacer()
                        
                        Text("\(goal.progressPercentage)%")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(progressColor)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(
                                Capsule()
                                    .fill(progressColor.opacity(0.15))
                            )
                    }
                    
                    Text(goal.category.displayName)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            
            // 进度条
            VStack(spacing: 6) {
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        // 背景条
                        RoundedRectangle(cornerRadius: 6)
                            .fill(Color.gray.opacity(0.2))
                            .frame(height: 8)
                        
                        // 进度条
                        RoundedRectangle(cornerRadius: 6)
                            .fill(
                                LinearGradient(
                                    colors: progressGradient,
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .frame(width: geometry.size.width * goal.progress, height: 8)
                            .animation(.easeInOut(duration: 0.5), value: goal.progress)
                    }
                }
                .frame(height: 8)
                
                // 金额信息
                HStack {
                    Text(goal.formattedCurrentAmount)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    if goal.isCompleted {
                        HStack(spacing: 4) {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("已完成！")
                                .fontWeight(.medium)
                        }
                        .font(.caption)
                        .foregroundColor(.green)
                    } else {
                        Text("还需 \(goal.formattedRemainingAmount)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.background)
                .shadow(
                    color: .primary.opacity(isPressed ? 0.1 : 0.05),
                    radius: isPressed ? 8 : 4,
                    x: 0,
                    y: isPressed ? 4 : 2
                )
        )
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onLongPressGesture(minimumDuration: 0.5) {
            // 长按显示操作菜单
            print("UI: 长按手势触发，显示操作菜单")
            
            // 确保在主线程执行UI更新
            DispatchQueue.main.async {
                // 重置所有状态，确保干净的开始
                showDeleteAlert = false
                showActionSheet = false
                alertTrigger = UUID()
                
                // 添加触觉反馈
                WKInterfaceDevice.current().play(.click)
                
                showActionSheet = true
                print("UI: 操作菜单已设置显示")
            }
        } onPressingChanged: { pressing in
            DispatchQueue.main.async {
                isPressed = pressing
                if pressing {
                    print("UI: 开始长按")
                    // 开始长按时的轻微触觉反馈
                    WKInterfaceDevice.current().play(.start)
                    
                    // 重置状态，准备新的操作
                    showDeleteAlert = false
                } else {
                    print("UI: 结束长按")
                }
            }
        }
        .confirmationDialog("目标操作", isPresented: $showActionSheet, titleVisibility: .visible) {
            Button("重置进度") {
                print("UI: 用户点击重置进度，目标索引: \(index), 目标名称: \(goal.name)")
                
                // 确保在主线程执行UI更新和操作
                DispatchQueue.main.async {
                    // 添加触觉反馈
                    WKInterfaceDevice.current().play(.notification)
                    
                    // 执行重置操作
                    appState.resetGoal(at: index)
                    
                    print("UI: 重置操作完成")
                }
            }
            
            Button("删除目标", role: .destructive) {
                print("UI: 用户点击删除目标，目标索引: \(index), 目标名称: \(goal.name)")
                
                // 先确保状态是干净的
                showDeleteAlert = false
                
                // 延迟显示删除确认对话框，避免对话框冲突
                // watchOS需要更长的延迟确保对话框切换顺畅
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    print("UI: 准备显示删除确认对话框")
                    
                    // 强制触发对话框显示
                    alertTrigger = UUID()
                    showDeleteAlert = true
                    
                    print("UI: 删除确认对话框已设置显示")
                    
                    // 验证状态变化
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        print("UI: 验证 showDeleteAlert 状态: \(showDeleteAlert)")
                    }
                }
            }
            
            Button("取消", role: .cancel) { 
                print("UI: 用户取消操作")
            }
        } message: {
            Text("对目标 \"\(goal.name)\" 执行什么操作？")
        }
        .alert("确认删除", isPresented: Binding(
            get: { 
                print("UI: alert binding get - showDeleteAlert: \(showDeleteAlert)")
                return showDeleteAlert 
            },
            set: { newValue in
                print("UI: alert binding set - \(showDeleteAlert) -> \(newValue)")
                showDeleteAlert = newValue
            }
        )) {
            Button("取消", role: .cancel) { 
                print("UI: 用户取消删除")
                // 重置状态
                showDeleteAlert = false
            }
            Button("删除", role: .destructive) {
                print("UI: 用户确认删除，目标索引: \(index), 目标名称: \(goal.name)")
                
                // 确保在主线程执行删除操作
                DispatchQueue.main.async {
                    // 添加触觉反馈
                    WKInterfaceDevice.current().play(.failure)
                    
                    // 执行删除操作
                    appState.removeGoal(at: index)
                    
                    // 重置状态
                    showDeleteAlert = false
                    
                    print("UI: 删除操作完成")
                }
            }
        } message: {
            Text("确定要删除目标 \"\(goal.name)\" 吗？此操作无法撤销。")
        }
        .onChange(of: showDeleteAlert) { _, newValue in
            print("UI: onChange - showDeleteAlert 变为 \(newValue)")
        }
        .onChange(of: showActionSheet) { _, newValue in
            print("UI: onChange - showActionSheet 变为 \(newValue)")
        }
        .onAppear {
            // 确保视图出现时状态是干净的
            showDeleteAlert = false
            showActionSheet = false
            alertTrigger = UUID()
            print("UI: EnhancedGoalRowView 出现 - 目标: \(goal.name), 当前金额: \(goal.currentAmount), 进度: \(goal.progressPercentage)%, 最后更新: \(goal.lastUpdated)")
        }

    }
    
    private var priorityColor: Color {
        switch goal.priority {
        case .urgent: return .red
        case .high: return .orange
        case .medium: return .blue
        case .low: return .gray
        }
    }
    
    private var progressColor: Color {
        if goal.isCompleted { return .green }
        if goal.progress >= 0.75 { return .blue }
        if goal.progress >= 0.5 { return .orange }
        return .gray
    }
    
    private var progressGradient: [Color] {
        if goal.isCompleted {
            return [.green.opacity(0.8), .green]
        } else if goal.progress >= 0.75 {
            return [.blue.opacity(0.8), .blue]
        } else if goal.progress >= 0.5 {
            return [.orange.opacity(0.8), .orange]
        } else {
            return [.gray.opacity(0.6), .gray]
        }
    }
}

/// 增强版添加目标视图
struct EnhancedAddGoalView: View {
    @EnvironmentObject var appState: AppState
    @Environment(\.dismiss) private var dismiss
    
    @State private var goalName = ""
    @State private var goalAmount = ""
    @State private var selectedCategory: GoalCategory = .other
    @State private var selectedPriority: GoalPriority = .medium
    @State private var goalDescription = ""
    @State private var hasTargetDate = false
    @State private var targetDate = Date().addingTimeInterval(30 * 24 * 3600) // 30天后
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 基本信息
                    GoalBasicInfoSection(
                        goalName: $goalName,
                        goalAmount: $goalAmount,
                        goalDescription: $goalDescription
                    )
                    
                    // 分类选择
                    GoalCategorySection(selectedCategory: $selectedCategory)
                    
                    // 优先级选择
                    GoalPrioritySection(selectedPriority: $selectedPriority)
                    
                    // 截止日期
                    GoalDateSection(hasTargetDate: $hasTargetDate, targetDate: $targetDate)
                    
                    // 预设目标
                    PresetGoalsSection(goalName: $goalName, goalAmount: $goalAmount, selectedCategory: $selectedCategory)
                }
                .padding()
            }
            .navigationTitle("添加目标")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("保存") {
                        saveGoal()
                    }
                    .disabled(goalName.isEmpty || goalAmount.isEmpty)
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    private func saveGoal() {
        guard let amount = Double(goalAmount), amount > 0 else { return }
        
        let newGoal = Goal(
            name: goalName,
            targetAmount: amount,
            priority: selectedPriority,
            category: selectedCategory,
            description: goalDescription.isEmpty ? nil : goalDescription,
            targetDate: hasTargetDate ? targetDate : nil
        )
        
        appState.goalTracker.addGoal(newGoal)
        dismiss()
    }
}

/// 目标基本信息输入
struct GoalBasicInfoSection: View {
    @Binding var goalName: String
    @Binding var goalAmount: String
    @Binding var goalDescription: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("基本信息")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 16) {
                TextField("目标名称", text: $goalName)
                
                TextField("目标金额", text: $goalAmount)
                
                TextField("描述（可选）", text: $goalDescription, axis: .vertical)
                    .lineLimit(2...4)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.05))
        )
    }
}

/// 目标分类选择
struct GoalCategorySection: View {
    @Binding var selectedCategory: GoalCategory
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("分类")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                ForEach(GoalCategory.allCases, id: \.self) { category in
                    CategoryButton(
                        category: category,
                        isSelected: selectedCategory == category
                    ) {
                        selectedCategory = category
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.05))
        )
    }
}

/// 分类按钮
struct CategoryButton: View {
    let category: GoalCategory
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 6) {
                Image(systemName: category.icon)
                    .font(.system(size: 16))
                    .foregroundColor(isSelected ? .white : .primary)
                
                Text(category.displayName)
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : .primary)
                    .lineLimit(1)
            }
            .frame(height: 50)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? Color.blue : Color.gray.opacity(0.1))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// 优先级选择
struct GoalPrioritySection: View {
    @Binding var selectedPriority: GoalPriority
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("优先级")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 8) {
                ForEach(GoalPriority.allCases, id: \.self) { priority in
                    PriorityButton(
                        priority: priority,
                        isSelected: selectedPriority == priority
                    ) {
                        selectedPriority = priority
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.05))
        )
    }
}

/// 优先级按钮
struct PriorityButton: View {
    let priority: GoalPriority
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(priority.displayName)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : priorityColor)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    Capsule()
                        .fill(isSelected ? priorityColor : priorityColor.opacity(0.1))
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var priorityColor: Color {
        switch priority {
        case .urgent: return .red
        case .high: return .orange
        case .medium: return .blue
        case .low: return .gray
        }
    }
}

/// 截止日期设置
struct GoalDateSection: View {
    @Binding var hasTargetDate: Bool
    @Binding var targetDate: Date
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("截止日期")
                .font(.headline)
                .fontWeight(.semibold)
            
            Toggle("设置截止日期", isOn: $hasTargetDate)
                .toggleStyle(SwitchToggleStyle())
            
            if hasTargetDate {
                DatePicker(
                    "目标日期",
                    selection: $targetDate,
                    in: Date()...,
                    displayedComponents: .date
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.05))
        )
    }
}

/// 预设目标选择
struct PresetGoalsSection: View {
    @Binding var goalName: String
    @Binding var goalAmount: String
    @Binding var selectedCategory: GoalCategory
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("快速选择")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                ForEach(Array(Goal.defaultGoals.enumerated()), id: \.offset) { _, defaultGoal in
                    Button {
                        goalName = defaultGoal.name
                        goalAmount = String(Int(defaultGoal.suggestedAmount))
                        selectedCategory = defaultGoal.category
                    } label: {
                        HStack(spacing: 8) {
                            Image(systemName: defaultGoal.category.icon)
                                .font(.caption)
                                .foregroundColor(.blue)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text(defaultGoal.name)
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .lineLimit(1)
                                
                                Text("¥\(Int(defaultGoal.suggestedAmount))")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.gray.opacity(0.1))
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.05))
        )
    }
}

/// 目标详情视图
struct GoalDetailView: View {
    let goal: Goal
    @EnvironmentObject var appState: AppState
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 目标概览
                    GoalOverviewCard(goal: goal)
                    
                    // 进度详情
                    GoalProgressCard(goal: goal)
                    
                    // 统计信息
                    GoalStatsCard(goal: goal)
                }
                .padding()
            }
            .navigationTitle(goal.name)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Button("完成") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
}

/// 目标概览卡片
struct GoalOverviewCard: View {
    let goal: Goal
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                ZStack {
                    Circle()
                        .fill(priorityColor.opacity(0.15))
                        .frame(width: 50, height: 50)
                    
                    Image(systemName: goal.displayIcon)
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(priorityColor)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(goal.name)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text(goal.category.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            if let description = goal.description {
                Text(description)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.background)
                .shadow(color: .primary.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
    
    private var priorityColor: Color {
        switch goal.priority {
        case .urgent: return .red
        case .high: return .orange
        case .medium: return .blue
        case .low: return .gray
        }
    }
}

/// 目标进度卡片
struct GoalProgressCard: View {
    let goal: Goal
    
    var body: some View {
        VStack(spacing: 16) {
            Text("进度详情")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                HStack {
                    Text("当前金额")
                    Spacer()
                    Text(goal.formattedCurrentAmount)
                        .fontWeight(.semibold)
                }
                
                HStack {
                    Text("目标金额")
                    Spacer()
                    Text(goal.formattedTargetAmount)
                        .fontWeight(.semibold)
                }
                
                HStack {
                    Text("剩余金额")
                    Spacer()
                    Text(goal.formattedRemainingAmount)
                        .fontWeight(.semibold)
                        .foregroundColor(goal.isCompleted ? .green : .primary)
                }
            }
            .font(.body)
            
            // 进度条
            VStack(spacing: 8) {
                HStack {
                    Text("完成进度")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text("\(goal.progressPercentage)%")
                        .font(.caption)
                        .fontWeight(.semibold)
                }
                
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.gray.opacity(0.2))
                            .frame(height: 8)
                        
                        RoundedRectangle(cornerRadius: 4)
                            .fill(
                                LinearGradient(
                                    colors: goal.isCompleted ? [.green.opacity(0.8), .green] : [.blue.opacity(0.8), .blue],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .frame(width: geometry.size.width * goal.progress, height: 8)
                    }
                }
                .frame(height: 8)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.background)
                .shadow(color: .primary.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
}

/// 目标统计卡片
struct GoalStatsCard: View {
    let goal: Goal
    
    var body: some View {
        VStack(spacing: 16) {
            Text("统计信息")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                HStack {
                    Text("创建时间")
                    Spacer()
                    Text(DateFormatter.shortDate.string(from: goal.createdAt))
                }
                
                HStack {
                    Text("最后更新")
                    Spacer()
                    Text(DateFormatter.shortDate.string(from: goal.lastUpdated))
                }
                
                if let targetDate = goal.targetDate {
                    HStack {
                        Text("目标日期")
                        Spacer()
                        Text(DateFormatter.shortDate.string(from: targetDate))
                            .foregroundColor(goal.isOverdue ? .red : .primary)
                    }
                }
                
                HStack {
                    Text("优先级")
                    Spacer()
                    Text(goal.priority.displayName)
                        .fontWeight(.medium)
                        .foregroundColor(priorityColor)
                }
            }
            .font(.body)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.background)
                .shadow(color: .primary.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
    
    private var priorityColor: Color {
        switch goal.priority {
        case .urgent: return .red
        case .high: return .orange
        case .medium: return .blue
        case .low: return .gray
        }
    }
}

// MARK: - Extensions

extension DateFormatter {
    static let shortDate: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .none
        return formatter
    }()
} 