//
//  OnboardingViews.swift
//  xzjlg-iwatch Watch App
//
//  Created by Assistant on 2025/1/20.
//

import SwiftUI

/// 主要引导容器视图
struct OnboardingContainerView: View {
    @ObservedObject var onboardingManager = OnboardingManager.shared
    @ObservedObject var userExperience = UserExperienceManager.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景渐变
                LinearGradient(
                    colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // 进度指示器
                    OnboardingProgressView()
                        .padding(.top, 8)
                    
                    // 当前步骤内容
                    TabView(selection: $onboardingManager.currentStepIndex) {
                        ForEach(Array(onboardingManager.allSteps.enumerated()), id: \.offset) { index, step in
                            OnboardingStepView(step: step)
                                .tag(index)
                        }
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                    .animation(.easeInOut(duration: userExperience.getAnimationDuration()), value: onboardingManager.currentStepIndex)
                    
                    // 导航按钮
                    OnboardingNavigationView()
                        .padding(.bottom, 8)
                }
            }
        }
        .navigationBarHidden(true)
    }
}

/// 引导进度视图
struct OnboardingProgressView: View {
    @ObservedObject var onboardingManager = OnboardingManager.shared
    
    var body: some View {
        VStack(spacing: 4) {
            // 步骤指示器
            HStack(spacing: 4) {
                ForEach(Array(onboardingManager.allSteps.enumerated()), id: \.offset) { index, step in
                    Circle()
                        .fill(index <= onboardingManager.currentStepIndex ? step.iconColor : Color.gray.opacity(0.3))
                        .frame(width: 6, height: 6)
                }
            }
            
            // 进度条
            ProgressView(value: onboardingManager.getProgressPercentage())
                .progressViewStyle(LinearProgressViewStyle(tint: onboardingManager.allSteps[onboardingManager.currentStepIndex].iconColor))
                .frame(height: 2)
        }
        .padding(.horizontal, 16)
    }
}

/// 单个引导步骤视图
struct OnboardingStepView: View {
    let step: OnboardingStep
    @ObservedObject var onboardingManager = OnboardingManager.shared
    @ObservedObject var userExperience = UserExperienceManager.shared
    @State private var animateIcon = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 12) {
                // 图标
                Image(systemName: step.iconName)
                    .font(.system(size: 40, weight: .bold))
                    .foregroundColor(step.iconColor)
                    .scaleEffect(animateIcon ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: animateIcon)
                    .onAppear {
                        animateIcon = true
                    }
                
                // 标题
                Text(step.title)
                    .font(.title2)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                
                // 描述
                Text(step.description)
                    .font(.caption)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                
                // 提示信息
                LazyVStack(spacing: 8) {
                    ForEach(onboardingManager.getCurrentStepTips()) { tip in
                        OnboardingTipView(tip: tip)
                    }
                }
                .padding(.top, 8)
                
                // 特殊步骤内容
                switch step {
                case .salarySettings:
                    QuickSalarySetupView()
                case .goalSetup:
                    QuickGoalSetupView()
                case .notifications:
                    NotificationPermissionView()
                case .character:
                    CharacterPreviewView()
                default:
                    EmptyView()
                }
                
                Spacer(minLength: 20)
            }
            .padding(.horizontal, 16)
        }
        .onAppear {
            // 步骤切换时触发触觉反馈
            userExperience.triggerHapticFeedback(.selection)
        }
    }
}

/// 引导提示信息视图
struct OnboardingTipView: View {
    let tip: OnboardingTip
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: tip.iconName)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(tip.iconColor)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(tip.title)
                    .font(.caption)
                    .fontWeight(.medium)
                
                Text(tip.description)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.gray.opacity(0.1))
        )
    }
}

/// 导航按钮视图
struct OnboardingNavigationView: View {
    @ObservedObject var onboardingManager = OnboardingManager.shared
    @ObservedObject var userExperience = UserExperienceManager.shared
    
    private var isLastStep: Bool {
        return onboardingManager.currentStepIndex == onboardingManager.allSteps.count - 1
    }
    
    private var buttonText: String {
        return isLastStep ? "开始使用" : "下一步"
    }
    
    var body: some View {
        HStack(spacing: 12) {
            // 上一步按钮
            if onboardingManager.currentStepIndex > 0 && !isLastStep {
                Button("上一步") {
                    userExperience.triggerHapticFeedback(.selection)
                    onboardingManager.previousStep()
                }
                .font(.caption)
                .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 跳过按钮 - 只在非最后一步显示
            if !isLastStep {
                Button("跳过") {
                    userExperience.triggerHapticFeedback(.warning)
                    onboardingManager.skipOnboarding()
                }
                .font(.caption)
                .foregroundColor(.secondary)
            }
            
            // 主要操作按钮
            Button(buttonText) {
                userExperience.triggerHapticFeedback(.success)
                if isLastStep {
                    // 在最后一步直接完成引导
                    onboardingManager.completeOnboarding()
                } else {
                    onboardingManager.nextStep()
                }
            }
            .font(.caption)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .padding(.horizontal, isLastStep ? 24 : 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isLastStep ? Color.green : onboardingManager.allSteps[onboardingManager.currentStepIndex].iconColor)
            )
        }
        .padding(.horizontal, 16)
    }
}

/// 快速薪资设置视图
struct QuickSalarySetupView: View {
    @State private var monthlySalary: String = ""
    @State private var workingDays: String = "22"
    @State private var workingHours: String = "8"
    
    var body: some View {
        VStack(spacing: 8) {
            Text("快速设置")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            VStack(spacing: 6) {
                HStack {
                    Text("月薪")
                        .font(.caption2)
                    Spacer()
                    TextField("输入月薪", text: $monthlySalary)
                        .textFieldStyle(.plain)
                        .frame(width: 80)
                }
                
                HStack {
                    Text("工作日")
                        .font(.caption2)
                    Spacer()
                    TextField("22", text: $workingDays)
                        .textFieldStyle(.plain)
                        .frame(width: 50)
                }
                
                HStack {
                    Text("工作时长")
                        .font(.caption2)
                    Spacer()
                    TextField("8", text: $workingHours)
                        .textFieldStyle(.plain)
                        .frame(width: 50)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.gray.opacity(0.1))
            )
        }
    }
}

/// 快速目标设置视图
struct QuickGoalSetupView: View {
    @State private var goalName: String = ""
    @State private var goalAmount: String = ""
    
    var body: some View {
        VStack(spacing: 8) {
            Text("添加第一个目标")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            VStack(spacing: 6) {
                TextField("目标名称", text: $goalName)
                    .textFieldStyle(.plain)
                
                TextField("目标金额", text: $goalAmount)
                    .textFieldStyle(.plain)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.gray.opacity(0.1))
            )
        }
    }
}

/// 通知权限视图
struct NotificationPermissionView: View {
    @State private var permissionGranted = false
    
    var body: some View {
        VStack(spacing: 8) {
            Text("开启通知")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            Button(action: requestNotificationPermission) {
                HStack {
                    Image(systemName: permissionGranted ? "checkmark.circle.fill" : "bell.fill")
                        .foregroundColor(permissionGranted ? .green : .orange)
                    Text(permissionGranted ? "通知已开启" : "开启通知")
                        .font(.caption)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.1))
                )
            }
        }
    }
    
    private func requestNotificationPermission() {
        // 请求通知权限的逻辑
        UserExperienceManager.shared.triggerHapticFeedback(.success)
        permissionGranted = true
    }
}

/// 角色预览视图
struct CharacterPreviewView: View {
    @State private var showCharacter = false
    
    var body: some View {
        VStack(spacing: 8) {
            Text("小福狸预览")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            Button(action: {
                UserExperienceManager.shared.triggerHapticFeedback(.success)
                showCharacter.toggle()
            }) {
                ZStack {
                    Circle()
                        .fill(LinearGradient(colors: [.orange, .red], startPoint: .topLeading, endPoint: .bottomTrailing))
                        .frame(width: 60, height: 60)
                    
                    if showCharacter {
                        VStack(spacing: 2) {
                            HStack(spacing: 4) {
                                Circle().fill(Color.black).frame(width: 4, height: 4)
                                Circle().fill(Color.black).frame(width: 4, height: 4)
                            }
                            
                            RoundedRectangle(cornerRadius: 2)
                                .fill(Color.black)
                                .frame(width: 8, height: 2)
                        }
                    } else {
                        Text("?")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    }
                }
            }
            
            Text(showCharacter ? "你好！我是小福狸 🦊" : "点击查看小福狸")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - Preview

struct OnboardingViews_Previews: PreviewProvider {
    static var previews: some View {
        OnboardingContainerView()
    }
} 