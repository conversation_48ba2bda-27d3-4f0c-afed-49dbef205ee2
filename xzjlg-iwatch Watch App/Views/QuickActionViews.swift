import SwiftUI

/// 快速操作按钮区域
struct QuickActionButtons: View {
    @EnvironmentObject var appState: AppState
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        VStack(spacing: 12) {
            Text("快速操作")
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            HStack(spacing: 12) {
                // 暂停/继续按钮
                QuickActionButton(
                    icon: appState.calculationState == .calculating ? "pause.fill" : "play.fill",
                    title: appState.calculationState == .calculating ? "暂停" : "继续",
                    color: appState.calculationState == .calculating ? .orange : .green
                ) {
                    toggleCalculation()
                }
                
                // 刷新按钮
                QuickActionButton(
                    icon: "arrow.clockwise",
                    title: "刷新",
                    color: .blue
                ) {
                    appState.refreshState()
                    showAlert("状态已刷新")
                }
                
                // 设置按钮
                QuickActionButton(
                    icon: "gear",
                    title: "设置",
                    color: .gray
                ) {
                    // 跳转到设置页面
                    appState.switchToSettingsTab?()
                }
            }
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定") { }
        } message: {
            Text(alertMessage)
        }
    }
    
    private func toggleCalculation() {
        switch appState.calculationState {
        case .calculating:
            appState.pauseCalculation()
            showAlert("计算已暂停")
        case .paused, .stopped:
            appState.resumeCalculation()
            showAlert("计算已继续")
        case .error:
            appState.startCalculation()
            showAlert("重新开始计算")
        }
    }
    
    private func showAlert(_ message: String) {
        alertMessage = message
        showingAlert = true
    }
}

/// 快速操作按钮组件
struct QuickActionButton: View {
    let icon: String
    let title: String
    let color: Color
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                isPressed = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                    isPressed = false
                }
            }
            action()
        }) {
            VStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption2)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.1))
                    .shadow(color: isPressed ? .clear : .black.opacity(0.1), radius: 2, x: 0, y: 1)
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
    }
} 