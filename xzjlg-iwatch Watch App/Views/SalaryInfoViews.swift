import SwiftUI

/// 工资信息视图
struct SalaryInfoView: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        VStack(spacing: 8) {
            // 当前收入
            VStack(spacing: 2) {
                Text("今日已赚")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                HStack {
                    Text("¥")
                        .font(.title2)
                        .fontWeight(.medium)
                    
                    Text(String(format: "%.0f", appState.dailyEarnings))
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                        .animation(.easeInOut(duration: 0.3), value: appState.dailyEarnings)
                }
            }
            
            // 进度条
            VStack(spacing: 4) {
                ProgressView(value: appState.currentProgress, total: 1.0)
                    .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                    .scaleEffect(x: 1, y: 2, anchor: .center)
                    .animation(.easeInOut(duration: 0.5), value: appState.currentProgress)
                
                Text(String(format: "%.1f%%", appState.currentProgress * 100))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // 工作状态
            HStack {
                Circle()
                    .fill(appState.isCalculating ? Color.green : Color.gray)
                    .frame(width: 6, height: 6)
                
                Text(appState.currentWorkScenario.displayName)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                // 通知状态指示器
                if appState.notificationManager.settings.isEnabled {
                    Image(systemName: appState.notificationManager.permissionGranted ? "bell.fill" : "bell.slash")
                        .foregroundColor(appState.notificationManager.permissionGranted ? .blue : .gray)
                        .font(.caption)
                }
                
                if appState.batteryOptimizationLevel != .normal {
                    Image(systemName: "battery.25")
                        .foregroundColor(.orange)
                        .font(.caption)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
}

/// 增强版工资信息卡片
struct SalaryInfoCard: View {
    @EnvironmentObject var appState: AppState
    @State private var displayedEarnings: Double = 0.0
    @State private var animationTrigger: Bool = false
    
    var body: some View {
        VStack(spacing: 16) {
            // 主要收入信息
            VStack(spacing: 8) {
                Text("今日收入")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .textCase(.uppercase)
                    .tracking(1)
                
                HStack(alignment: .bottom, spacing: 4) {
                    Text("¥")
                        .font(.title3)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    AnimatedNumberView(
                        value: appState.dailyEarnings,
                        formatter: NumberFormatter.currency
                    )
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                }
                .scaleEffect(animationTrigger ? 1.05 : 1.0)
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: animationTrigger)
            }
            
            // 渐变进度条
            GradientProgressBar(
                progress: appState.currentProgress,
                colors: getProgressColors()
            )
            
            // 详细信息网格
            VStack(spacing: 8) {
                HStack {
                    InfoItem(
                        title: "进度",
                        value: String(format: "%.1f%%", appState.currentProgress * 100),
                        icon: "chart.line.uptrend.xyaxis"
                    )
                    
                    Spacer()
                    
                    InfoItem(
                        title: "状态",
                        value: appState.calculationState.displayName,
                        icon: getStatusIcon()
                    )
                }
                
                HStack {
                    InfoItem(
                        title: "目标",
                        value: String(format: "¥%.0f", appState.getDailyTargetSalary()),
                        icon: "target"
                    )
                    
                    Spacer()
                    
                    InfoItem(
                        title: "剩余",
                        value: String(format: "¥%.0f", max(0, appState.getDailyTargetSalary() - appState.dailyEarnings)),
                        icon: "hourglass"
                    )
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.gray.opacity(0.1))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .onChange(of: appState.dailyEarnings) { oldValue, newValue in
            if newValue > oldValue {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                    animationTrigger.toggle()
                }
            }
        }
    }
    
    private func getProgressColors() -> [Color] {
        let progress = appState.currentProgress
        
        if progress >= 1.0 {
            return [Color.green, Color.green.opacity(0.7)]
        } else if progress >= 0.75 {
            return [Color.orange, Color.yellow]
        } else if progress >= 0.5 {
            return [Color.blue, Color.cyan]
        } else {
            return [Color.purple, Color.pink]
        }
    }
    
    private func getStatusIcon() -> String {
        switch appState.calculationState {
        case .calculating:
            return "play.circle.fill"
        case .paused:
            return "pause.circle.fill"
        case .stopped:
            return "stop.circle.fill"
        case .error:
            return "exclamationmark.triangle.fill"
        }
    }
}

/// 渐变进度条组件
struct GradientProgressBar: View {
    let progress: Double
    let colors: [Color]
    @State private var animatedProgress: Double = 0.0
    
    var body: some View {
        VStack(spacing: 6) {
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // 背景
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.gray.opacity(0.3))
                        .frame(height: 8)
                    
                    // 进度条
                    RoundedRectangle(cornerRadius: 4)
                        .fill(
                            LinearGradient(
                                colors: colors,
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(width: geometry.size.width * animatedProgress, height: 8)
                        .animation(.easeInOut(duration: 0.5), value: animatedProgress)
                    
                    // 光效
                    if animatedProgress > 0 {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(
                                LinearGradient(
                                    colors: [Color.white.opacity(0.3), Color.clear],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .frame(width: geometry.size.width * animatedProgress * 0.6, height: 6)
                            .offset(x: 1, y: 1)
                    }
                }
            }
            .frame(height: 8)
            
            // 进度标签
            HStack {
                Text("0%")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("100%")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0)) {
                animatedProgress = progress
            }
        }
        .onChange(of: progress) { oldValue, newValue in
            withAnimation(.easeInOut(duration: 0.5)) {
                animatedProgress = newValue
            }
        }
    }
}

/// 信息项组件
struct InfoItem: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 2) {
            HStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Text(title)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .textCase(.uppercase)
            }
            
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.primary)
        }
    }
}

/// 动画数字视图
struct AnimatedNumberView: View {
    let value: Double
    let formatter: NumberFormatter
    @State private var displayValue: Double = 0.0
    
    var body: some View {
        Text(formatter.string(from: NSNumber(value: displayValue)) ?? "0")
            .onAppear {
                animateToValue()
            }
            .onChange(of: value) { oldValue, newValue in
                animateToValue()
            }
    }
    
    private func animateToValue() {
        let startValue = displayValue
        let endValue = value
        let duration: Double = 0.6
        let frameCount = 30
        
        for i in 0...frameCount {
            let delay = Double(i) / Double(frameCount) * duration
            let progress = Double(i) / Double(frameCount)
            let currentValue = startValue + (endValue - startValue) * easeOutCubic(progress)
            
            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                self.displayValue = currentValue
            }
        }
    }
    
    private func easeOutCubic(_ t: Double) -> Double {
        let t1 = t - 1
        return t1 * t1 * t1 + 1
    }
}

extension NumberFormatter {
    static let currency: NumberFormatter = {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.minimumFractionDigits = 0
        formatter.maximumFractionDigits = 0
        return formatter
    }()
} 