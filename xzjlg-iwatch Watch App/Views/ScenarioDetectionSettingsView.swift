//
//  ScenarioDetectionSettingsView.swift
//  xzjlg-iwatch Watch App
//
//  Created by Kiro on 2025/7/26.
//

import SwiftUI

/// 智能场景检测设置界面
struct ScenarioDetectionSettingsView: View {
    @ObservedObject var appState: AppState
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 12) {
                    // 标题和说明
                    headerSection
                    
                    // 自动检测开关
                    autoDetectionSection
                    
                    // 检测设置
                    if appState.scenarioDetector.isAutoDetectionEnabled {
                        detectionSettingsSection
                    }
                    
                    // 当前状态显示
                    currentStatusSection
                    
                    // 手动场景选择
                    manualScenarioSection
                    
                    // 统计信息
                    statisticsSection
                }
                .padding(.horizontal, 8)
            }
            .navigationTitle("智能检测")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarTrailing) {
                    But<PERSON>("完成") {
                        dismiss()
                    }
                    .font(.system(size: 14, weight: .medium))
                }
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 8) {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 24))
                .foregroundColor(.blue)
            
            Text("智能场景识别")
                .font(.system(size: 16, weight: .semibold))
            
            Text("自动识别工作状态，提供个性化体验")
                .font(.system(size: 12))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - Auto Detection Section
    private var autoDetectionSection: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "wand.and.rays")
                    .foregroundColor(.purple)
                Text("自动检测")
                    .font(.system(size: 14, weight: .medium))
                Spacer()
                Toggle("", isOn: Binding(
                    get: { appState.scenarioDetector.isAutoDetectionEnabled },
                    set: { appState.setAutoDetectionEnabled($0) }
                ))
                .labelsHidden()
            }
            
            if appState.scenarioDetector.isAutoDetectionEnabled {
                Text("系统将根据时间、活动等因素自动识别工作场景")
                    .font(.system(size: 11))
                    .foregroundColor(.secondary)
            } else {
                Text("已禁用自动检测，可手动选择场景")
                    .font(.system(size: 11))
                    .foregroundColor(.orange)
            }
        }
        .padding(12)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
    
    // MARK: - Detection Settings Section
    private var detectionSettingsSection: some View {
        VStack(spacing: 12) {
            Text("检测设置")
                .font(.system(size: 14, weight: .medium))
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 8) {
                // 活动检测
                HStack {
                    Image(systemName: "figure.walk")
                        .foregroundColor(.green)
                    Text("活动检测")
                        .font(.system(size: 13))
                    Spacer()
                    Toggle("", isOn: Binding(
                        get: { appState.scenarioDetector.enableActivityBasedDetection },
                        set: { appState.scenarioDetector.enableActivityBasedDetection = $0 }
                    ))
                    .labelsHidden()
                }
                
                // 位置检测（暂时禁用）
                HStack {
                    Image(systemName: "location")
                        .foregroundColor(.blue)
                    Text("位置检测")
                        .font(.system(size: 13))
                    Spacer()
                    Toggle("", isOn: .constant(false))
                        .labelsHidden()
                        .disabled(true)
                }
                
                Text("位置检测功能即将推出")
                    .font(.system(size: 10))
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
        }
        .padding(12)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
    
    // MARK: - Current Status Section
    private var currentStatusSection: some View {
        VStack(spacing: 8) {
            Text("当前状态")
                .font(.system(size: 14, weight: .medium))
                .frame(maxWidth: .infinity, alignment: .leading)
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("场景")
                        .font(.system(size: 11))
                        .foregroundColor(.secondary)
                    Text(appState.scenarioDetector.currentScenario.displayName)
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(.primary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("置信度")
                        .font(.system(size: 11))
                        .foregroundColor(.secondary)
                    Text("\(Int(appState.getCurrentDetectionConfidence() * 100))%")
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(confidenceColor)
                }
            }
            
            // 置信度进度条
            ProgressView(value: appState.getCurrentDetectionConfidence())
                .progressViewStyle(LinearProgressViewStyle(tint: confidenceColor))
                .scaleEffect(y: 0.5)
        }
        .padding(12)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
    
    // MARK: - Manual Scenario Section
    private var manualScenarioSection: some View {
        VStack(spacing: 8) {
            Text("手动选择场景")
                .font(.system(size: 14, weight: .medium))
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // 分成两个部分来避免编译器复杂度问题
            scenarioGridFirstHalf
            scenarioGridSecondHalf
        }
        .padding(12)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
    
    // 场景网格第一部分
    private var scenarioGridFirstHalf: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 8) {
            ForEach(Array(WorkScenario.allCases.prefix(4)), id: \.self) { scenario in
                scenarioButton(for: scenario)
            }
        }
    }
    
    // 场景网格第二部分
    private var scenarioGridSecondHalf: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 8) {
            ForEach(Array(WorkScenario.allCases.suffix(from: 4)), id: \.self) { scenario in
                scenarioButton(for: scenario)
            }
        }
    }
    
    // 单个场景按钮
    private func scenarioButton(for scenario: WorkScenario) -> some View {
        Button(action: {
            appState.manuallySetScenario(scenario)
        }) {
            VStack(spacing: 4) {
                Image(systemName: scenarioIcon(for: scenario))
                    .font(.system(size: 16))
                    .foregroundColor(scenarioColor(for: scenario))
                
                Text(scenario.displayName)
                    .font(.system(size: 10))
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
            }
            .frame(height: 50)
            .frame(maxWidth: .infinity)
            .background(
                appState.scenarioDetector.currentScenario == scenario ?
                Color.blue.opacity(0.2) : Color.gray.opacity(0.1)
            )
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(
                        appState.scenarioDetector.currentScenario == scenario ?
                        Color.blue : Color.clear,
                        lineWidth: 1
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Statistics Section
    private var statisticsSection: some View {
        VStack(spacing: 8) {
            Text("今日统计")
                .font(.system(size: 14, weight: .medium))
                .frame(maxWidth: .infinity, alignment: .leading)
            
            let stats = appState.getScenarioDetectionStats()
            
            if stats.isEmpty {
                Text("暂无统计数据")
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 20)
            } else {
                VStack(spacing: 6) {
                    ForEach(stats.sorted(by: { $0.value > $1.value }), id: \.key) { scenario, duration in
                        HStack {
                            Image(systemName: scenarioIcon(for: scenario))
                                .foregroundColor(scenarioColor(for: scenario))
                                .frame(width: 16)
                            
                            Text(scenario.displayName)
                                .font(.system(size: 12))
                            
                            Spacer()
                            
                            Text(formatDuration(duration))
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
        }
        .padding(12)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
    
    // MARK: - Helper Properties
    private var confidenceColor: Color {
        let confidence = appState.getCurrentDetectionConfidence()
        if confidence >= 0.8 {
            return .green
        } else if confidence >= 0.6 {
            return .orange
        } else {
            return .red
        }
    }
    
    // MARK: - Helper Methods
    private func scenarioIcon(for scenario: WorkScenario) -> String {
        switch scenario {
        case .normalWork:
            return "briefcase"
        case .nightOvertime:
            return "moon"
        case .weekendWithPay, .weekendVoluntary:
            return "calendar.badge.clock"
        case .resting:
            return "bed.double"
        case .lunchBreak:
            return "fork.knife"
        case .deepWork:
            return "brain.head.profile"
        case .meeting:
            return "person.3"
        case .commuting:
            return "car"
        }
    }
    
    private func scenarioColor(for scenario: WorkScenario) -> Color {
        switch scenario {
        case .normalWork:
            return .blue
        case .nightOvertime:
            return .purple
        case .weekendWithPay, .weekendVoluntary:
            return .orange
        case .resting:
            return .green
        case .lunchBreak:
            return .yellow
        case .deepWork:
            return .red
        case .meeting:
            return .indigo
        case .commuting:
            return .gray
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60
        
        if hours > 0 {
            return "\(hours)h\(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}

// MARK: - Preview
struct ScenarioDetectionSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        ScenarioDetectionSettingsView(appState: AppState())
    }
}