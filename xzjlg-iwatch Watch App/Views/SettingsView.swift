import SwiftUI

/// 设置视图
struct SettingsView: View {
    @EnvironmentObject var appState: AppState
    @State private var isEditingMode = false
    
    var body: some View {
        NavigationView {
        Form {
            Section("工资设置") {
                    NavigationLink("月薪设置") {
                        SalarySettingView()
                            .environmentObject(appState)
                }
                
                    NavigationLink("工作日设置") {
                        WorkDaysSettingView()
                            .environmentObject(appState)
                }
                
                    NavigationLink("工作时长设置") {
                        WorkHoursSettingView()
                            .environmentObject(appState)
                }
                    
                    // 显示当前设置摘要
                    VStack(alignment: .leading, spacing: 4) {
                HStack {
                            Text("当前设置：")
                                .font(.caption)
                                .foregroundColor(.secondary)
                    Spacer()
                        }
                        Text("月薪 ¥\(Int(appState.userSettings.monthlySalary))")
                            .font(.caption2)
                        Text("\(appState.userSettings.workDaysPerMonth)天/月, \(appState.userSettings.workHoursPerDay)小时/天")
                            .font(.caption2)
                    }
                    .padding(.vertical, 4)
                }
                
                Section("工作设置") {
                    Toggle("周末工作", isOn: Binding(
                        get: { appState.userSettings.isWeekendWorkEnabled },
                        set: { newValue in
                            var settings = appState.userSettings
                            settings.isWeekendWorkEnabled = newValue
                            appState.updateUserSettings(settings)
                        }
                    ))
                    
                    NavigationLink("加班费率") {
                        OvertimeRateSettingView()
                            .environmentObject(appState)
                    }
                    
                    NavigationLink("工作时间设置") {
                        WorkTimeSettingView()
                            .environmentObject(appState)
                }
            }
            
            Section("通知设置") {
                Toggle("启用通知", isOn: Binding(
                    get: { appState.userSettings.notificationSettings.isEnabled },
                    set: { newValue in
                        var settings = appState.userSettings
                        settings.notificationSettings.isEnabled = newValue
                        appState.updateUserSettings(settings)
                    }
                ))
                
                if appState.userSettings.notificationSettings.isEnabled {
                    Toggle("工资阈值通知", isOn: Binding(
                        get: { appState.userSettings.notificationSettings.salaryThresholdEnabled },
                        set: { newValue in
                            var settings = appState.userSettings
                            settings.notificationSettings.salaryThresholdEnabled = newValue
                            appState.updateUserSettings(settings)
                        }
                    ))
                    
                    Toggle("目标完成通知", isOn: Binding(
                        get: { appState.userSettings.notificationSettings.goalCompletionEnabled },
                        set: { newValue in
                            var settings = appState.userSettings
                            settings.notificationSettings.goalCompletionEnabled = newValue
                            appState.updateUserSettings(settings)
                        }
                    ))
                    
                    Toggle("里程碑通知", isOn: Binding(
                        get: { appState.userSettings.notificationSettings.milestoneEnabled },
                        set: { newValue in
                            var settings = appState.userSettings
                            settings.notificationSettings.milestoneEnabled = newValue
                            appState.updateUserSettings(settings)
                        }
                    ))
                    
                    Toggle("电池优化", isOn: Binding(
                        get: { appState.userSettings.notificationSettings.batteryOptimizationEnabled },
                        set: { newValue in
                            var settings = appState.userSettings
                            settings.notificationSettings.batteryOptimizationEnabled = newValue
                            appState.updateUserSettings(settings)
                        }
                    ))
                        
                        NavigationLink("高级通知设置") {
                            AdvancedNotificationSettingsView()
                                .environmentObject(appState)
                        }
                    }
                }
                
                Section("电池优化") {
                    Toggle("启用电池优化", isOn: Binding(
                        get: { appState.batteryOptimizationManager.isBatteryOptimizationEnabled },
                        set: { newValue in
                            appState.batteryOptimizationManager.isBatteryOptimizationEnabled = newValue
                        }
                    ))
                    
                    if appState.batteryOptimizationManager.isBatteryOptimizationEnabled {
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text("当前状态：")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                Spacer()
                            }
                            Text("电池电量: \(Int(appState.batteryOptimizationManager.currentBatteryLevel * 100))%")
                                .font(.caption2)
                            Text("优化级别: \(appState.batteryOptimizationManager.currentOptimizationLevel.rawValue)")
                                .font(.caption2)
                            Text("时间段: \(appState.batteryOptimizationManager.currentTimeOptimization.displayName)")
                                .font(.caption2)
                            if appState.batteryOptimizationManager.isInIdleMode {
                                Text("状态: 空闲模式")
                                    .font(.caption2)
                                    .foregroundColor(.orange)
                            }
                        }
                        .padding(.vertical, 4)
                        
                        NavigationLink("电池优化详情") {
                            BatteryOptimizationDetailView()
                                .environmentObject(appState)
                        }
                    }
                }
                
                Section("用户体验优化") {
                    NavigationLink("引导流程") {
                        OnboardingContainerView()
                    }
                    
                    HStack {
                        Text("性能等级")
                        Spacer()
                        Text(UserExperienceManager.shared.settings.performanceLevel.displayName)
                            .foregroundColor(.secondary)
                    }
                    
                    Button("触发触觉反馈测试") {
                        UserExperienceManager.shared.triggerHapticFeedback(.milestone)
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            UserExperienceManager.shared.triggerHapticFeedback(.goalComplete)
                        }
                    }
                    
                    Button("开始屏幕适配测试") {
                        TestingManager.shared.startScreenAdaptationTest()
                    }
                    
                    Button("开始稳定性测试") {
                        TestingManager.shared.startStabilityTest()
                    }
                    
                    NavigationLink("查看测试报告") {
                        TestReportsView()
                    }
                }
                
                Section("系统信息") {
                HStack {
                    Text("通知权限")
                    Spacer()
                    Text(appState.notificationManager.permissionGranted ? "已授权" : "未授权")
                        .foregroundColor(appState.notificationManager.permissionGranted ? .green : .red)
                }
                
                    HStack {
                        Text("计算状态")
                        Spacer()
                        Text(appState.isCalculating ? "运行中" : "已停止")
                            .foregroundColor(appState.isCalculating ? .green : .secondary)
                    }
                }
                
                Section("表盘功能") {
                    NavigationLink("复杂功能设置") {
                        ComplicationSettingsView()
                            .environmentObject(appState)
                    }
                    
                    NavigationLink("复杂功能预览") {
                        ComplicationPreviewView()
                            .environmentObject(appState)
                    }
                    
                    NavigationLink("复杂功能测试") {
                        ComplicationTestView()
                            .environmentObject(appState)
                    }
                }
                
                Section("数据管理") {
                    Button("检查数据完整性") {
                        checkDataIntegrity()
                    }
                    
                    Button("修复损坏数据") {
                        repairData()
                    }
                    .foregroundColor(.orange)
                    
                    Button("重置为默认设置") {
                        resetToDefaults()
                    }
                    .foregroundColor(.red)
                    
                    Button("清除所有数据") {
                        clearAllData()
                    }
                    .foregroundColor(.red)
                }
                
                Section("调试工具") {
                    Button("测试通知") {
                        appState.sendTestNotification()
                    }
                    
                    Button("批量保存数据") {
                        saveAllData()
                    }
                }
                
                Section("错误处理") {
                    NavigationLink("错误历史") {
                        ErrorHistoryView()
                    }
                    
                    NavigationLink("系统健康") {
                        SystemHealthDetailView()
                    }
                    
                    Button("清理错误日志") {
                        GlobalErrorHandler.shared.cleanupErrorHistory()
                    }
                    .foregroundColor(.orange)
                    
                    Button("触发测试错误") {
                        triggerTestError()
                    }
                    .foregroundColor(.red)
                    
                    Button("测试错误处理系统") {
                        GlobalErrorHandler.shared.testErrorHandlingSystem()
                    }
                    .foregroundColor(.blue)
                    
                    Button("演示错误恢复") {
                        GlobalErrorHandler.shared.demonstrateErrorRecovery()
                    }
                    .foregroundColor(.green)
                }
            }
            .navigationTitle("设置")
        }
    }
    
    private func resetToDefaults() {
        let defaultSettings = UserSettings.default
        appState.updateUserSettings(defaultSettings)
    }
    
    private func checkDataIntegrity() {
        let isValid = DataPersistenceManager.shared.checkDataIntegrity()
        if isValid {
            print("数据完整性检查通过")
        } else {
            GlobalErrorHandler.shared.handleError(DataPersistenceError.corruptedData("完整性检查失败"))
        }
    }
    
    private func repairData() {
        let success = DataPersistenceManager.shared.repairCorruptedData()
        if success {
            print("数据修复成功")
            // 重新加载数据
            appState.refreshState()
        } else {
            GlobalErrorHandler.shared.handleError(DataPersistenceError.repairFailed("数据修复失败"))
        }
    }
    
    private func clearAllData() {
        DataPersistenceManager.shared.clearAllData()
        
        // 重置应用状态
        appState.updateUserSettings(UserSettings.default)
        appState.goalTracker.clearAllGoals()
        
        print("所有数据已清除")
    }
    
    private func saveAllData() {
        do {
            try DataPersistenceManager.shared.saveAllData(
                userSettings: appState.userSettings,
                goals: appState.goals,
                dailyEarnings: appState.dailyEarnings,
                notificationSettings: appState.userSettings.notificationSettings,
                notificationHistory: appState.notificationManager.getNotificationHistory()
            )
            print("批量保存成功")
        } catch {
            GlobalErrorHandler.shared.handleError(DataPersistenceError.saveOperationFailed("批量保存失败"))
        }
    }
    
    private func triggerTestError() {
        // 随机触发不同类型的测试错误
        let errorTypes: [AppError] = [
            SalaryCalculatorError.calculationFailed("测试计算错误"),
            DataPersistenceError.saveOperationFailed("测试保存错误"),
            NotificationError.scheduleFailure("测试通知错误"),
            SystemError.lowMemory
        ]
        
        let randomError = errorTypes.randomElement()!
        GlobalErrorHandler.shared.handleError(randomError, autoRecover: false)
    }
}

/// 月薪设置视图
struct SalarySettingView: View {
    @EnvironmentObject var appState: AppState
    @State private var selectedSalary: Double
    @State private var customSalaryText: String = ""
    @State private var useCustomInput: Bool = false
    @State private var showingInvalidInput: Bool = false
    
    // 预设的常见月薪选项
    private let salaryOptions: [Double] = [
        3000, 4000, 5000, 6000, 7000, 8000, 9000, 9500, 10000,
        12000, 15000, 18000, 20000, 25000, 30000, 35000, 40000, 50000
    ]
    
    init() {
        self._selectedSalary = State(initialValue: 9500)
    }
    
    var body: some View {
        List {
            Section("输入方式") {
                Picker("输入方式", selection: $useCustomInput) {
                    Text("预设选项").tag(false)
                    Text("自定义输入").tag(true)
                }
                .pickerStyle(.navigationLink)
            }
            
            if useCustomInput {
                Section("自定义金额输入") {
                    VStack(alignment: .leading, spacing: 8) {
                        TextField("输入月薪金额", text: $customSalaryText)
                            .onSubmit {
                                validateAndUpdateCustomSalary()
                            }
                        
                        if showingInvalidInput {
                            Text("请输入有效的金额（1000-999999）")
                                .font(.caption)
                                .foregroundColor(.red)
                        }
                        
                        HStack {
                            Text("范围：¥1,000 - ¥999,999")
                            Spacer()
                        }
                        .font(.caption)
                        .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Button("确认输入") {
                            validateAndUpdateCustomSalary()
                        }
                        .buttonStyle(.borderedProminent)
                        .disabled(customSalaryText.isEmpty)
                        
                        Spacer()
                        
                        Button("清空") {
                            customSalaryText = ""
                            showingInvalidInput = false
                        }
                        .buttonStyle(.bordered)
                    }
                }
            } else {
                Section("选择预设月薪") {
                    Picker("月薪", selection: $selectedSalary) {
                        ForEach(salaryOptions, id: \.self) { salary in
                            Text("¥\(Int(salary))")
                                .tag(salary)
                        }
                    }
                    .pickerStyle(.wheel)
                }
            }
            
            Section("快速调整") {
                HStack {
                    Text("当前设置")
                    Spacer()
                    Text("¥\(Int(selectedSalary))")
                        .foregroundColor(.blue)
                        .fontWeight(.semibold)
                }
                
                VStack(spacing: 8) {
                    HStack {
                        Button("-5000") {
                            adjustSalary(-5000)
                        }
                        .buttonStyle(.bordered)
                        
                        Button("-1000") {
                            adjustSalary(-1000)
                        }
                        .buttonStyle(.bordered)
                        
                        Spacer()
                        
                        Button("+1000") {
                            adjustSalary(1000)
                        }
                        .buttonStyle(.bordered)
                        
                        Button("+5000") {
                            adjustSalary(5000)
                        }
                        .buttonStyle(.bordered)
                    }
                    
                    HStack {
                        Button("-500") {
                            adjustSalary(-500)
                        }
                        .buttonStyle(.bordered)
                        
                        Button("-100") {
                            adjustSalary(-100)
                        }
                        .buttonStyle(.bordered)
                        
                        Spacer()
                        
                        Button("+100") {
                            adjustSalary(100)
                        }
                        .buttonStyle(.bordered)
                        
                        Button("+500") {
                            adjustSalary(500)
                        }
                        .buttonStyle(.bordered)
                    }
                }
            }
        }
        .navigationTitle("月薪设置")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            selectedSalary = appState.userSettings.monthlySalary
            customSalaryText = String(Int(selectedSalary))
        }
        .onChange(of: selectedSalary) {
            if !useCustomInput {
                updateSalary()
            }
        }
    }
    
    private func adjustSalary(_ amount: Double) {
        selectedSalary = max(1000, min(999999, selectedSalary + amount))
        customSalaryText = String(Int(selectedSalary))
        updateSalary()
    }
    
    private func validateAndUpdateCustomSalary() {
        guard let salary = Double(customSalaryText.trimmingCharacters(in: .whitespacesAndNewlines)),
              salary >= 1000, salary <= 999999 else {
            showingInvalidInput = true
            return
        }
        
        showingInvalidInput = false
        selectedSalary = salary
        updateSalary()
    }
    
    private func updateSalary() {
        var settings = appState.userSettings
        settings.monthlySalary = selectedSalary
        appState.updateUserSettings(settings)
    }
}

/// 工作日设置视图
struct WorkDaysSettingView: View {
    @EnvironmentObject var appState: AppState
    @State private var selectedDays: Int
    @State private var customDaysText: String = ""
    @State private var useCustomInput: Bool = false
    @State private var showingInvalidInput: Bool = false
    
    private let daysOptions = Array(15...31)
    
    init() {
        self._selectedDays = State(initialValue: 22)
    }
    
    var body: some View {
        List {
            Section("输入方式") {
                Picker("输入方式", selection: $useCustomInput) {
                    Text("预设选项").tag(false)
                    Text("自定义输入").tag(true)
                }
                .pickerStyle(.navigationLink)
            }
            
            if useCustomInput {
                Section("自定义天数输入") {
                    VStack(alignment: .leading, spacing: 8) {
                        TextField("输入工作天数", text: $customDaysText)
                            .onSubmit {
                                validateAndUpdateCustomDays()
                            }
                        
                        if showingInvalidInput {
                            Text("请输入有效的天数（1-31）")
                                .font(.caption)
                                .foregroundColor(.red)
                        }
                        
                        HStack {
                            Text("范围：1 - 31天")
                            Spacer()
                        }
                        .font(.caption)
                        .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Button("确认输入") {
                            validateAndUpdateCustomDays()
                        }
                        .buttonStyle(.borderedProminent)
                        .disabled(customDaysText.isEmpty)
                        
                        Spacer()
                        
                        Button("清空") {
                            customDaysText = ""
                            showingInvalidInput = false
                        }
                        .buttonStyle(.bordered)
                    }
                }
            } else {
                Section("选择预设工作天数") {
                    Picker("工作天数", selection: $selectedDays) {
                        ForEach(daysOptions, id: \.self) { days in
                            Text("\(days)天")
                                .tag(days)
                        }
                    }
                    .pickerStyle(.wheel)
                }
            }
            
            Section("快速调整") {
                HStack {
                    Text("当前设置")
                    Spacer()
                    Text("\(selectedDays)天")
                        .foregroundColor(.blue)
                        .fontWeight(.semibold)
                }
                
                HStack {
                    Button("-5") {
                        adjustDays(-5)
                    }
                    .buttonStyle(.bordered)
                    
                    Button("-1") {
                        adjustDays(-1)
                    }
                    .buttonStyle(.bordered)
                    
                    Spacer()
                    
                    Button("+1") {
                        adjustDays(1)
                    }
                    .buttonStyle(.bordered)
                    
                    Button("+5") {
                        adjustDays(5)
                    }
                    .buttonStyle(.bordered)
                }
            }
            
            Section("计算预览") {
                VStack(alignment: .leading, spacing: 4) {
                    Text("每月工作：\(selectedDays)天")
                    Text("每周平均：\(Double(selectedDays) / 4.33, specifier: "%.1f")天")
                    Text("日薪：¥\(Int(appState.userSettings.monthlySalary / Double(selectedDays)))")
                }
                .font(.caption)
                .foregroundColor(.secondary)
            }
        }
        .navigationTitle("工作日设置")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            selectedDays = appState.userSettings.workDaysPerMonth
            customDaysText = String(selectedDays)
        }
        .onChange(of: selectedDays) {
            if !useCustomInput {
                updateWorkDays()
            }
        }
    }
    
    private func adjustDays(_ amount: Int) {
        selectedDays = max(1, min(31, selectedDays + amount))
        customDaysText = String(selectedDays)
        updateWorkDays()
    }
    
    private func validateAndUpdateCustomDays() {
        guard let days = Int(customDaysText.trimmingCharacters(in: .whitespacesAndNewlines)),
              days >= 1, days <= 31 else {
            showingInvalidInput = true
            return
        }
        
        showingInvalidInput = false
        selectedDays = days
        updateWorkDays()
    }
    
    private func updateWorkDays() {
        var settings = appState.userSettings
        settings.workDaysPerMonth = selectedDays
        appState.updateUserSettings(settings)
    }
}

/// 工作时长设置视图
struct WorkHoursSettingView: View {
    @EnvironmentObject var appState: AppState
    @State private var selectedHours: Int
    @State private var customHoursText: String = ""
    @State private var useCustomInput: Bool = false
    @State private var showingInvalidInput: Bool = false
    
    private let hoursOptions = Array(4...16)
    
    init() {
        self._selectedHours = State(initialValue: 8)
    }
    
    var body: some View {
        List {
            Section("输入方式") {
                Picker("输入方式", selection: $useCustomInput) {
                    Text("预设选项").tag(false)
                    Text("自定义输入").tag(true)
                }
                .pickerStyle(.navigationLink)
            }
            
            if useCustomInput {
                Section("自定义小时数输入") {
                    VStack(alignment: .leading, spacing: 8) {
                        TextField("输入工作小时数", text: $customHoursText)
                            .onSubmit {
                                validateAndUpdateCustomHours()
                            }
                        
                        if showingInvalidInput {
                            Text("请输入有效的小时数（1-24）")
                                .font(.caption)
                                .foregroundColor(.red)
                        }
                        
                        HStack {
                            Text("范围：1 - 24小时")
                            Spacer()
                        }
                        .font(.caption)
                        .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Button("确认输入") {
                            validateAndUpdateCustomHours()
                        }
                        .buttonStyle(.borderedProminent)
                        .disabled(customHoursText.isEmpty)
                        
                        Spacer()
                        
                        Button("清空") {
                            customHoursText = ""
                            showingInvalidInput = false
                        }
                        .buttonStyle(.bordered)
                    }
                }
            } else {
                Section("选择预设工作小时数") {
                    Picker("工作小时", selection: $selectedHours) {
                        ForEach(hoursOptions, id: \.self) { hours in
                            Text("\(hours)小时")
                                .tag(hours)
                        }
                    }
                    .pickerStyle(.wheel)
                    
                    Text("推荐8小时标准工作制")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Section("快速调整") {
                HStack {
                    Text("当前设置")
                    Spacer()
                    Text("\(selectedHours)小时")
                        .foregroundColor(.blue)
                        .fontWeight(.semibold)
                }
                
                VStack(spacing: 6) {
                    HStack {
                        Button("-2h") {
                            adjustHours(-2)
                        }
                        .buttonStyle(.bordered)
                        
                        Button("-1h") {
                            adjustHours(-1)
                        }
                        .buttonStyle(.bordered)
                        
                        Button("-0.5h") {
                            adjustHoursByHalf(-0.5)
                        }
                        .buttonStyle(.bordered)
                        
                        Spacer()
                        
                        Button("+0.5h") {
                            adjustHoursByHalf(0.5)
                        }
                        .buttonStyle(.bordered)
                        
                        Button("+1h") {
                            adjustHours(1)
                        }
                        .buttonStyle(.bordered)
                        
                        Button("+2h") {
                            adjustHours(2)
                        }
                        .buttonStyle(.bordered)
                    }
                    
                    Text("支持半小时精度调整")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                VStack(spacing: 6) {
                    HStack {
                        Button("弹性6h") {
                            setHours(6)
                        }
                        .buttonStyle(.bordered)
                        
                        Button("标准8h") {
                            setHours(8)
                        }
                        .buttonStyle(.bordered)
                        
                        Button("延长9h") {
                            setHours(9)
                        }
                        .buttonStyle(.bordered)
                        
                        Button("加班10h") {
                            setHours(10)
                        }
                        .buttonStyle(.bordered)
                    }
                    
                    HStack {
                        Button("轻松4h") {
                            setHours(4)
                        }
                        .buttonStyle(.bordered)
                        
                        Button("半天7.5h") {
                            setHoursWithHalf(7.5)
                        }
                        .buttonStyle(.bordered)
                        
                        Button("强度11h") {
                            setHours(11)
                        }
                        .buttonStyle(.bordered)
                        
                        Button("奋斗12h") {
                            setHours(12)
                        }
                        .buttonStyle(.bordered)
                    }
                    
                    Text("常用工作时长快捷设置")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            
            Section("计算预览") {
                VStack(alignment: .leading, spacing: 4) {
                    Text("每日工作：\(selectedHours)小时")
                    Text("时薪：¥\(Int(appState.userSettings.monthlySalary / Double(appState.userSettings.workDaysPerMonth) / Double(selectedHours)))")
                    Text("分钟收入：¥\(appState.userSettings.monthlySalary / Double(appState.userSettings.workDaysPerMonth) / Double(selectedHours) / 60, specifier: "%.2f")")
                    
                    if selectedHours > 8 {
                        Text("⚠️ 工作时间较长，注意劳逸结合")
                            .foregroundColor(.orange)
                    } else if selectedHours < 6 {
                        Text("💡 工作时间较短，收入可能偏低")
                            .foregroundColor(.blue)
                    }
                }
                .font(.caption)
                .foregroundColor(.secondary)
            }
        }
        .navigationTitle("工作时长设置")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            selectedHours = appState.userSettings.workHoursPerDay
            customHoursText = String(selectedHours)
        }
        .onChange(of: selectedHours) {
            if !useCustomInput {
                updateWorkHours()
            }
        }
    }
    
    private func adjustHours(_ amount: Int) {
        selectedHours = max(1, min(24, selectedHours + amount))
        customHoursText = String(selectedHours)
        updateWorkHours()
    }
    
    private func adjustHoursByHalf(_ amount: Double) {
        let newHours = Double(selectedHours) + amount
        selectedHours = max(1, min(24, Int(newHours * 2) / 2)) // 保持半小时精度
        customHoursText = newHours.truncatingRemainder(dividingBy: 1) == 0 ? String(Int(newHours)) : String(format: "%.1f", newHours)
        updateWorkHours()
    }
    
    private func setHours(_ hours: Int) {
        selectedHours = hours
        customHoursText = String(hours)
        updateWorkHours()
    }
    
    private func setHoursWithHalf(_ hours: Double) {
        selectedHours = Int(hours)
        customHoursText = String(format: "%.1f", hours)
        updateWorkHours()
    }
    
    private func validateAndUpdateCustomHours() {
        guard let hours = Int(customHoursText.trimmingCharacters(in: .whitespacesAndNewlines)),
              hours >= 1, hours <= 24 else {
            showingInvalidInput = true
            return
        }
        
        showingInvalidInput = false
        selectedHours = hours
        updateWorkHours()
    }
    
    private func updateWorkHours() {
        var settings = appState.userSettings
        settings.workHoursPerDay = selectedHours
        appState.updateUserSettings(settings)
    }
}

/// 加班费率设置视图
struct OvertimeRateSettingView: View {
    @EnvironmentObject var appState: AppState
    @State private var selectedRate: Double
    @State private var customRateText: String = ""
    @State private var useCustomInput: Bool = false
    @State private var showingInvalidInput: Bool = false
    
    private let rateOptions: [Double] = [1.0, 1.2, 1.5, 1.8, 2.0, 2.5, 3.0]
    
    init() {
        self._selectedRate = State(initialValue: 1.5)
    }
    
    var body: some View {
        List {
            Section("输入方式") {
                Picker("输入方式", selection: $useCustomInput) {
                    Text("预设选项").tag(false)
                    Text("自定义输入").tag(true)
                }
                .pickerStyle(.navigationLink)
            }
            
            if useCustomInput {
                Section("自定义费率输入") {
                    VStack(alignment: .leading, spacing: 8) {
                        TextField("输入加班费率倍数", text: $customRateText)
                            .onSubmit {
                                validateAndUpdateCustomRate()
                            }
                        
                        if showingInvalidInput {
                            Text("请输入有效的倍数（1.0-5.0）")
                                .font(.caption)
                                .foregroundColor(.red)
                        }
                        
                        HStack {
                            Text("范围：1.0x - 5.0x")
                            Spacer()
                        }
                        .font(.caption)
                        .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Button("确认输入") {
                            validateAndUpdateCustomRate()
                        }
                        .buttonStyle(.borderedProminent)
                        .disabled(customRateText.isEmpty)
                        
                        Spacer()
                        
                        Button("清空") {
                            customRateText = ""
                            showingInvalidInput = false
                        }
                        .buttonStyle(.bordered)
                    }
                }
            } else {
                Section("选择预设费率") {
                    Picker("费率", selection: $selectedRate) {
                        ForEach(rateOptions, id: \.self) { rate in
                            Text("\(rate, specifier: "%.1f")x")
                                .tag(rate)
                        }
                    }
                    .pickerStyle(.wheel)
                }
            }
            
            Section("快速调整") {
                HStack {
                    Text("当前费率")
                    Spacer()
                    Text("\(selectedRate, specifier: "%.1f")x")
                        .foregroundColor(.blue)
                        .fontWeight(.semibold)
                }
                
                VStack(spacing: 8) {
                    HStack {
                        Button("-0.5") {
                            adjustRate(-0.5)
                        }
                        .buttonStyle(.bordered)
                        
                        Button("-0.1") {
                            adjustRate(-0.1)
                        }
                        .buttonStyle(.bordered)
                        
                        Spacer()
                        
                        Button("+0.1") {
                            adjustRate(0.1)
                        }
                        .buttonStyle(.bordered)
                        
                        Button("+0.5") {
                            adjustRate(0.5)
                        }
                        .buttonStyle(.bordered)
                    }
                    
                    HStack {
                        Button("正常1.0x") {
                            selectedRate = 1.0
                            customRateText = "1.0"
                            updateOvertimeRate()
                        }
                        .buttonStyle(.bordered)
                        
                        Button("标准1.5x") {
                            selectedRate = 1.5
                            customRateText = "1.5"
                            updateOvertimeRate()
                        }
                        .buttonStyle(.bordered)
                        
                        Spacer()
                        
                        Button("双倍2.0x") {
                            selectedRate = 2.0
                            customRateText = "2.0"
                            updateOvertimeRate()
                        }
                        .buttonStyle(.bordered)
                        
                        Button("三倍3.0x") {
                            selectedRate = 3.0
                            customRateText = "3.0"
                            updateOvertimeRate()
                        }
                        .buttonStyle(.bordered)
                    }
                }
            }
            
            Section("计算预览") {
                let baseHourlyRate = appState.userSettings.monthlySalary / Double(appState.userSettings.workDaysPerMonth) / Double(appState.userSettings.workHoursPerDay)
                let overtimeHourlyRate = baseHourlyRate * selectedRate
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("正常时薪：¥\(Int(baseHourlyRate))")
                    Text("加班时薪：¥\(Int(overtimeHourlyRate))")
                    Text("加班收益提升：\((selectedRate - 1) * 100, specifier: "%.0f")%")
                    
                    if selectedRate >= 2.0 {
                        Text("💰 高收益加班费率！")
                            .foregroundColor(.green)
                    } else if selectedRate == 1.0 {
                        Text("💡 无加班费，建议调整")
                            .foregroundColor(.orange)
                    }
                }
                .font(.caption)
            }
        }
        .navigationTitle("加班费率")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            selectedRate = appState.userSettings.overtimeRate
            customRateText = String(format: "%.1f", selectedRate)
        }
        .onChange(of: selectedRate) {
            if !useCustomInput {
                updateOvertimeRate()
            }
        }
    }
    
    private func adjustRate(_ amount: Double) {
        selectedRate = max(1.0, min(5.0, selectedRate + amount))
        customRateText = String(format: "%.1f", selectedRate)
        updateOvertimeRate()
    }
    
    private func validateAndUpdateCustomRate() {
        guard let rate = Double(customRateText.trimmingCharacters(in: .whitespacesAndNewlines)),
              rate >= 1.0, rate <= 5.0 else {
            showingInvalidInput = true
            return
        }
        
        showingInvalidInput = false
        selectedRate = rate
        updateOvertimeRate()
    }
    
    private func updateOvertimeRate() {
        var settings = appState.userSettings
        settings.overtimeRate = selectedRate
        appState.updateUserSettings(settings)
    }
}

/// 工作时间设置视图
struct WorkTimeSettingView: View {
    @EnvironmentObject var appState: AppState
    @State private var workStartHour: Int
    @State private var workEndHour: Int
    
    private let hourOptions = Array(0...23)
    
    init() {
        self._workStartHour = State(initialValue: 9)
        self._workEndHour = State(initialValue: 18)
    }
    
    var body: some View {
        List {
            Section("工作时间") {
                Picker("开始时间", selection: $workStartHour) {
                    ForEach(hourOptions, id: \.self) { hour in
                        Text(String(format: "%02d:00", hour))
                            .tag(hour)
                    }
                }
                .pickerStyle(.wheel)
                
                Picker("结束时间", selection: $workEndHour) {
                    ForEach(hourOptions, id: \.self) { hour in
                        Text(String(format: "%02d:00", hour))
                            .tag(hour)
                    }
                }
                .pickerStyle(.wheel)
            }
            
            Section("时间预览") {
                VStack(alignment: .leading, spacing: 4) {
                    Text("工作时间：\(String(format: "%02d:00", workStartHour)) - \(String(format: "%02d:00", workEndHour))")
                    Text("工作时长：\(workEndHour - workStartHour)小时")
                    if workEndHour <= workStartHour {
                        Text("⚠️ 结束时间应晚于开始时间")
                            .foregroundColor(.red)
                    }
                }
                .font(.caption)
                .foregroundColor(.secondary)
            }
        }
        .navigationTitle("工作时间")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            workStartHour = appState.userSettings.workStartHour
            workEndHour = appState.userSettings.workEndHour
        }
        .onChange(of: workStartHour) {
            updateWorkTime()
        }
        .onChange(of: workEndHour) {
            updateWorkTime()
        }
    }
    
    private func updateWorkTime() {
        // 确保结束时间晚于开始时间
        if workEndHour <= workStartHour {
            workEndHour = min(23, workStartHour + 8)
        }
        
        var settings = appState.userSettings
        settings.workStartHour = workStartHour
        settings.workEndHour = workEndHour
        appState.updateUserSettings(settings)
    }
}

// MARK: - 保留原有的高级设置视图

/// 高级通知设置视图
struct AdvancedNotificationSettingsView: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        Form {
            Section("安静时间") {
                HStack {
                    Text("开始时间")
                    Spacer()
                    Text(String(format: "%02d:00", appState.userSettings.notificationSettings.quietHoursStart))
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Text("结束时间")
                    Spacer()
                    Text(String(format: "%02d:00", appState.userSettings.notificationSettings.quietHoursEnd))
                        .foregroundColor(.secondary)
                }
            }
            
            Section("通知频率控制") {
                HStack {
                    Text("每小时最大通知数")
                    Spacer()
                    Text("\(appState.userSettings.notificationSettings.maxNotificationsPerHour)")
                        .foregroundColor(.secondary)
                }
            }
            
            Section("通知统计") {
                let history = appState.notificationManager.getNotificationHistory()
                let todayCount = history.filter { Calendar.current.isDateInToday($0.timestamp) }.count
                
                HStack {
                    Text("今日通知数")
                    Spacer()
                    Text("\(todayCount)")
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Text("历史记录数")
                    Spacer()
                    Text("\(history.count)")
                        .foregroundColor(.secondary)
                }
                
                NavigationLink("查看通知历史") {
                    NotificationHistoryView()
                        .environmentObject(appState)
                }
            }
            
            Section("通知测试") {
                Button("测试工资阈值通知") {
                    appState.notificationManager.sendSalaryThresholdNotification(
                        amount: 1000,
                        percentage: 50,
                        dailyTarget: 2000
                    )
                }
                
                Button("测试目标完成通知") {
                    appState.notificationManager.sendGoalCompletionNotification(
                        goalName: "测试目标",
                        amount: 5000
                    )
                }
                
                Button("测试工作提醒") {
                    appState.notificationManager.sendWorkReminder(message: "这是一条测试工作提醒 📝")
                }
            }
        }
        .navigationTitle("高级通知设置")
        .navigationBarTitleDisplayMode(.inline)
    }
}

/// 通知历史记录视图
struct NotificationHistoryView: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        List {
            let history = appState.notificationManager.getNotificationHistory()
            
            if history.isEmpty {
                Text("暂无通知记录")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
            } else {
                ForEach(history, id: \.id) { record in
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text(getNotificationTypeTitle(record.type))
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(getNotificationTypeColor(record.type))
                            
                            Spacer()
                            
                            Text(record.timestamp, style: .time)
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        
                        Text(record.content)
                            .font(.caption)
                            .lineLimit(2)
                    }
                    .padding(.vertical, 2)
                }
            }
        }
        .navigationTitle("通知历史")
        .navigationBarTitleDisplayMode(.inline)
    }
    
    private func getNotificationTypeTitle(_ type: NotificationHistoryType) -> String {
        switch type {
        case .salaryThreshold:
            return "工资阈值"
        case .goalCompletion:
            return "目标完成"
        case .milestone:
            return "里程碑"
        case .workReminder:
            return "工作提醒"
        case .restReminder:
            return "休息提醒"
        case .batteryOptimization:
            return "电池优化"
        }
    }
    
    private func getNotificationTypeColor(_ type: NotificationHistoryType) -> Color {
        switch type {
        case .salaryThreshold:
            return .blue
        case .goalCompletion:
            return .green
        case .milestone:
            return .orange
        case .workReminder:
            return .purple
        case .restReminder:
            return .mint
        case .batteryOptimization:
            return .yellow
        }
    }
}

/// 电池优化详情视图
struct BatteryOptimizationDetailView: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        Form {
            Section("电池状态") {
                HStack {
                    Text("当前电量")
                    Spacer()
                    Text("\(Int(appState.batteryOptimizationManager.currentBatteryLevel * 100))%")
                        .foregroundColor(batteryLevelColor)
                }
                
                HStack {
                    Text("低电量模式")
                    Spacer()
                    Text(appState.batteryOptimizationManager.isLowPowerModeEnabled ? "开启" : "关闭")
                        .foregroundColor(appState.batteryOptimizationManager.isLowPowerModeEnabled ? .orange : .secondary)
                }
            }
            
            Section("优化策略") {
                HStack {
                    Text("当前级别")
                    Spacer()
                    Text(optimizationLevelDisplayName)
                        .foregroundColor(optimizationLevelColor)
                }
                
                HStack {
                    Text("时间段优化")
                    Spacer()
                    Text(appState.batteryOptimizationManager.currentTimeOptimization.displayName)
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Text("空闲状态")
                    Spacer()
                    Text(appState.batteryOptimizationManager.isInIdleMode ? "是" : "否")
                        .foregroundColor(appState.batteryOptimizationManager.isInIdleMode ? .orange : .green)
                }
            }
            
            Section("性能设置") {
                HStack {
                    Text("计算间隔")
                    Spacer()
                    Text("\(Int(appState.batteryOptimizationManager.getCalculationInterval()))秒")
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Text("动画帧率")
                    Spacer()
                    Text("\(Int(appState.batteryOptimizationManager.getAnimationFrameRate()))fps")
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Text("动画显示")
                    Spacer()
                    Text(appState.batteryOptimizationManager.shouldShowAnimation() ? "开启" : "关闭")
                        .foregroundColor(appState.batteryOptimizationManager.shouldShowAnimation() ? .green : .red)
                }
                
                HStack {
                    Text("动画暂停")
                    Spacer()
                    Text(appState.batteryOptimizationManager.shouldPauseAnimations() ? "是" : "否")
                        .foregroundColor(appState.batteryOptimizationManager.shouldPauseAnimations() ? .orange : .green)
                }
            }
            
            Section("优化建议") {
                let recommendations = appState.batteryOptimizationManager.getOptimizationRecommendations()
                if recommendations.isEmpty {
                    Text("当前没有优化建议")
                        .foregroundColor(.secondary)
                        .font(.caption)
                } else {
                    ForEach(recommendations, id: \.self) { recommendation in
                        HStack {
                            Image(systemName: "lightbulb.fill")
                                .foregroundColor(.yellow)
                                .font(.caption)
                            Text(recommendation)
                                .font(.caption)
                        }
                    }
                }
            }
            
            Section("操作") {
                Button("立即更新电池状态") {
                    appState.updateBatteryStatus()
                }
                .foregroundColor(.blue)
                
                Button("记录用户交互") {
                    appState.recordUserInteraction()
                }
                .foregroundColor(.green)
                
                Button("测试电池优化系统") {
                    appState.testBatteryOptimization()
                }
                .foregroundColor(.blue)
            }
        }
        .navigationTitle("电池优化")
        .onAppear {
            appState.recordUserInteraction()
        }
    }
    
    private var batteryLevelColor: Color {
        let level = appState.batteryOptimizationManager.currentBatteryLevel
        if level > 0.5 {
            return .green
        } else if level > 0.2 {
            return .orange
        } else {
            return .red
        }
    }
    
    private var optimizationLevelDisplayName: String {
        switch appState.batteryOptimizationManager.currentOptimizationLevel {
        case .normal:
            return "正常"
        case .reduced:
            return "节能"
        case .minimal:
            return "极简"
        }
    }
    
    private var optimizationLevelColor: Color {
        switch appState.batteryOptimizationManager.currentOptimizationLevel {
        case .normal:
            return .green
        case .reduced:
            return .orange
        case .minimal:
            return .red
        }
    }
}