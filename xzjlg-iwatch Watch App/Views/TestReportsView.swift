import SwiftUI

/// 测试报告视图
struct TestReportsView: View {
    @StateObject private var testingManager = TestingManager.shared
    
    var body: some View {
        ScrollView {
            VStack(spacing: 12) {
                // 屏幕适配测试结果
                Section("屏幕适配测试") {
                    ForEach(Array(testingManager.screenAdaptationResults.keys), id: \.self) { screenSize in
                        HStack {
                            Text(screenSize.displayName)
                            Spacer()
                            if let result = testingManager.screenAdaptationResults[screenSize] {
                                Text(result.rawValue)
                                    .foregroundColor(colorForResult(result))
                            }
                        }
                        .padding(.vertical, 2)
                    }
                }
                .padding()
                .background(Color.black.opacity(0.1))
                .cornerRadius(8)
                
                // 测试报告列表
                Section("详细报告") {
                    if testingManager.testReports.isEmpty {
                        Text("暂无测试报告")
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity, alignment: .center)
                    } else {
                        ForEach(testingManager.testReports) { report in
                            TestReportRowView(report: report)
                        }
                    }
                }
            }
            .padding()
        }
        .navigationTitle("测试报告")
    }
    
    private func colorForResult(_ result: TestingResult) -> Color {
        switch result {
        case .passed:
            return .green
        case .warning:
            return .orange
        case .failed:
            return .red
        case .running:
            return .blue
        }
    }
}

/// 测试报告行视图
struct TestReportRowView: View {
    let report: TestReport
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(report.testType)
                    .font(.headline)
                Spacer()
                Text(report.result.rawValue)
                    .foregroundColor(colorForResult(report.result))
                    .font(.caption)
            }
            
            Text("用时: \(report.durationString)")
                .font(.caption)
                .foregroundColor(.secondary)
            
            if !report.issues.isEmpty {
                Text("问题: \(report.issues.count)个")
                    .font(.caption)
                    .foregroundColor(.orange)
            }
        }
        .padding(.vertical, 4)
        .padding(.horizontal, 8)
        .background(Color.black.opacity(0.05))
        .cornerRadius(6)
    }
    
    private func colorForResult(_ result: TestingResult) -> Color {
        switch result {
        case .passed:
            return .green
        case .warning:
            return .orange
        case .failed:
            return .red
        case .running:
            return .blue
        }
    }
}

#Preview {
    TestReportsView()
} 