//
//  xzjlg_iwatchApp.swift
//  xzjlg-iwatch Watch App
//
//  Created by binge on 2025/7/26.
//

import SwiftUI
import ClockKit

@main
struct xzjlg_iwatch_Watch_AppApp: App {
    
    // MARK: - Properties
    
    @StateObject private var appState = AppState()
    
    // MARK: - App Scene
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(appState)
                .onOpenURL { url in
                    handleDeepLink(url)
                }
        }
    }
    
    // MARK: - Deep Link Handling
    
    /// 处理深度链接，包括复杂功能点击
    private func handleDeepLink(_ url: URL) {
        guard url.scheme == "salary-reward-app" else { return }
        
        switch url.host {
        case "complication":
            // 复杂功能点击，直接打开主界面
            // 可以在这里添加特定的导航逻辑
            break
        case "goal":
            // 目标相关的深度链接
            if let goalId = url.pathComponents.last {
                // 导航到特定目标
                handleGoalDeepLink(goalId: goalId)
            }
            break
        default:
            break
        }
    }
    
    /// 处理目标相关的深度链接
    private func handleGoalDeepLink(goalId: String) {
        // 这里可以实现导航到特定目标的逻辑
        // 例如：切换到目标页面并高亮特定目标
    }
}
