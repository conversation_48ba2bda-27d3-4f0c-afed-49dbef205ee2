// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		77507D002E346F1B00AC6074 /* xzjlg-iwatch Watch App.app in Embed Watch Content */ = {isa = PBXBuildFile; fileRef = 77507CFF2E346F1B00AC6074 /* xzjlg-iwatch Watch App.app */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		77507D012E346F1B00AC6074 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 77507CF32E346F1A00AC6074 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 77507CFE2E346F1B00AC6074;
			remoteInfo = "xzjlg-iwatch Watch App";
		};
		77507D122E346F1C00AC6074 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 77507CF32E346F1A00AC6074 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 77507CFE2E346F1B00AC6074;
			remoteInfo = "xzjlg-iwatch Watch App";
		};
		77507D1C2E346F1C00AC6074 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 77507CF32E346F1A00AC6074 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 77507CFE2E346F1B00AC6074;
			remoteInfo = "xzjlg-iwatch Watch App";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		77507D282E346F1C00AC6074 /* Embed Watch Content */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "$(CONTENTS_FOLDER_PATH)/Watch";
			dstSubfolderSpec = 16;
			files = (
				77507D002E346F1B00AC6074 /* xzjlg-iwatch Watch App.app in Embed Watch Content */,
			);
			name = "Embed Watch Content";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		77507CF92E346F1B00AC6074 /* xzjlg-iwatch.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "xzjlg-iwatch.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		77507CFF2E346F1B00AC6074 /* xzjlg-iwatch Watch App.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "xzjlg-iwatch Watch App.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		77507D112E346F1C00AC6074 /* xzjlg-iwatch Watch AppTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "xzjlg-iwatch Watch AppTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		77507D1B2E346F1C00AC6074 /* xzjlg-iwatch Watch AppUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "xzjlg-iwatch Watch AppUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		77507D032E346F1B00AC6074 /* xzjlg-iwatch Watch App */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "xzjlg-iwatch Watch App";
			sourceTree = "<group>";
		};
		77507D142E346F1C00AC6074 /* xzjlg-iwatch Watch AppTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "xzjlg-iwatch Watch AppTests";
			sourceTree = "<group>";
		};
		77507D1E2E346F1C00AC6074 /* xzjlg-iwatch Watch AppUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "xzjlg-iwatch Watch AppUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		77507CFC2E346F1B00AC6074 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		77507D0E2E346F1C00AC6074 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		77507D182E346F1C00AC6074 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		77507CF22E346F1A00AC6074 = {
			isa = PBXGroup;
			children = (
				77507D032E346F1B00AC6074 /* xzjlg-iwatch Watch App */,
				77507D142E346F1C00AC6074 /* xzjlg-iwatch Watch AppTests */,
				77507D1E2E346F1C00AC6074 /* xzjlg-iwatch Watch AppUITests */,
				77507CFA2E346F1B00AC6074 /* Products */,
			);
			sourceTree = "<group>";
		};
		77507CFA2E346F1B00AC6074 /* Products */ = {
			isa = PBXGroup;
			children = (
				77507CF92E346F1B00AC6074 /* xzjlg-iwatch.app */,
				77507CFF2E346F1B00AC6074 /* xzjlg-iwatch Watch App.app */,
				77507D112E346F1C00AC6074 /* xzjlg-iwatch Watch AppTests.xctest */,
				77507D1B2E346F1C00AC6074 /* xzjlg-iwatch Watch AppUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		77507CF82E346F1B00AC6074 /* xzjlg-iwatch */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 77507D292E346F1C00AC6074 /* Build configuration list for PBXNativeTarget "xzjlg-iwatch" */;
			buildPhases = (
				77507CF72E346F1B00AC6074 /* Resources */,
				77507D282E346F1C00AC6074 /* Embed Watch Content */,
			);
			buildRules = (
			);
			dependencies = (
				77507D022E346F1B00AC6074 /* PBXTargetDependency */,
			);
			name = "xzjlg-iwatch";
			packageProductDependencies = (
			);
			productName = "xzjlg-iwatch";
			productReference = 77507CF92E346F1B00AC6074 /* xzjlg-iwatch.app */;
			productType = "com.apple.product-type.application.watchapp2-container";
		};
		77507CFE2E346F1B00AC6074 /* xzjlg-iwatch Watch App */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 77507D252E346F1C00AC6074 /* Build configuration list for PBXNativeTarget "xzjlg-iwatch Watch App" */;
			buildPhases = (
				77507CFB2E346F1B00AC6074 /* Sources */,
				77507CFC2E346F1B00AC6074 /* Frameworks */,
				77507CFD2E346F1B00AC6074 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				77507D032E346F1B00AC6074 /* xzjlg-iwatch Watch App */,
			);
			name = "xzjlg-iwatch Watch App";
			packageProductDependencies = (
			);
			productName = "xzjlg-iwatch Watch App";
			productReference = 77507CFF2E346F1B00AC6074 /* xzjlg-iwatch Watch App.app */;
			productType = "com.apple.product-type.application";
		};
		77507D102E346F1C00AC6074 /* xzjlg-iwatch Watch AppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 77507D2C2E346F1C00AC6074 /* Build configuration list for PBXNativeTarget "xzjlg-iwatch Watch AppTests" */;
			buildPhases = (
				77507D0D2E346F1C00AC6074 /* Sources */,
				77507D0E2E346F1C00AC6074 /* Frameworks */,
				77507D0F2E346F1C00AC6074 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				77507D132E346F1C00AC6074 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				77507D142E346F1C00AC6074 /* xzjlg-iwatch Watch AppTests */,
			);
			name = "xzjlg-iwatch Watch AppTests";
			packageProductDependencies = (
			);
			productName = "xzjlg-iwatch Watch AppTests";
			productReference = 77507D112E346F1C00AC6074 /* xzjlg-iwatch Watch AppTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		77507D1A2E346F1C00AC6074 /* xzjlg-iwatch Watch AppUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 77507D2F2E346F1C00AC6074 /* Build configuration list for PBXNativeTarget "xzjlg-iwatch Watch AppUITests" */;
			buildPhases = (
				77507D172E346F1C00AC6074 /* Sources */,
				77507D182E346F1C00AC6074 /* Frameworks */,
				77507D192E346F1C00AC6074 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				77507D1D2E346F1C00AC6074 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				77507D1E2E346F1C00AC6074 /* xzjlg-iwatch Watch AppUITests */,
			);
			name = "xzjlg-iwatch Watch AppUITests";
			packageProductDependencies = (
			);
			productName = "xzjlg-iwatch Watch AppUITests";
			productReference = 77507D1B2E346F1C00AC6074 /* xzjlg-iwatch Watch AppUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		77507CF32E346F1A00AC6074 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					77507CF82E346F1B00AC6074 = {
						CreatedOnToolsVersion = 16.2;
					};
					77507CFE2E346F1B00AC6074 = {
						CreatedOnToolsVersion = 16.2;
					};
					77507D102E346F1C00AC6074 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 77507CFE2E346F1B00AC6074;
					};
					77507D1A2E346F1C00AC6074 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 77507CFE2E346F1B00AC6074;
					};
				};
			};
			buildConfigurationList = 77507CF62E346F1A00AC6074 /* Build configuration list for PBXProject "xzjlg-iwatch" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 77507CF22E346F1A00AC6074;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 77507CFA2E346F1B00AC6074 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				77507CF82E346F1B00AC6074 /* xzjlg-iwatch */,
				77507CFE2E346F1B00AC6074 /* xzjlg-iwatch Watch App */,
				77507D102E346F1C00AC6074 /* xzjlg-iwatch Watch AppTests */,
				77507D1A2E346F1C00AC6074 /* xzjlg-iwatch Watch AppUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		77507CF72E346F1B00AC6074 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		77507CFD2E346F1B00AC6074 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		77507D0F2E346F1C00AC6074 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		77507D192E346F1C00AC6074 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		77507CFB2E346F1B00AC6074 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		77507D0D2E346F1C00AC6074 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		77507D172E346F1C00AC6074 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		77507D022E346F1B00AC6074 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 77507CFE2E346F1B00AC6074 /* xzjlg-iwatch Watch App */;
			targetProxy = 77507D012E346F1B00AC6074 /* PBXContainerItemProxy */;
		};
		77507D132E346F1C00AC6074 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 77507CFE2E346F1B00AC6074 /* xzjlg-iwatch Watch App */;
			targetProxy = 77507D122E346F1C00AC6074 /* PBXContainerItemProxy */;
		};
		77507D1D2E346F1C00AC6074 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 77507CFE2E346F1B00AC6074 /* xzjlg-iwatch Watch App */;
			targetProxy = 77507D1C2E346F1C00AC6074 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		77507D232E346F1C00AC6074 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		77507D242E346F1C00AC6074 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		77507D262E346F1C00AC6074 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"xzjlg-iwatch Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = L3H7CM8ZUA;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = "xzjlg-iwatch";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKWatchOnly = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.smy.xzjlgwatch.xzjlg-iwatch.watchkitapp";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 11.2;
			};
			name = Debug;
		};
		77507D272E346F1C00AC6074 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"xzjlg-iwatch Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = L3H7CM8ZUA;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = "xzjlg-iwatch";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKWatchOnly = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.smy.xzjlgwatch.xzjlg-iwatch.watchkitapp";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				VALIDATE_PRODUCT = YES;
				WATCHOS_DEPLOYMENT_TARGET = 11.2;
			};
			name = Release;
		};
		77507D2A2E346F1C00AC6074 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = L3H7CM8ZUA;
				INFOPLIST_KEY_CFBundleDisplayName = "xzjlg-iwatch";
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.smy.xzjlgwatch.xzjlg-iwatch";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		77507D2B2E346F1C00AC6074 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = L3H7CM8ZUA;
				INFOPLIST_KEY_CFBundleDisplayName = "xzjlg-iwatch";
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.smy.xzjlgwatch.xzjlg-iwatch";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		77507D2D2E346F1C00AC6074 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = L3H7CM8ZUA;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.smy.xzjlgwatch.xzjlg-iwatch-Watch-AppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/xzjlg-iwatch Watch App.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/xzjlg-iwatch Watch App";
				WATCHOS_DEPLOYMENT_TARGET = 11.2;
			};
			name = Debug;
		};
		77507D2E2E346F1C00AC6074 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = L3H7CM8ZUA;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.smy.xzjlgwatch.xzjlg-iwatch-Watch-AppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/xzjlg-iwatch Watch App.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/xzjlg-iwatch Watch App";
				VALIDATE_PRODUCT = YES;
				WATCHOS_DEPLOYMENT_TARGET = 11.2;
			};
			name = Release;
		};
		77507D302E346F1C00AC6074 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = L3H7CM8ZUA;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.smy.xzjlgwatch.xzjlg-iwatch-Watch-AppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TEST_TARGET_NAME = "xzjlg-iwatch Watch App";
				WATCHOS_DEPLOYMENT_TARGET = 11.2;
			};
			name = Debug;
		};
		77507D312E346F1C00AC6074 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = L3H7CM8ZUA;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.smy.xzjlgwatch.xzjlg-iwatch-Watch-AppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TEST_TARGET_NAME = "xzjlg-iwatch Watch App";
				VALIDATE_PRODUCT = YES;
				WATCHOS_DEPLOYMENT_TARGET = 11.2;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		77507CF62E346F1A00AC6074 /* Build configuration list for PBXProject "xzjlg-iwatch" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				77507D232E346F1C00AC6074 /* Debug */,
				77507D242E346F1C00AC6074 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		77507D252E346F1C00AC6074 /* Build configuration list for PBXNativeTarget "xzjlg-iwatch Watch App" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				77507D262E346F1C00AC6074 /* Debug */,
				77507D272E346F1C00AC6074 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		77507D292E346F1C00AC6074 /* Build configuration list for PBXNativeTarget "xzjlg-iwatch" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				77507D2A2E346F1C00AC6074 /* Debug */,
				77507D2B2E346F1C00AC6074 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		77507D2C2E346F1C00AC6074 /* Build configuration list for PBXNativeTarget "xzjlg-iwatch Watch AppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				77507D2D2E346F1C00AC6074 /* Debug */,
				77507D2E2E346F1C00AC6074 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		77507D2F2E346F1C00AC6074 /* Build configuration list for PBXNativeTarget "xzjlg-iwatch Watch AppUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				77507D302E346F1C00AC6074 /* Debug */,
				77507D312E346F1C00AC6074 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 77507CF32E346F1A00AC6074 /* Project object */;
}
